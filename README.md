# xbit-hypertrader-go

A high-performance trading platform built with Go, featuring dual database architecture for optimal OLTP and OLAP workloads.

## Architecture Overview

This project implements a modern microservices architecture with:

- **ClickHouse**: Columnar database for analytics and real-time data processing (OLAP)
- **GraphQL API**: Modern API layer with type-safe queries
- **NATS JetStream**: Message queue for asynchronous processing
- **Redis**: High-performance caching and real-time data storage
- **Background Jobs**: Scheduled tasks for data processing and maintenance

## Database Configuration

### ClickHouse (OLAP)
- **Host**: ***********:8123
- **Purpose**: Analytics, market data, real-time aggregations
- **Features**: Columnar storage, high-performance analytics, real-time queries

## Quick Start

### Prerequisites
- Go 1.21+
- Access to ClickHouse instance
- NATS server (optional, for message queue features)
- Redis server (for caching and real-time data)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/xbit-dex/xbit-hypertrader-go.git
cd xbit-hypertrader-go
```

2. Install dependencies:
```bash
make deps
```

3. Create database tables:
```bash
make db-create-tables
```

4. Run the application:
```bash
make run-local
```

### Development

Start the development server with hot reload:
```bash
make dev
```

Run tests:
```bash
make test
```

Build all services:
```bash
make build-all
```

Run individual services:
```bash
make run-scheduler           # Run scheduler
make run-websocket-worker    # Run WebSocket worker
make run-nats-consumer-worker # Run NATS consumer worker
```

## Services

### GraphQL Server
- **Port**: 8080
- **Endpoint**: `/api/dex-hypertrader/graphql`
- **Health Check**: `/health`

## API Documentation

This system exposes both RESTful and GraphQL APIs. Unless otherwise noted, the server listens on port `8080` and uses the router prefix configured at `system.router-prefix` (default: `/api/dex-hypertrader`).

### RESTful API

- **Base URL**: `http://localhost:8080`
- **Router Prefix**: `/api/dex-hypertrader`
- **Endpoint Structure**: `http://<host>:<port>/api/dex-hypertrader/<resource>`

Available endpoints:

- `GET /health` — service health status
- `GET /api/dex-hypertrader/ping` — simple liveness check
- `POST /api/dex-hypertrader/info` — multi-purpose information endpoint (various request types)

#### Authentication

- No authentication is required by default for the above endpoints.
- You may include `Authorization: Bearer <token>`; it is currently ignored by the server but can be enforced via gateway/reverse-proxy in production.

#### Request Types for POST /info

The `/info` endpoint accepts a JSON body with a `type` field and optional parameters depending on the request type. Common types:

- `candleSnapshot` — fetch OHLCV data from the system
  - Body fields: `req.coin`, `req.interval`, `req.startTime`, `req.endTime`
- `meta`, `spotMeta`, `metaAndAssetCtxs` — general market metadata
- `openOrders`, `frontendOpenOrders` — user orders (requires `user`)
- `activeAssetData` — details for a user and coin (requires `user`, `coin`)
- `fundingHistory` — funding data for a coin/time range (requires `coin`, `startTime`, `endTime`)

Request/response examples:

- Candle snapshot (REST):

```bash
curl -X POST \
  http://localhost:8080/api/dex-hypertrader/info \
  -H 'Content-Type: application/json' \
  -d '{
    "type": "candleSnapshot",
    "req": {
      "coin": "BTC",
      "interval": "1m",
      "startTime": 1697462400000,
      "endTime": 1697466000000
    }
  }'
```

- Meta (REST):

```bash
curl -X POST \
  http://localhost:8080/api/dex-hypertrader/info \
  -H 'Content-Type: application/json' \
  -d '{ "type": "meta" }'
```

Typical success response shape:

```json
{
  "success": true,
  "data": { /* type-specific payload */ }
}
```

Error response example (bad request):

```json
{
  "success": false,
  "message": "Invalid request format: ..."
}
```

#### Common REST use cases

- Health monitoring with `/health`
- Liveness checks with `/ping`
- On-demand candle snapshots with `/info` (`type: candleSnapshot`)
- Pulling market metadata via `/info` (`type: meta`, `spotMeta`, etc.)

### GraphQL API

- **Endpoint**: `POST /api/dex-hypertrader/graphql`
- **Playground/Explorer**: `GET /api/dex-hypertrader/graphql/` (enabled in non-production environments)

Schema overview:

- The schema and resolvers live in `internal/controller/graphql/`. For OHLCV, see the `getOHLC` query. A detailed guide is available in `docs/API_GETOHLCV.md`.

Authentication:

- No authentication is required by default. You may include `Authorization: Bearer <token>` for future compatibility.

Example queries:

- Fetch OHLCV/kline data:

```graphql
query GetOHLC {
  getOHLC(
    input: {
      symbol: "BTC-USD"
      interval: ONE_MINUTE
      timestamp: 1697462400000
      isForward: true
      limit: 100
    }
  ) {
    symbol
    data {
      timestamp
      open
      high
      low
      close
      volume
    }
  }
}
```

- Query trading/market data (examples depend on available resolvers). Refer to `internal/controller/graphql/schema.graphqls` and generated models in `internal/controller/graphql/gql_model/` for available fields and types.

Using the Playground:

- Run the server in a non-production environment (e.g., `APP_ENV=local`) and open `http://localhost:8080/api/dex-hypertrader/graphql/` in a browser.

### API Usage Examples

#### curl

- Health check:

```bash
curl http://localhost:8080/health
```

- GraphQL request:

```bash
curl -X POST http://localhost:8080/api/dex-hypertrader/graphql \
  -H 'Content-Type: application/json' \
  -d '{
    "query": "query GetOHLC($input: GetOHLCInput!) { getOHLC(input: $input) { symbol data { timestamp open high low close volume } } }",
    "variables": {
      "input": {
        "symbol": "BTC-USD",
        "interval": "ONE_MINUTE",
        "timestamp": 1697462400000,
        "isForward": true,
        "limit": 100
      }
    }
  }'
```

#### JavaScript (fetch)

```javascript
// REST - candle snapshot
await fetch('http://localhost:8080/api/dex-hypertrader/info', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'candleSnapshot',
    req: { coin: 'BTC', interval: '1m', startTime: 1697462400000, endTime: 1697466000000 }
  })
}).then(r => r.json());

// GraphQL - getOHLC
await fetch('http://localhost:8080/api/dex-hypertrader/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    query: `query GetOHLC($input: GetOHLCInput!) {
      getOHLC(input: $input) { symbol data { timestamp open high low close volume } }
    }`,
    variables: {
      input: { symbol: 'BTC-USD', interval: 'ONE_MINUTE', timestamp: 1697462400000, isForward: true, limit: 100 }
    }
  })
}).then(r => r.json());
```

#### Python (requests)

```python
import requests

# REST - candle snapshot
resp = requests.post(
    'http://localhost:8080/api/dex-hypertrader/info',
    json={
        'type': 'candleSnapshot',
        'req': {
            'coin': 'BTC',
            'interval': '1m',
            'startTime': 1697462400000,
            'endTime': 1697466000000,
        }
    }
)
resp.raise_for_status()
print(resp.json())

# GraphQL - getOHLC
resp = requests.post(
    'http://localhost:8080/api/dex-hypertrader/graphql',
    json={
        'query': 'query GetOHLC($input: GetOHLCInput!) { getOHLC(input: $input) { symbol data { timestamp open high low close volume } } }',
        'variables': {
            'input': {
                'symbol': 'BTC-USD',
                'interval': 'ONE_MINUTE',
                'timestamp': 1697462400000,
                'isForward': True,
                'limit': 100,
            }
        }
    }
)
resp.raise_for_status()
print(resp.json())
```

### Error Handling

- Validation/format errors (REST) return `400` with `{ success: false, message: string }`.
- Rate limit exceeded returns `429` with headers: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`.
- GraphQL errors are returned in the standard `errors` array in the response body.

Example `429 Too Many Requests` body:

```json
{
  "success": false,
  "message": "Request too frequent, please try again later",
  "retry_after": **********
}
```

### Rate Limiting

- Default: 100 requests per minute per client IP (configurable via `protection.rateLimit` in `config.yaml`).
- Paths skipped by default: `/health`, and the configured ping path.
- Caching and circuit breaker are enabled by default and can be tuned under `protection.cache` and `protection.circuitBreaker`.

### Notes

- GraphQL Playground is enabled when `APP_ENV` is one of: `local`, `dev`, `development`, `unstable`, `staging`.
- Router and GraphQL prefixes are configurable in `config.yaml` under `system.router-prefix` and `system.graphql-prefix`.
- See detailed OHLCV query docs in `docs/API_GETOHLCV.md`.

### Scheduler
Background job scheduler for:
- Market data synchronization
- Analytics aggregation
- Data cleanup tasks

### Worker
Message queue worker for:
- Trade processing
- Market data ingestion
- Real-time analytics

## Environment Configuration

The application uses environment-based configuration. Copy and modify the environment files:

- `env/local.env` - Local development
- `env/docker.env` - Docker deployment
- `env/staging.env` - Staging environment
- `env/prod.env` - Production environment

## Docker Deployment

Build and run with Docker Compose:
```bash
make docker-build
make docker-run
```

## Project Structure

```
├── cmd/                    # Application entry points
│   ├── graphql/           # GraphQL server
│   ├── scheduler/         # Background job scheduler
│   ├── worker_example/    # Message queue worker
│   └── cli/              # CLI tools
├── config/               # Configuration structures
├── internal/             # Private application code
│   ├── initializer/      # Database and service initializers
│   ├── model/           # Database models
│   ├── nats/            # Message queue client
│   ├── task/            # Background task scheduler
│   └── test/            # Test utilities
├── env/                 # Environment configuration files
├── scripts/             # Build and deployment scripts
└── migrations/          # Database migrations
```

## Testing

Run the test suite:
```bash
make test
```

Run tests with coverage:
```bash
make test-coverage
```

## Contributing

1. Follow the existing code structure and patterns
2. Write tests for new functionality
3. Update documentation as needed
4. Use the provided Makefile targets for common tasks

## License

[Add your license information here]