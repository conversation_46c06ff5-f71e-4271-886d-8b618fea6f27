# JWT Configuration
jwt:
  signing-key: {{ index . "JWT_SECRET" | default "your-secret-key" }}
  expires-time: {{ index . "JWT_EXPIRES_TIME" | default "7d" }}
  buffer-time: {{ index . "JWT_BUFFER_TIME" | default "1d" }}
  issuer: {{ index . "JWT_ISSUER" | default "dex-hypertrader" }}

# System Configuration
system:
  env: {{ index . "APP_ENV" | default "local" }}
  addr: {{ index . "SERVER_PORT" | default "8080" }}
  router-prefix: "/api/dex-hypertrader"
  graphql-prefix: "/api/dex-hypertrader/graphql"
  admin-graphql-prefix: "/api/dex-hypertrader/admin/graphql"

# TiDB Configuration
tidb:
  host: {{ index . "TIDB_HOST" | default "127.0.0.1" }}
  port: {{ index . "TIDB_PORT" | default "4000" }}
  config: {{ index . "TIDB_CONFIG" | default "charset=utf8mb4&parseTime=True&loc=Local" }}
  db-name: {{ index . "TIDB_DB" | default "xbit_hypertrader" }}
  username: {{ index . "TIDB_USER" | default "root" }}
  password: {{ index . "TIDB_PASS" | default "" }}
  max-idle-conns: {{ index . "TIDB_MAX_IDLE_CONNS" | default "10" }}
  max-open-conns: {{ index . "TIDB_MAX_OPEN_CONNS" | default "100" }}
  max-lifetime: {{ index . "TIDB_MAX_LIFETIME" | default "1h" }}

# ClickHouse Configuration (OLAP - Analytics workloads)
clickhouse:
  host: {{ index . "CLICKHOUSE_HOST" | default "127.0.0.1" }}
  port: {{ index . "CLICKHOUSE_PORT" | default "8123" }}
  database: {{ index . "CLICKHOUSE_DB" | default "default" }}
  username: {{ index . "CLICKHOUSE_USER" | default "default" }}
  password: {{ index . "CLICKHOUSE_PASS" | default "" }}
  max-idle-conns: {{ index . "CLICKHOUSE_MAX_IDLE_CONNS" | default "5" }}
  max-open-conns: {{ index . "CLICKHOUSE_MAX_OPEN_CONNS" | default "50" }}
  max-lifetime: {{ index . "CLICKHOUSE_MAX_LIFETIME" | default "1h" }}
  secure: {{ index . "CLICKHOUSE_SECURE" | default "false" }}
  skip-verify: {{ index . "CLICKHOUSE_SKIP_VERIFY" | default "true" }}

# NATS Configuration
nats:
  url: {{ index . "NATS_URL" | default "nats://127.0.0.1:4222" }}
  user: {{ index . "NATS_USER" | default "" }}
  pass: {{ index . "NATS_PASS" | default "" }}
  use-tls: {{ index . "NATS_USE_TLS" | default "false" }}
  token: {{ index . "NATS_TOKEN" | default "" }}

# Redis Configuration (Caching and Real-time Data)
redis:
  host: {{ index . "REDIS_HOST" | default "127.0.0.1" }}
  port: {{ index . "REDIS_PORT" | default "6379" }}
  password: {{ index . "REDIS_PASSWORD" | default "" }}
  db: {{ index . "REDIS_DB" | default "0" }}
  max-idle-conns: {{ index . "REDIS_MAX_IDLE_CONNS" | default "10" }}
  max-open-conns: {{ index . "REDIS_MAX_OPEN_CONNS" | default "100" }}
  max-lifetime: {{ index . "REDIS_MAX_LIFETIME" | default "1h" }}

# WebSocket Configuration (Real-time Trading Data)
websocket:
  host: {{ index . "WEBSOCKET_HOST" | default "127.0.0.1" }}
  port: {{ index . "WEBSOCKET_PORT" | default "8000" }}
  path: {{ index . "WEBSOCKET_PATH" | default "/ws" }}
  reconnect-interval: {{ index . "WEBSOCKET_RECONNECT_INTERVAL" | default "5s" }}
  ping-interval: {{ index . "WEBSOCKET_PING_INTERVAL" | default "30s" }}
  pong-timeout: {{ index . "WEBSOCKET_PONG_TIMEOUT" | default "10s" }}

# Protection Middleware Configuration
protection:
  # Rate Limiting
  rateLimit:
    enabled: {{ index . "RATE_LIMIT_ENABLED" | default "true" }}
    requests: {{ index . "RATE_LIMIT_REQUESTS" | default "100" }} # Number of requests per time window
    window: {{ index . "RATE_LIMIT_WINDOW" | default "1m" }} # Time window
    skipPaths: ["/health", "/api/xbit-hypertrader/ping"] # Paths to skip rate limiting

  # Caching
  cache:
    enabled: {{ if eq (index . "CACHE_ENABLED") "false" }}false{{ else }}true{{ end }}
    ttl: {{ index . "CACHE_TTL" | default "30s" }} # Cache time-to-live
    maxSize: {{ index . "CACHE_MAX_SIZE" | default "1000" }} # Maximum number of cache entries
    skipPaths: ["/health"] # Paths to skip caching
    skipMethods: ["PUT", "DELETE", "PATCH"] # HTTP methods to skip caching

  # Circuit Breaker
  circuitBreaker:
    enabled: {{ index . "CIRCUIT_BREAKER_ENABLED" | default "true" }}
    maxMemoryUsage: {{ index . "CIRCUIT_BREAKER_MAX_MEMORY" | default "0.8" }} # Maximum memory usage
    maxCPUUsage: {{ index . "CIRCUIT_BREAKER_MAX_CPU" | default "0.8" }} # Maximum CPU usage
    maxResponseTime: {{ index . "CIRCUIT_BREAKER_MAX_RESPONSE_TIME" | default "5s" }} # Maximum response time
    checkInterval: {{ index . "CIRCUIT_BREAKER_CHECK_INTERVAL" | default "10s" }} # Check interval
    recoveryThreshold: {{ index . "CIRCUIT_BREAKER_RECOVERY_THRESHOLD" | default "5" }} # Recovery threshold

# InfoAPI Configuration (External API calls)
info-api:
  url: {{ index . "INFO_API_URL" | default "https://api.hyperliquid.xyz/info" }}
  timeout: {{ index . "INFO_API_TIMEOUT" | default "30s" }}

# NodeAPI Configuration (Node API calls)
node-api:
  url: {{ index . "NODE_API_URL" | default "http://**************:3001/info" }}
  timeout: {{ index . "NODE_API_TIMEOUT" | default "30s" }}

# Scheduled Tasks
cron-tasks:
  - id: "persist_klines"
    cron: "2 * * * * *"    # Every minute
  - id: "user_fills_sync"
    cron: "*/5 * * * * *"  # Every 5 seconds
  - id: "historical_orders_sync"
    cron: "*/30 * * * * *"  # Every 30 seconds
  - id: "tidb_to_clickhouse_sync"
    cron: "*/10 * * * * *"  # Every 10 seconds
  - id: user_non_funding_sync
    cron: "0 */1 * * * *"
    
# OHLCV Backfill Configuration
backfill:
  enabled: {{ index . "BACKFILL_ENABLED" | default "true" }}
  timeout: {{ index . "BACKFILL_TIMEOUT" | default "300s" }}

  # Timing configuration for backfill operations
  timing:
    # API request timing
    retry-delay: {{ index . "BACKFILL_RETRY_DELAY" | default "200ms" }}                    # Delay between retry attempts
    rate-limit-delay: {{ index . "BACKFILL_RATE_LIMIT_DELAY" | default "200ms" }}          # Delay between API requests for rate limiting
    inter-symbol-delay: {{ index . "BACKFILL_INTER_SYMBOL_DELAY" | default "50ms" }}    # Delay between starting to process different symbols

    # HTTP client timeouts
    base-timeout: {{ index . "BACKFILL_BASE_TIMEOUT" | default "120s" }}                # Base timeout for HTTP requests
    progressive-timeout-increment: {{ index . "BACKFILL_PROGRESSIVE_TIMEOUT_INCREMENT" | default "60s" }} # Timeout increment per retry
    max-timeout: {{ index . "BACKFILL_MAX_TIMEOUT" | default "300s" }}                  # Maximum timeout for HTTP requests
    tls-handshake-timeout: {{ index . "BACKFILL_TLS_HANDSHAKE_TIMEOUT" | default "30s" }} # TLS handshake timeout
    response-header-timeout: {{ index . "BACKFILL_RESPONSE_HEADER_TIMEOUT" | default "60s" }} # Response header timeout
    idle-conn-timeout: {{ index . "BACKFILL_IDLE_CONN_TIMEOUT" | default "120s" }}      # Idle connection timeout

    # Database operation timeouts
    database-timeout: {{ index . "BACKFILL_DATABASE_TIMEOUT" | default "2m" }}          # Timeout for database operations

    # Caching
    symbols-cache-ttl: {{ index . "BACKFILL_SYMBOLS_CACHE_TTL" | default "5m" }}        # TTL for symbols cache

  # Proxy configuration for rate limit avoidance
  proxies:
    enabled: {{ index . "BACKFILL_PROXIES_ENABLED" | default "true" }}
    concurrency: {{ index . "BACKFILL_PROXIES_CONCURRENCY" | default "10" }}  # Match proxy count for optimal performance
    rotation: {{ index . "BACKFILL_PROXIES_ROTATION" | default "round-robin" }}  # round-robin or random

    # Health check configuration
    health-check:
      enabled: {{ index . "BACKFILL_PROXY_HEALTH_CHECK_ENABLED" | default "true" }}
      interval: {{ index . "BACKFILL_PROXY_HEALTH_CHECK_INTERVAL" | default "5m" }}
      timeout: {{ index . "BACKFILL_PROXY_HEALTH_CHECK_TIMEOUT" | default "10s" }}
      url: {{ index . "BACKFILL_PROXY_HEALTH_CHECK_URL" | default "https://httpbin.org/ip" }}

    # List of HTTP proxies for rate limit avoidance
    urls:
      - "**********************************************"
      - "********************************************"
      - "*********************************************"
      - "*********************************************"
      - "*********************************************"
      - "**********************************************"
      - "**********************************************"
      - "*********************************************"
      - "**********************************************"
      - "**********************************************"

  jobs:
    # 1-minute backfill job
    # - id: "backfill_1m"
    #   timeframe: "1m"
    #   candle-count: {{ index . "BACKFILL_1M_CANDLE_COUNT" | default "5000" }}
    #   cron: {{ index . "BACKFILL_1M_CRON" | default "\"0 50 17 * * *\"" }}
    #   enabled: {{ index . "BACKFILL_1M_ENABLED" | default "true" }}

    # # 3-minute backfill job
    # - id: "backfill_3m"
    #   timeframe: "3m"
    #   candle-count: {{ index . "BACKFILL_3M_CANDLE_COUNT" | default "10" }}
    #   cron: {{ index . "BACKFILL_3M_CRON" | default "\"0 */6 * * * *\"" }}  # Every 6 minutes
    #   enabled: {{ index . "BACKFILL_3M_ENABLED" | default "true" }}

    # 5-minute backfill job
    # - id: "backfill_5m"
    #   timeframe: "5m"
    #   candle-count: {{ index . "BACKFILL_5M_CANDLE_COUNT" | default "5000" }}
    #   cron: {{ index . "BACKFILL_1M_CRON" | default "\"00 55 17 * * *\"" }}
    #   enabled: {{ index . "BACKFILL_5M_ENABLED" | default "true" }}

    # 15-minute backfill job
    # - id: "backfill_15m"
    #   timeframe: "15m"
    #   candle-count: {{ index . "BACKFILL_15M_CANDLE_COUNT" | default "5000" }}
    #   cron: {{ index . "BACKFILL_1M_CRON" | default "\"00 12 18 * * *\"" }}
    #   enabled: {{ index . "BACKFILL_15M_ENABLED" | default "true" }}

    # # 30-minute backfill job
    # - id: "backfill_30m"
    #   timeframe: "30m"
    #   candle-count: {{ index . "BACKFILL_30M_CANDLE_COUNT" | default "10" }}
    #   cron: {{ index . "BACKFILL_30M_CRON" | default "\"0 0 * * * *\"" }}  # Every hour
    #   enabled: {{ index . "BACKFILL_30M_ENABLED" | default "true" }}

    # 1-hour backfill job
    # - id: "backfill_1h"
    #   timeframe: "1h"
    #   candle-count: {{ index . "BACKFILL_1H_CANDLE_COUNT" | default "5000" }}
    #   cron: {{ index . "BACKFILL_1M_CRON" | default "\"30 30 18 * * *\"" }}
    #   enabled: {{ index . "BACKFILL_1H_ENABLED" | default "true" }}

    # 2-hour backfill job
    - id: "backfill_2h"
      timeframe: "2h"
      candle-count: {{ index . "BACKFILL_2H_CANDLE_COUNT" | default "5000" }}
      cron: {{ index . "BACKFILL_1M_CRON" | default "\"00 06 11 * * *\"" }}
      enabled: {{ index . "BACKFILL_2H_ENABLED" | default "true" }}

    # # 4-hour backfill job
    # - id: "backfill_4h"
    #   timeframe: "4h"
    #   candle-count: {{ index . "BACKFILL_4H_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_4H_CRON" | default "\"0 0 */6 * * *\"" }}  # Every 6 hours
    #   enabled: {{ index . "BACKFILL_4H_ENABLED" | default "true" }}


    # # 8-hour backfill job
    # - id: "backfill_8h"
    #   timeframe: "8h"
    #   candle-count: {{ index . "BACKFILL_8H_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_8H_CRON" | default "\"0 0 */12 * * *\"" }}  # Every 12 hours
    #   enabled: {{ index . "BACKFILL_8H_ENABLED" | default "true" }}

    # # 12-hour backfill job
    # - id: "backfill_12h"
    #   timeframe: "12h"
    #   candle-count: {{ index . "BACKFILL_12H_CANDLE_COUNT" | default "5000" }}
    #   cron: {{ index . "BACKFILL_1M_CRON" | default "\"00 31 19 * * *\"" }}
    #   enabled: {{ index . "BACKFILL_12H_ENABLED" | default "true" }}

    # # 1-day backfill job
    # - id: "backfill_1d"
    #   timeframe: "1d"
    #   candle-count: {{ index . "BACKFILL_1D_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_1D_CRON" | default "\"0 0 2 * * *\"" }}  # Daily at 2 AM
    #   enabled: {{ index . "BACKFILL_1D_ENABLED" | default "true" }}

    # # 3-day backfill job
    # - id: "backfill_3d"
    #   timeframe: "3d"
    #   candle-count: {{ index . "BACKFILL_3D_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_3D_CRON" | default "\"0 0 4 * * 0\"" }}  # Weekly on Sunday at 4 AM
    #   enabled: {{ index . "BACKFILL_3D_ENABLED" | default "true" }}

    # # 1-week backfill job
    # - id: "backfill_1w"
    #   timeframe: "1w"
    #   candle-count: {{ index . "BACKFILL_1W_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_1W_CRON" | default "\"0 0 6 * * 0\"" }}  # Weekly on Sunday at 6 AM
    #   enabled: {{ index . "BACKFILL_1W_ENABLED" | default "true" }}

    # # 1-month backfill job
    # - id: "backfill_1mo"
    #   timeframe: "1mo"
    #   candle-count: {{ index . "BACKFILL_1MO_CANDLE_COUNT" | default "5" }}
    #   cron: {{ index . "BACKFILL_1MO_CRON" | default "\"0 0 8 1 * *\"" }}  # Monthly on 1st at 8 AM
    #   enabled: {{ index . "BACKFILL_1MO_ENABLED" | default "true" }}
