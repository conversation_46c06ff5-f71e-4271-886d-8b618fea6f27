FROM golang:1.24-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git make

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Generate GraphQL code before building
RUN make gqlgen


# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/dex-hypertrader cmd/graphql/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/dex-hypertrader-scheduler cmd/scheduler/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/dex-hypertrader-websocket-worker cmd/worker_websocket/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/dex-hypertrader-nats-consumer-worker cmd/worker_nats_consumer/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/dex-hypertrader-sync-user cmd/worker_sync_user/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates
RUN apk --no-cache add ca-certificates curl

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main/ .
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/entrypoint.sh .
COPY --from=builder /app/migrations/ migrations/
COPY env/ env/

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Expose port
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["./entrypoint.sh"]

# Default command
CMD ["./dex-hypertrader"]
