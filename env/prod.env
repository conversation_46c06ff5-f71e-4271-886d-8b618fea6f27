# Application Configuration
APP_ENV=production
APP_NAME=dex-hypertrader
SERVER_PORT=8080

# TiDB Configuration
# SECURITY: All passwords must be changed before production deployment
TIDB_HOST=prod-tidb.example.com
TIDB_PORT=4000
TIDB_USER=prod_user
TIDB_PASS=CHANGE_ME_PRODUCTION_PASSWORD_REQUIRED
TIDB_DB=xbit_hypertrader
TIDB_CONFIG=charset=utf8mb4&parseTime=True&loc=Local&tls=true
TIDB_MAX_IDLE_CONNS=20
TIDB_MAX_OPEN_CONNS=200
TIDB_MAX_LIFETIME=2h

# ClickHouse Configuration (OLAP - Analytics workloads)
CLICKHOUSE_HOST=
CLICKHOUSE_PORT=
CLICKHOUSE_USER=
CLICKHOUSE_PASS=
CLICKHOUSE_DB=default

# JWT Configuration
JWT_SECRET=
JWT_EXPIRES_TIME=1h
JWT_ISSUER=dex-hypertrader-unstable


# NATS Configuration
NATS_URL=
NATS_USER=
NATS_PASS=
NATS_USE_TLS=true
NATS_TOKEN=

# Redis Configuration
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_IDLE_CONNS=10
REDIS_MAX_OPEN_CONNS=100
REDIS_MAX_LIFETIME=1h

# WebSocket Configuration
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=3s
WEBSOCKET_PING_INTERVAL=15s
WEBSOCKET_PONG_TIMEOUT=5s

# OHLCV Backfill Proxy Configuration
# Enable/disable HTTP proxy usage for rate limit avoidance
# PRODUCTION: Consider disabling if proxies are not needed or causing issues
BACKFILL_PROXIES_ENABLED=true

# Maximum concurrent requests (should match proxy count for optimal performance)
# PRODUCTION: Tune based on actual proxy performance and API rate limits
BACKFILL_PROXIES_CONCURRENCY=10

# Proxy rotation strategy: "round-robin" or "random"
# PRODUCTION: "round-robin" provides more predictable load distribution
BACKFILL_PROXIES_ROTATION=round-robin

# Proxy Health Check Configuration
# Enable/disable automatic proxy health checking
# PRODUCTION: Recommended to keep enabled for automatic failover
BACKFILL_PROXY_HEALTH_CHECK_ENABLED=true

# Interval between health checks (e.g., "5m", "10m", "1h")
# PRODUCTION: Consider longer intervals to reduce health check overhead
BACKFILL_PROXY_HEALTH_CHECK_INTERVAL=10m

# Timeout for individual health check requests (e.g., "10s", "30s")
# PRODUCTION: Use longer timeout to account for network latency
BACKFILL_PROXY_HEALTH_CHECK_TIMEOUT=30s

# URL to use for proxy health checks (should return HTTP 200)
# PRODUCTION: Consider using a lightweight endpoint or internal service
BACKFILL_PROXY_HEALTH_CHECK_URL=https://httpbin.org/ip
