# Application Configuration
APP_ENV=unstable
APP_NAME=dex-hypertrader
SERVER_PORT=8080

# TiDB Configuration
TIDB_HOST=tidb.adxixt1valr1.clusters.tidb-cloud.com
TIDB_PORT=4000
TIDB_USER=root
TIDB_PASS=XN9YY2P2gl41IoqjQdnQEomHIivuwLCZ
TIDB_DB=test
TIDB_CONFIG=charset=utf8mb4&parseTime=True&loc=Local
TIDB_MAX_IDLE_CONNS=5
TIDB_MAX_OPEN_CONNS=50
TIDB_MAX_LIFETIME=30m

# ClickHouse Configuration (OLAP - Analytics workloads)
CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=dex
CLICKHOUSE_PASS=Zww9fKzH4MbI19K3PrT0WIXFnvmFSvKAEUV4r0aqAho0
CLICKHOUSE_DB=default

# JWT Configuration
JWT_SECRET=unstable-experimental-secret-key-dex-hypertrader
JWT_EXPIRES_TIME=1h
JWT_ISSUER=dex-hypertrader-unstable

# NATS Configuration
NATS_URL=nats://***********:4222
NATS_USER=dex
NATS_PASS=toLCNEc6vI5YpFRcfqX4FrklRizGBGpm
NATS_USE_TLS=false

# Redis Configuration
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=fwdA08030FMPAcQcR3Mk7hQRXZ036y4zJ5qODub0dbU0
REDIS_DB=0
REDIS_MAX_IDLE_CONNS=5
REDIS_MAX_OPEN_CONNS=50
REDIS_MAX_LIFETIME=30m

# WebSocket Configuration
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=3s
WEBSOCKET_PING_INTERVAL=15s
WEBSOCKET_PONG_TIMEOUT=5s

# OHLCV Backfill Proxy Configuration
# Enable/disable HTTP proxy usage for rate limit avoidance
# UNSTABLE: Experimental proxy settings for testing new features
BACKFILL_PROXIES_ENABLED=true

# Maximum concurrent requests (should match proxy count for optimal performance)
# UNSTABLE: May be adjusted for testing different concurrency levels
BACKFILL_PROXIES_CONCURRENCY=5

# Proxy rotation strategy: "round-robin" or "random"
# UNSTABLE: May test different rotation strategies
BACKFILL_PROXIES_ROTATION=random

# Proxy Health Check Configuration
# Enable/disable automatic proxy health checking
# UNSTABLE: May be disabled for testing proxy failure scenarios
BACKFILL_PROXY_HEALTH_CHECK_ENABLED=true

# Interval between health checks (e.g., "5m", "10m", "1h")
# UNSTABLE: Shorter interval for faster feedback during testing
BACKFILL_PROXY_HEALTH_CHECK_INTERVAL=2m

# Timeout for individual health check requests (e.g., "10s", "30s")
# UNSTABLE: Shorter timeout for faster failure detection during testing
BACKFILL_PROXY_HEALTH_CHECK_TIMEOUT=5s

# URL to use for proxy health checks (should return HTTP 200)
# UNSTABLE: May use different endpoints for testing
BACKFILL_PROXY_HEALTH_CHECK_URL=https://httpbin.org/ip
