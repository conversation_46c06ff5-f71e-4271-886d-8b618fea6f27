# Application Configuration
APP_ENV=staging
APP_NAME=dex-hypertrader
SERVER_PORT=8080

# TiDB Configuration
TIDB_HOST=staging-tidb.example.com
TIDB_PORT=4000
TIDB_USER=staging_user
TIDB_PASS=CHANGE_ME_STAGING_PASSWORD
TIDB_DB=xbit_hypertrader_staging
TIDB_CONFIG=charset=utf8mb4&parseTime=True&loc=Local&tls=true
TIDB_MAX_IDLE_CONNS=10
TIDB_MAX_OPEN_CONNS=100
TIDB_MAX_LIFETIME=1h

# ClickHouse Configuration (OLAP - Analytics workloads)
CLICKHOUSE_HOST=
CLICKHOUSE_PORT=
CLICKHOUSE_USER=
CLICKHOUSE_PASS=
CLICKHOUSE_DB=

# JWT Configuration
JWT_SECRET=
JWT_EXPIRES_TIME=1h
JWT_ISSUER=

# NATS Configuration
NATS_URL=
NATS_USER=
NATS_PASS=
NATS_USE_TLS=true
NATS_TOKEN=

# Redis Configuration
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_IDLE_CONNS=10
REDIS_MAX_OPEN_CONNS=100
REDIS_MAX_LIFETIME=1h

# WebSocket Configuration
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=3s
WEBSOCKET_PING_INTERVAL=15s
WEBSOCKET_PONG_TIMEOUT=5s

# OHLCV Backfill Proxy Configuration
# Enable/disable HTTP proxy usage for rate limit avoidance
BACKFILL_PROXIES_ENABLED=true

# Maximum concurrent requests (should match proxy count for optimal performance)
BACKFILL_PROXIES_CONCURRENCY=10

# Proxy rotation strategy: "round-robin" or "random"
BACKFILL_PROXIES_ROTATION=round-robin

# Proxy Health Check Configuration
# Enable/disable automatic proxy health checking
BACKFILL_PROXY_HEALTH_CHECK_ENABLED=true

# Interval between health checks (e.g., "5m", "10m", "1h")
BACKFILL_PROXY_HEALTH_CHECK_INTERVAL=5m

# Timeout for individual health check requests (e.g., "10s", "30s")
BACKFILL_PROXY_HEALTH_CHECK_TIMEOUT=10s

# URL to use for proxy health checks (should return HTTP 200)
BACKFILL_PROXY_HEALTH_CHECK_URL=https://httpbin.org/ip
