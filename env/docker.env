# Application Configuration
APP_ENV=docker
APP_NAME=dex-hypertrader
SERVER_PORT=8080

# TiDB Configuration
TIDB_HOST=**********
TIDB_PORT=4000
TIDB_USER=root
TIDB_PASS=XN9YY2P2gl41IoqjQdnQEomHIivuwLCZ
TIDB_DB=test
TIDB_CONFIG=charset=utf8mb4&parseTime=True&loc=Local
TIDB_MAX_IDLE_CONNS=10
TIDB_MAX_OPEN_CONNS=100
TIDB_MAX_LIFETIME=1h

# ClickHouse Configuration (OLAP - Analytics workloads)
CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=dex
CLICKHOUSE_PASS=Zww9fKzH4MbI19K3PrT0WIXFnvmFSvKAEUV4r0aqAho0
CLICKHOUSE_DB=default

# JWT Configuration
JWT_SECRET=docker-secret-key-dex-hypertrader
JWT_EXPIRES_TIME=7d
JWT_ISSUER=dex-hypertrader-docker

# NATS Configuration
NATS_URL=nats://***********:4222
NATS_USER=dex
NATS_PASS=toLCNEc6vI5YpFRcfqX4FrklRizGBGpm
NATS_AUTH_TOKEN=0Kz2YxNusQ0aUBRWQnl00cyUXWHAIRHM9S4dnIQFiss0
NATS_USE_TLS=false

# Redis Configuration
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=fwdA08030FMPAcQcR3Mk7hQRXZ036y4zJ5qODub0dbU0
REDIS_DB=0
REDIS_MAX_IDLE_CONNS=10
REDIS_MAX_OPEN_CONNS=100
REDIS_MAX_LIFETIME=1h

# WebSocket Configuration
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=5s
WEBSOCKET_PING_INTERVAL=30s
WEBSOCKET_PONG_TIMEOUT=10s

# OHLCV Backfill Proxy Configuration
# Enable/disable HTTP proxy usage for rate limit avoidance
BACKFILL_PROXIES_ENABLED=true

# Maximum concurrent requests (should match proxy count for optimal performance)
BACKFILL_PROXIES_CONCURRENCY=10

# Proxy rotation strategy: "round-robin" or "random"
BACKFILL_PROXIES_ROTATION=round-robin

# Proxy Health Check Configuration
# Enable/disable automatic proxy health checking
BACKFILL_PROXY_HEALTH_CHECK_ENABLED=true

# Interval between health checks (e.g., "5m", "10m", "1h")
BACKFILL_PROXY_HEALTH_CHECK_INTERVAL=5m

# Timeout for individual health check requests (e.g., "10s", "30s")
BACKFILL_PROXY_HEALTH_CHECK_TIMEOUT=10s

# URL to use for proxy health checks (should return HTTP 200)
BACKFILL_PROXY_HEALTH_CHECK_URL=https://httpbin.org/ip
