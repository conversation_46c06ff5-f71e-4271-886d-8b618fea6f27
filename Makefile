# Project Configuration
Server = .
ServerName = dex-hypertrader
GOOS = linux
GOARCH = amd64
BinDir = ./bin

# Detect local platform
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

# Build targets
build: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go

build-local: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go

build-scheduler:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName)-scheduler cmd/scheduler/main.go

build-websocket-worker:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName)-websocket-worker cmd/worker_websocket/main.go

build-nats-consumer-worker:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName)-nats-consumer-worker cmd/worker_nats_consumer/main.go

build-test-realtime:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/test-realtime cmd/test_realtime/main.go

build-all: build-local build-scheduler build-websocket-worker build-nats-consumer-worker

# GraphQL generation
gqlgen:
	@echo "Generating GraphQL code..."
	go run github.com/99designs/gqlgen generate --config gqlgen.yml

gqlgen-check:
	@if [ ! -f "internal/controller/graphql/generated.go" ]; then \
		echo "Generated GraphQL files not found, generating..."; \
		$(MAKE) gqlgen; \
	fi

# Database operations
db-create-tables:
	@echo "Creating all database tables..."
	./scripts/run.sh local create-tables

db-create-clickhouse-tables:
	@echo "Creating ClickHouse tables only..."
	./scripts/run.sh local create-clickhouse-tables

# ClickHouse migration operations
clickhouse-migrate-up:
	@echo "Running ClickHouse migrations up..."
	go run cmd/cli/clickhouse_migrate_up.go

clickhouse-migrate-down:
	@echo "Rolling back ClickHouse migration..."
	go run cmd/cli/clickhouse_migrate_down.go

clickhouse-migrate-status:
	@echo "Checking ClickHouse migration status..."
	go run cmd/cli/clickhouse_migrate_status.go

clickhouse-migrate-create:
	@if [ -z "$(NAME)" ]; then \
		echo "Usage: make clickhouse-migrate-create NAME=migration_name"; \
		echo "Example: make clickhouse-migrate-create NAME=add_indexes"; \
		exit 1; \
	fi
	@echo "Creating new ClickHouse migration: $(NAME)"
	go run cmd/cli/clickhouse_migrate_create.go $(NAME)

# Testing
test:
	go test ./...

test-verbose:
	go test -v ./...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Optimization tests
test-websocket-optimization:
	go test -v ./internal/service -run TestOptimized

test-websocket-performance:
	go test -v ./internal/service -run TestMessageSizeReduction

test-pipeline-optimization:
	go test -v ./internal/service -run TestPipelineOptimization

test-memory-optimization:
	go test -v ./internal/service -run TestMemoryUsageOptimization

test-websocket-message:
	go test -v ./internal/service -run TestRealWebSocketMessage

benchmark-websocket-optimization:
	go test -bench=BenchmarkOptimized ./internal/service -benchmem

benchmark-real-message:
	go test -bench=BenchmarkRealMessageParsing ./internal/service -benchmem

benchmark-pipeline-optimization:
	go test -bench=BenchmarkOptimizedPipeline ./internal/service -benchmem

benchmark-websocket-message:
	go test -bench=BenchmarkRealMessageParsing ./internal/service -benchmem

# Development
run-local:
	./scripts/run.sh local run

run-scheduler:
	./scripts/run.sh local scheduler

run-websocket-worker:
	./scripts/run.sh local websocket-worker

run-nats-consumer-worker:
	./scripts/run.sh local nats-consumer-worker

# NATS utilities
check-nats-consumer:
	go run scripts/check_nats_consumer.go

delete-nats-consumer:
	go run scripts/delete_nats_consumer.go

test-realtime:
	./scripts/run.sh local test-realtime

dev:
	./scripts/run.sh local dev

# Dependencies
deps:
	go mod tidy
	go mod download

# Linting and formatting
fmt:
	go fmt ./...

vet:
	go vet ./...

lint:
	golangci-lint run

# Docker operations
docker-build:
	docker build -t $(ServerName):latest .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Cleanup
clean:
	rm -rf $(BinDir)
	rm -f coverage.out coverage.html

clean-all: clean
	go clean -cache
	go clean -modcache

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build for Linux (production)"
	@echo "  build-local    - Build for local platform"
	@echo "  build-all      - Build all services"
	@echo "  gqlgen         - Generate GraphQL code"
	@echo "  db-create-tables - Create ClickHouse tables"
	@echo "  clickhouse-migrate-up - Run ClickHouse migrations up"
	@echo "  clickhouse-migrate-down - Rollback ClickHouse migration"
	@echo "  clickhouse-migrate-status - Check ClickHouse migration status"
	@echo "  clickhouse-migrate-create NAME=name - Create new ClickHouse migration"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  test-websocket-optimization - Test WebSocket optimized format"
	@echo "  test-websocket-performance - Test message size reduction"
	@echo "  test-pipeline-optimization - Test entire optimized pipeline"
	@echo "  test-memory-optimization - Test memory usage optimization"
	@echo "  test-websocket-message - Test WebSocket message parsing"
	@echo "  benchmark-websocket-optimization - Benchmark WebSocket parsing"
	@echo "  benchmark-websocket-message - Benchmark WebSocket message parsing"
	@echo "  benchmark-pipeline-optimization - Benchmark entire pipeline"
	@echo "  run-local      - Run GraphQL server locally"
	@echo "  run-scheduler  - Run scheduler locally"
	@echo "  run-websocket-worker - Run WebSocket worker locally"
	@echo "  run-nats-consumer-worker - Run NATS consumer worker locally"
	@echo "  dev            - Run with hot reload"
	@echo "  deps           - Download dependencies"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"
	@echo "  lint           - Run linter"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run with Docker Compose"
	@echo "  clean          - Clean build artifacts"
	@echo "  help           - Show this help"

.PHONY: build build-local build-scheduler build-websocket-worker build-nats-consumer-worker build-all gqlgen gqlgen-check db-create-tables db-create-clickhouse-tables clickhouse-migrate-up clickhouse-migrate-down clickhouse-migrate-status clickhouse-migrate-create test test-verbose test-coverage test-websocket-optimization test-websocket-performance test-pipeline-optimization test-memory-optimization test-websocket-message benchmark-websocket-optimization benchmark-websocket-message benchmark-pipeline-optimization run-local run-scheduler run-websocket-worker run-nats-consumer-worker dev deps fmt vet lint docker-build docker-run docker-stop docker-logs clean clean-all help
