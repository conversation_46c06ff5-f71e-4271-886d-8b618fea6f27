package initializer

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// InitRedis initializes Redis connection for caching and real-time data
func InitRedis() *redis.Client {
	redisConfig := global.GVA_CONFIG.Redis
	if redisConfig.Host == "" {
		global.GVA_LOG.Info("Redis host is empty, skipping Redis initialization")
		return nil
	}

	// Configure Redis client options
	options := &redis.Options{
		Addr:     fmt.Sprintf("%s:%s", redisConfig.Host, redisConfig.Port),
		Password: redisConfig.Password,
		DB:       redisConfig.DB,
	}

	// Set connection pool settings
	if redisConfig.MaxIdleConns > 0 {
		options.MaxIdleConns = redisConfig.MaxIdleConns
	} else {
		options.MaxIdleConns = 10 // Default
	}

	if redisConfig.MaxOpenConns > 0 {
		options.PoolSize = redisConfig.MaxOpenConns
	} else {
		options.PoolSize = 100 // Default
	}

	// Parse max lifetime
	if redisConfig.MaxLifetime != "" {
		if lifetime, err := time.ParseDuration(redisConfig.MaxLifetime); err == nil {
			options.ConnMaxLifetime = lifetime
		} else {
			global.GVA_LOG.Warn(fmt.Sprintf("Invalid Redis max lifetime format: %s, using default", redisConfig.MaxLifetime))
			options.ConnMaxLifetime = time.Hour
		}
	} else {
		options.ConnMaxLifetime = time.Hour
	}

	// Set timeouts
	options.DialTimeout = 10 * time.Second
	options.ReadTimeout = 5 * time.Second
	options.WriteTimeout = 5 * time.Second

	// Create Redis client
	client := redis.NewClient(options)

	// Test connection with context timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to ping Redis: %v", err))
		panic("Failed to ping Redis: " + err.Error())
	}

	// Test a simple command to ensure the connection works
	result, err := client.Info(ctx, "server").Result()
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to get Redis server info: %v", err))
		panic("Failed to get Redis server info: " + err.Error())
	}

	global.GVA_LOG.Info(fmt.Sprintf("Redis connection established successfully, server info length: %d", len(result)))
	return client
}

// GetRedis returns the global Redis connection
func GetRedis() *redis.Client {
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Error("Redis connection is not initialized")
		return nil
	}
	return global.GVA_REDIS
}
