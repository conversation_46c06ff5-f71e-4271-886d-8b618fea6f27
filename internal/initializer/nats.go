package initializer

import (
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/nats"
)

// InitNATS initializes the NATS client connection
func InitNATS() {
	natsClient := nats.InitNatsJetStream(global.GVA_CONFIG.Nats)
	global.GVA_NATS = natsClient
	global.GVA_LOG.Info("NATS client initialized successfully")
}

// GetNATS returns the NATS client instance
func GetNATS() *nats.NATSClient {
	if global.GVA_NATS == nil {
		global.GVA_LOG.Fatal("NATS client not initialized")
	}
	return global.GVA_NATS.(*nats.NATSClient)
}

// CloseNATS closes the NATS connection
func CloseNATS() {
	if global.GVA_NATS != nil {
		natsClient := global.GVA_NATS.(*nats.NATSClient)
		natsClient.Close()
		global.GVA_LOG.Info("NATS connection closed")
	}
}
