package initializer

import (
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// InitDatabases initializes ClickHouse, Redis, and NATS connections
func InitDatabases() {
	global.GVA_LOG.Info("Initializing database connections...")

	// Initialize TiDB for OLTP workloads
	global.GVA_DB_TIDB = InitTiDB()
	if global.GVA_DB_TIDB != nil {
		global.GVA_LOG.Info("TiDB (OLTP) connection initialized successfully")
	}

	// Initialize ClickHouse for OLAP workloads
	global.GVA_DB_CLICKHOUSE = InitClickHouse()
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_LOG.Info("ClickHouse (OLAP) connection initialized successfully")
	}

	// Initialize Redis for caching and real-time data
	global.GVA_REDIS = InitRedis()
	if global.GVA_REDIS != nil {
		global.GVA_LOG.Info("Redis connection initialized successfully")
	}

	// Initialize NATS for message queue
	InitNATS()

	global.GVA_LOG.Info("Database initialization completed")
}

// CloseDatabases closes all database connections gracefully
func CloseDatabases() {
	global.GVA_LOG.Info("Closing database connections...")
	
	// Close TiDB connection
	if global.GVA_DB_TIDB != nil {
		if sqlDB, err := global.GVA_DB_TIDB.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				global.GVA_LOG.Error("Failed to close TiDB connection: " + err.Error())
			} else {
				global.GVA_LOG.Info("TiDB connection closed successfully")
			}
		}
	}

	// Close ClickHouse connection
	if global.GVA_DB_CLICKHOUSE != nil {
		if err := global.GVA_DB_CLICKHOUSE.Close(); err != nil {
			global.GVA_LOG.Error("Failed to close ClickHouse connection: " + err.Error())
		} else {
			global.GVA_LOG.Info("ClickHouse connection closed successfully")
		}
	}

	// Close Redis connection
	if global.GVA_REDIS != nil {
		if err := global.GVA_REDIS.Close(); err != nil {
			global.GVA_LOG.Error("Failed to close Redis connection: " + err.Error())
		} else {
			global.GVA_LOG.Info("Redis connection closed successfully")
		}
	}

	// Close NATS connection
	CloseNATS()

	global.GVA_LOG.Info("All database connections closed")
}
