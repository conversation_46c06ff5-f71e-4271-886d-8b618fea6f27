package initializer

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// InitClickHouse initializes ClickHouse connection for OLAP workloads
func InitClickHouse() *sql.DB {
	ch := global.GVA_CONFIG.ClickHouse
	if ch.Database == "" {
		global.GVA_LOG.Info("ClickHouse database name is empty, skipping ClickHouse initialization")
		return nil
	}

	// Configure ClickHouse connection options with HTTP interface (port 8123)
	options := &clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%s", ch.Host, ch.Port)},
		Auth: clickhouse.Auth{
			Database: ch.Database,
			Username: ch.Username,
			Password: ch.Password,
		},
		Settings: clickhouse.Settings{
			"max_execution_time": 120,
		},
		DialTimeout:      time.Duration(30) * time.Second,
		ConnOpenStrategy: clickhouse.ConnOpenInOrder,
		Protocol:         clickhouse.HTTP, // Use HTTP protocol for port 8123
	}

	// Configure TLS if secure connection is enabled (optional)
	// Since we're only using basic connection parameters, TLS is disabled by default

	// Configure debug logging for development environments
	if global.GVA_CONFIG.System.Env == "local" || global.GVA_CONFIG.System.Env == "unstable" {
		options.Debug = true
	}

	// Open ClickHouse connection
	db := clickhouse.OpenDB(options)
	if db == nil {
		global.GVA_LOG.Error("Failed to create ClickHouse connection")
		panic("Failed to create ClickHouse connection")
	}

	// Set connection pool settings with sensible defaults
	db.SetMaxIdleConns(5)            // Default: 5 idle connections
	db.SetMaxOpenConns(50)           // Default: 50 max open connections
	db.SetConnMaxLifetime(time.Hour) // Default: 1 hour connection lifetime

	// Test connection with context timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to ping ClickHouse: %v", err))
		panic("Failed to ping ClickHouse: " + err.Error())
	}

	// Test a simple query to ensure the connection works
	var version string
	err := db.QueryRowContext(ctx, "SELECT version()").Scan(&version)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to query ClickHouse version: %v", err))
		panic("Failed to query ClickHouse version: " + err.Error())
	}

	global.GVA_LOG.Info(fmt.Sprintf("ClickHouse connection established successfully, version: %s", version))
	return db
}

// GetClickHouse returns the global ClickHouse connection
func GetClickHouse() *sql.DB {
	if global.GVA_DB_CLICKHOUSE == nil {
		global.GVA_LOG.Error("ClickHouse connection is not initialized")
		return nil
	}
	return global.GVA_DB_CLICKHOUSE
}

// ExecuteClickHouseQuery executes a query on ClickHouse with context timeout
func ExecuteClickHouseQuery(query string, args ...interface{}) (*sql.Rows, error) {
	db := GetClickHouse()
	if db == nil {
		return nil, fmt.Errorf("ClickHouse connection is not available")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return db.QueryContext(ctx, query, args...)
}

// ExecuteClickHouseExec executes a non-query statement on ClickHouse
func ExecuteClickHouseExec(query string, args ...interface{}) (sql.Result, error) {
	db := GetClickHouse()
	if db == nil {
		return nil, fmt.Errorf("ClickHouse connection is not available")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return db.ExecContext(ctx, query, args...)
}
