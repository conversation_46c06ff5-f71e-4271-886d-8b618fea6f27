package initializer

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/fsnotify/fsnotify"
	"github.com/joho/godotenv"
	"github.com/spf13/viper"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

func Viper(path ...string) *viper.Viper {
	var config string

	if len(path) == 0 {
		config = "config.yaml"
		if configEnv := os.Getenv("GVA_CONFIG"); configEnv != "" {
			config = configEnv
		}
	} else {
		config = path[0]
	}

	// Load environment file based on STAGE
	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
		os.Setenv("STAGE", stage)
	}

	envFilePath := filepath.Join("env", fmt.Sprintf("%s.env", stage))
	if err := godotenv.Load(envFilePath); err != nil {
		fmt.Printf("Warning: Could not load .env file %s: %v\n", envFilePath, err)
	}

	// Read and parse config template
	configData, err := os.ReadFile(config)
	if err != nil {
		panic(fmt.Errorf("Fatal error reading config file: %s", err))
	}

	// Create template with custom functions and execute with environment variables
	funcMap := template.FuncMap{
		"default": func(defaultValue, value string) string {
			if value == "" {
				return defaultValue
			}
			return value
		},
	}

	tmpl, err := template.New("config").Funcs(funcMap).Parse(string(configData))
	if err != nil {
		panic(fmt.Errorf("Fatal error parsing config template: %s", err))
	}

	envVars := make(map[string]string)
	for _, env := range os.Environ() {
		pair := strings.SplitN(env, "=", 2)
		if len(pair) == 2 {
			envVars[pair[0]] = pair[1]
		}
	}

	var buf bytes.Buffer
	if err = tmpl.Execute(&buf, envVars); err != nil {
		panic(fmt.Errorf("Fatal error executing config template: %s", err))
	}

	v := viper.New()
	v.SetConfigType("yaml")
	if err = v.ReadConfig(strings.NewReader(buf.String())); err != nil {
		panic(fmt.Errorf("Fatal error reading config: %s", err))
	}

	v.WatchConfig()
	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("Config file changed:", e.Name)
		if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
			fmt.Println(err)
		}
	})

	if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
		panic(err)
	}

	return v
}
