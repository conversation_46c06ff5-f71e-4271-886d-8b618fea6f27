package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

// OHLCRepository implements the OHLCRepositoryInterface
type OHLCRepository struct {
	clickhouse *sql.DB
	redis      *redis.Client
}

// NewOHLCRepository creates a new OHLC repository
func NewOHLCRepository() service.OHLCRepositoryInterface {
	return &OHLCRepository{
		clickhouse: global.GVA_DB_CLICKHOUSE,
		redis:      global.GVA_REDIS,
	}
}

// roundVolume rounds volume values to exactly 2 decimal places
func roundVolume(volume float64) float64 {
	return math.Round(volume*100) / 100
}

// GetHistoricalKlines fetches historical kline data from ClickHouse
func (r *OHLCRepository) GetHistoricalKlines(ctx context.Context, req *service.OHLCQueryRequest) ([]*model.Kline, error) {
	tableName := req.Timeframe.GetTableName()

	var query string
	var args []interface{}

	if req.IsForward {
		// Query from timestamp onwards (ascending order)
		query = fmt.Sprintf(`
			SELECT timestamp, symbol, open, high, low, close, volume, trades_count
			FROM %s
			WHERE symbol = ? AND timestamp >= ?
			ORDER BY timestamp ASC
			LIMIT ?
		`, tableName)
		args = []interface{}{req.Symbol, req.Timestamp, req.Limit}
	} else {
		// Query before timestamp (descending order)
		query = fmt.Sprintf(`
			SELECT timestamp, symbol, open, high, low, close, volume, trades_count
			FROM %s
			WHERE symbol = ? AND timestamp < ?
			ORDER BY timestamp DESC
			LIMIT ?
		`, tableName)
		args = []interface{}{req.Symbol, req.Timestamp, req.Limit}
	}

	rows, err := r.clickhouse.QueryContext(ctx, query, args...)
	if err != nil {
		global.GVA_LOG.Error("Failed to query ClickHouse for historical klines",
			zap.String("symbol", req.Symbol),
			zap.String("timeframe", string(req.Timeframe)),
			zap.Error(err))
		return nil, fmt.Errorf("failed to query historical klines: %w", err)
	}
	defer rows.Close()

	var klines []*model.Kline
	for rows.Next() {
		kline := &model.Kline{}
		err := rows.Scan(
			&kline.Timestamp,
			&kline.Symbol,
			&kline.Open,
			&kline.High,
			&kline.Low,
			&kline.Close,
			&kline.Volume,
			&kline.TradesCount,
		)
		if err != nil {
			global.GVA_LOG.Error("Failed to scan kline row",
				zap.String("symbol", req.Symbol),
				zap.Error(err))
			continue
		}
		klines = append(klines, kline)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating over kline rows: %w", err)
	}

	return klines, nil
}

// GetActiveKline fetches the current active kline from Redis with fallback logic
func (r *OHLCRepository) GetActiveKline(ctx context.Context, symbol string, timeframe model.KlineTimeframe) (*model.Kline, error) {
	now := time.Now()
	currentCandleTime := timeframe.TruncateTime(now)

	// Try to get the current candle from Redis first
	currentKline, err := r.getKlineFromRedis(ctx, symbol, timeframe, currentCandleTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to get current kline from Redis",
			zap.String("symbol", symbol),
			zap.String("timeframe", string(timeframe)),
			zap.Error(err))
		// Continue with fallback logic even if Redis fails
	}

	// If we have the current candle, return it
	if currentKline != nil {
		return currentKline, nil
	}

	// Handle missing current candle data - check if we're at the start of a new candle period
	// and Redis data might be delayed by 1-2 seconds
	if now.Sub(currentCandleTime) <= 2*time.Second {
		// We're at the beginning of a new candle period, try fallback strategies

		// Strategy 1: Try to get the most recent previous candle from Redis
		previousKline, err := r.getMostRecentKlineFromRedis(ctx, symbol, timeframe, currentCandleTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get previous kline from Redis",
				zap.String("symbol", symbol),
				zap.String("timeframe", string(timeframe)),
				zap.Error(err))
		}

		if previousKline != nil {
			// Initialize new candle using previous candle's close price
			initializedKline := r.initializeKlineFromPrevious(previousKline, currentCandleTime, symbol)

			// Write the initialized candle to Redis for future requests
			if err := r.writeKlineToRedis(ctx, initializedKline, timeframe); err != nil {
				global.GVA_LOG.Error("Failed to write initialized kline to Redis",
					zap.String("symbol", symbol),
					zap.String("timeframe", string(timeframe)),
					zap.Error(err))
			}

			return initializedKline, nil
		}

		// Strategy 2: Fallback to ClickHouse if no data in Redis at all
		clickhouseKline, err := r.GetLastCompletedKline(ctx, symbol, timeframe, currentCandleTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get last completed kline from ClickHouse",
				zap.String("symbol", symbol),
				zap.String("timeframe", string(timeframe)),
				zap.Error(err))
			return nil, fmt.Errorf("failed to get fallback kline from ClickHouse: %w", err)
		}

		if clickhouseKline != nil {
			// Initialize new candle using ClickHouse data
			initializedKline := r.initializeKlineFromPrevious(clickhouseKline, currentCandleTime, symbol)

			// Write the initialized candle to both Redis and ClickHouse
			if err := r.writeKlineToRedis(ctx, initializedKline, timeframe); err != nil {
				global.GVA_LOG.Error("Failed to write initialized kline to Redis",
					zap.String("symbol", symbol),
					zap.String("timeframe", string(timeframe)),
					zap.Error(err))
			}

			if err := r.writeKlineToClickHouse(ctx, initializedKline, timeframe); err != nil {
				global.GVA_LOG.Error("Failed to write initialized kline to ClickHouse",
					zap.String("symbol", symbol),
					zap.String("timeframe", string(timeframe)),
					zap.Error(err))
			}

			return initializedKline, nil
		}
	}

	// If we reach here, either we're not at the start of a candle period or no fallback data is available
	// Try to get the most recent kline from Redis regardless of timestamp
	recentKline, err := r.getMostRecentKlineFromRedis(ctx, symbol, timeframe, time.Time{})
	if err != nil {
		global.GVA_LOG.Error("Failed to get any recent kline from Redis",
			zap.String("symbol", symbol),
			zap.String("timeframe", string(timeframe)),
			zap.Error(err))
	}

	return recentKline, nil
}

// GetLastCompletedKline fetches the last completed kline for fallback logic
func (r *OHLCRepository) GetLastCompletedKline(ctx context.Context, symbol string, timeframe model.KlineTimeframe, beforeTimestamp time.Time) (*model.Kline, error) {
	tableName := timeframe.GetTableName()

	query := fmt.Sprintf(`
		SELECT timestamp, symbol, open, high, low, close, volume, trades_count
		FROM %s
		WHERE symbol = ? AND timestamp < ?
		ORDER BY timestamp DESC
		LIMIT 1
	`, tableName)

	row := r.clickhouse.QueryRowContext(ctx, query, symbol, beforeTimestamp)

	kline := &model.Kline{}
	err := row.Scan(
		&kline.Timestamp,
		&kline.Symbol,
		&kline.Open,
		&kline.High,
		&kline.Low,
		&kline.Close,
		&kline.Volume,
		&kline.TradesCount,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// No previous kline found
			return nil, nil
		}
		global.GVA_LOG.Error("Failed to get last completed kline",
			zap.String("symbol", symbol),
			zap.String("timeframe", string(timeframe)),
			zap.Time("beforeTimestamp", beforeTimestamp),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get last completed kline: %w", err)
	}

	return kline, nil
}

// getKlineFromRedis fetches a specific kline from Redis by timestamp
func (r *OHLCRepository) getKlineFromRedis(ctx context.Context, symbol string, timeframe model.KlineTimeframe, timestamp time.Time) (*model.Kline, error) {
	redisKey := fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))
	score := float64(timestamp.UnixMilli())

	// Get kline with specific timestamp from sorted set
	results, err := r.redis.ZRangeByScore(ctx, redisKey, &redis.ZRangeBy{
		Min: fmt.Sprintf("%f", score),
		Max: fmt.Sprintf("%f", score),
	}).Result()

	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get kline from Redis: %w", err)
	}

	if len(results) == 0 {
		return nil, nil
	}

	// Parse the Redis kline data
	var redisKline service.RedisKline
	if err := json.Unmarshal([]byte(results[0]), &redisKline); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Redis kline data: %w", err)
	}

	// Convert Redis kline to model.Kline
	kline := &model.Kline{
		Timestamp:   time.Unix(0, redisKline.Timestamp*int64(time.Millisecond)),
		Symbol:      redisKline.Symbol,
		Open:        redisKline.Open,
		High:        redisKline.High,
		Low:         redisKline.Low,
		Close:       redisKline.Close,
		Volume:      redisKline.Volume,
		TradesCount: redisKline.TradesCount,
	}

	return kline, nil
}

// getMostRecentKlineFromRedis fetches the most recent kline from Redis, optionally before a specific timestamp
func (r *OHLCRepository) getMostRecentKlineFromRedis(ctx context.Context, symbol string, timeframe model.KlineTimeframe, beforeTimestamp time.Time) (*model.Kline, error) {
	redisKey := fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))

	var results []string
	var err error

	if beforeTimestamp.IsZero() {
		// Get the most recent kline (highest score)
		results, err = r.redis.ZRevRange(ctx, redisKey, 0, 0).Result()
	} else {
		// Get the most recent kline before the specified timestamp
		maxScore := float64(beforeTimestamp.UnixMilli()) - 1
		results, err = r.redis.ZRevRangeByScore(ctx, redisKey, &redis.ZRangeBy{
			Min: "-inf",
			Max: fmt.Sprintf("%f", maxScore),
		}).Result()
		if len(results) > 0 {
			results = results[:1] // Take only the most recent one
		}
	}

	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get recent kline from Redis: %w", err)
	}

	if len(results) == 0 {
		return nil, nil
	}

	// Parse the Redis kline data
	var redisKline service.RedisKline
	if err := json.Unmarshal([]byte(results[0]), &redisKline); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Redis kline data: %w", err)
	}

	// Convert Redis kline to model.Kline
	kline := &model.Kline{
		Timestamp:   time.Unix(0, redisKline.Timestamp*int64(time.Millisecond)),
		Symbol:      redisKline.Symbol,
		Open:        redisKline.Open,
		High:        redisKline.High,
		Low:         redisKline.Low,
		Close:       redisKline.Close,
		Volume:      redisKline.Volume,
		TradesCount: redisKline.TradesCount,
	}

	return kline, nil
}

// initializeKlineFromPrevious creates a new kline initialized with the previous kline's close price
func (r *OHLCRepository) initializeKlineFromPrevious(previousKline *model.Kline, newTimestamp time.Time, symbol string) *model.Kline {
	return &model.Kline{
		Timestamp:   newTimestamp,
		Symbol:      symbol,
		Open:        previousKline.Close,
		High:        previousKline.Close,
		Low:         previousKline.Close,
		Close:       previousKline.Close,
		Volume:      0,
		TradesCount: 0,
	}
}

// writeKlineToRedis writes a kline to Redis using the same format as the existing Redis kline service
func (r *OHLCRepository) writeKlineToRedis(ctx context.Context, kline *model.Kline, timeframe model.KlineTimeframe) error {
	redisKey := fmt.Sprintf("kline:%s:%s", kline.Symbol, string(timeframe))
	score := float64(kline.Timestamp.UnixMilli())

	// Create RedisKline structure
	redisKline := service.RedisKline{
		Timestamp:       kline.Timestamp.UnixMilli(),
		Symbol:          kline.Symbol,
		Open:            kline.Open,
		High:            kline.High,
		Low:             kline.Low,
		Close:           kline.Close,
		Volume:          kline.Volume,
		TradesCount:     kline.TradesCount,
		Timeframe:       string(timeframe),
		OpenIndex:       0, // Default for initialized candles
		CloseIndex:      0, // Default for initialized candles
		OpenBatchIndex:  0, // Default for initialized candles
		CloseBatchIndex: 0, // Default for initialized candles
		OpenTimestamp:   kline.Timestamp.UnixMilli(),
		CloseTimestamp:  kline.Timestamp.UnixMilli(),
	}

	// Serialize kline data
	klineData, err := json.Marshal(redisKline)
	if err != nil {
		return fmt.Errorf("failed to marshal kline data: %w", err)
	}

	// Use pipeline for atomic operations
	pipe := r.redis.Pipeline()

	// Remove old entry first (if exists)
	pipe.ZRemRangeByScore(ctx, redisKey, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score))

	// Add the kline to sorted set
	pipe.ZAdd(ctx, redisKey, redis.Z{
		Score:  score,
		Member: string(klineData),
	})

	// Execute pipeline
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute Redis pipeline: %w", err)
	}

	return nil
}

// writeKlineToClickHouse writes a kline to ClickHouse
func (r *OHLCRepository) writeKlineToClickHouse(ctx context.Context, kline *model.Kline, timeframe model.KlineTimeframe) error {
	tableName := timeframe.GetTableName()

	query := fmt.Sprintf(`
		INSERT INTO %s (timestamp, symbol, open, high, low, close, volume, trades_count)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, tableName)

	_, err := r.clickhouse.ExecContext(ctx, query,
		kline.Timestamp,
		kline.Symbol,
		kline.Open,
		kline.High,
		kline.Low,
		kline.Close,
		roundVolume(kline.Volume), // Round volume to 2 decimal places
		kline.TradesCount,
	)

	if err != nil {
		return fmt.Errorf("failed to insert kline to ClickHouse: %w", err)
	}

	return nil
}
