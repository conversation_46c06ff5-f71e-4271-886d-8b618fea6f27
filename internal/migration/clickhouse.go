package migration

import (
	"database/sql"
	"fmt"
	"path/filepath"

	"github.com/pressly/goose/v3"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

const (
	// MigrationsDir is the directory containing ClickHouse migration files
	MigrationsDir = "migrations/clickhouse"
)

// InitClickHouseMigrations initializes the migration system for ClickHouse
func InitClickHouseMigrations() error {
	// Set the dialect for ClickHouse
	if err := goose.SetDialect("clickhouse"); err != nil {
		return fmt.Errorf("failed to set ClickHouse dialect: %w", err)
	}

	global.GVA_LOG.Info("ClickHouse migration system initialized")
	return nil
}

// RunClickHouseMigrations runs all pending ClickHouse migrations
func RunClickHouseMigrations(db *sql.DB) error {
	if db == nil {
		return fmt.Errorf("ClickHouse database connection is nil")
	}

	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return err
	}

	// Get absolute path to migrations directory
	migrationsPath, err := filepath.Abs(MigrationsDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations: %w", err)
	}

	global.GVA_LOG.Info(fmt.Sprintf("Running ClickHouse migrations from: %s", migrationsPath))

	// Run migrations
	if err := goose.Up(db, migrationsPath); err != nil {
		return fmt.Errorf("failed to run ClickHouse migrations: %w", err)
	}

	global.GVA_LOG.Info("ClickHouse migrations completed successfully")
	return nil
}

// RollbackClickHouseMigration rolls back the last ClickHouse migration
func RollbackClickHouseMigration(db *sql.DB) error {
	if db == nil {
		return fmt.Errorf("ClickHouse database connection is nil")
	}

	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return err
	}

	// Get absolute path to migrations directory
	migrationsPath, err := filepath.Abs(MigrationsDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations: %w", err)
	}

	global.GVA_LOG.Info("Rolling back last ClickHouse migration")

	// Rollback last migration
	if err := goose.Down(db, migrationsPath); err != nil {
		return fmt.Errorf("failed to rollback ClickHouse migration: %w", err)
	}

	global.GVA_LOG.Info("ClickHouse migration rollback completed successfully")
	return nil
}

// GetClickHouseMigrationStatus returns the current migration status
func GetClickHouseMigrationStatus(db *sql.DB) error {
	if db == nil {
		return fmt.Errorf("ClickHouse database connection is nil")
	}

	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return err
	}

	// Get absolute path to migrations directory
	migrationsPath, err := filepath.Abs(MigrationsDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations: %w", err)
	}

	global.GVA_LOG.Info("Checking ClickHouse migration status")

	// Get migration status
	if err := goose.Status(db, migrationsPath); err != nil {
		return fmt.Errorf("failed to get ClickHouse migration status: %w", err)
	}

	return nil
}

// CreateClickHouseMigration creates a new migration file
func CreateClickHouseMigration(name string) error {
	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return err
	}

	// Get absolute path to migrations directory
	migrationsPath, err := filepath.Abs(MigrationsDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations: %w", err)
	}

	global.GVA_LOG.Info(fmt.Sprintf("Creating new ClickHouse migration: %s", name))

	// Create migration file
	if err := goose.Create(nil, migrationsPath, name, "sql"); err != nil {
		return fmt.Errorf("failed to create ClickHouse migration: %w", err)
	}

	global.GVA_LOG.Info(fmt.Sprintf("ClickHouse migration created successfully: %s", name))
	return nil
}

// ResetClickHouseMigrations resets all migrations (drops all tables and re-runs migrations)
// WARNING: This will delete all data!
func ResetClickHouseMigrations(db *sql.DB) error {
	if db == nil {
		return fmt.Errorf("ClickHouse database connection is nil")
	}

	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return err
	}

	// Get absolute path to migrations directory
	migrationsPath, err := filepath.Abs(MigrationsDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for migrations: %w", err)
	}

	global.GVA_LOG.Warn("Resetting ClickHouse migrations - this will delete all data!")

	// Reset migrations
	if err := goose.Reset(db, migrationsPath); err != nil {
		return fmt.Errorf("failed to reset ClickHouse migrations: %w", err)
	}

	global.GVA_LOG.Info("ClickHouse migrations reset completed successfully")
	return nil
}

// GetClickHouseVersion returns the current migration version
func GetClickHouseVersion(db *sql.DB) (int64, error) {
	if db == nil {
		return 0, fmt.Errorf("ClickHouse database connection is nil")
	}

	// Initialize migration system
	if err := InitClickHouseMigrations(); err != nil {
		return 0, err
	}

	// Get current version
	version, err := goose.GetDBVersion(db)
	if err != nil {
		return 0, fmt.Errorf("failed to get ClickHouse migration version: %w", err)
	}

	return version, nil
}
