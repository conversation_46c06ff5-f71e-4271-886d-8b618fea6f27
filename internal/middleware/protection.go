package middleware

import (
	"bytes"
	"crypto/md5"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ulule/limiter/v3"
	"github.com/ulule/limiter/v3/drivers/store/memory"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// ProtectionConfig protection mechanism configuration
type ProtectionConfig struct {
	// Rate limiting configuration
	RateLimit struct {
		Enabled   bool          `yaml:"enabled"`
		Requests  int           `yaml:"requests"`  // Number of requests per time window
		Window    time.Duration `yaml:"window"`    // Time window
		SkipPaths []string      `yaml:"skipPaths"` // Paths to skip rate limiting
	} `yaml:"rateLimit"`

	// Cache configuration
	Cache struct {
		Enabled     bool          `yaml:"enabled"`
		TTL         time.Duration `yaml:"ttl"`         // Cache time-to-live
		MaxSize     int           `yaml:"maxSize"`     // Maximum number of cache entries
		SkipPaths   []string      `yaml:"skipPaths"`   // Paths to skip caching
		SkipMethods []string      `yaml:"skipMethods"` // HTTP methods to skip caching
	} `yaml:"cache"`

	// Circuit breaker configuration
	CircuitBreaker struct {
		Enabled           bool          `yaml:"enabled"`
		MaxMemoryUsage    float64       `yaml:"maxMemoryUsage"`    // Maximum memory usage (0-1)
		MaxCPUUsage       float64       `yaml:"maxCPUUsage"`       // Maximum CPU usage (0-1)
		MaxResponseTime   time.Duration `yaml:"maxResponseTime"`   // Maximum response time
		CheckInterval     time.Duration `yaml:"checkInterval"`     // Check interval
		RecoveryThreshold int           `yaml:"recoveryThreshold"` // Recovery threshold
	} `yaml:"circuitBreaker"`
}

// CacheEntry cache entry
type CacheEntry struct {
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	TTL       time.Duration
}

// ProtectionMiddleware protection middleware
type ProtectionMiddleware struct {
	config         *ProtectionConfig
	rateLimiter    *limiter.Limiter
	cache          map[string]*CacheEntry
	cacheMutex     sync.RWMutex
	circuitBreaker *CircuitBreaker
}

// CircuitBreaker circuit breaker
type CircuitBreaker struct {
	enabled           bool
	maxMemoryUsage    float64
	maxCPUUsage       float64
	maxResponseTime   time.Duration
	checkInterval     time.Duration
	recoveryThreshold int

	state           string // "closed", "open", "half-open"
	failureCount    int
	lastFailureTime time.Time
	lastCheckTime   time.Time

	mutex sync.RWMutex
}

// NewProtectionMiddleware creates a new protection middleware
func NewProtectionMiddleware(config *ProtectionConfig) *ProtectionMiddleware {
	pm := &ProtectionMiddleware{
		config: config,
		cache:  make(map[string]*CacheEntry),
	}

	// Initialize rate limiter
	if config.RateLimit.Enabled {
		store := memory.NewStore()
		rate := limiter.Rate{
			Period: config.RateLimit.Window,
			Limit:  int64(config.RateLimit.Requests),
		}
		pm.rateLimiter = limiter.New(store, rate)
	}

	// Initialize circuit breaker
	if config.CircuitBreaker.Enabled {
		pm.circuitBreaker = &CircuitBreaker{
			enabled:           config.CircuitBreaker.Enabled,
			maxMemoryUsage:    config.CircuitBreaker.MaxMemoryUsage,
			maxCPUUsage:       config.CircuitBreaker.MaxCPUUsage,
			maxResponseTime:   config.CircuitBreaker.MaxResponseTime,
			checkInterval:     config.CircuitBreaker.CheckInterval,
			recoveryThreshold: config.CircuitBreaker.RecoveryThreshold,
			state:             "closed",
		}

		// Start circuit breaker monitoring
		go pm.circuitBreaker.monitor()
	}

	// Start cache cleanup goroutine
	if config.Cache.Enabled {
		go pm.cleanupCache()
	}

	return pm
}

// Middleware returns Gin middleware function
func (pm *ProtectionMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// 1. Check circuit breaker status
		if pm.circuitBreaker != nil && pm.circuitBreaker.isOpen() {
			pm.handleCircuitBreakerOpen(c)
			return
		}

		// 2. Rate limiting check
		if pm.rateLimiter != nil && pm.shouldApplyRateLimit(c.Request.URL.Path) {
			if !pm.checkRateLimit(c) {
				return
			}
		}

		// 3. Cache check
		var cacheKey string
		var cachedData interface{}
		if pm.config.Cache.Enabled && pm.shouldApplyCache(c) {
			global.GVA_LOG.Info("Cache enabled, generating cache key")
			cacheKey = pm.generateCacheKey(c)
			if cachedData = pm.getFromCache(cacheKey); cachedData != nil {
				pm.handleCachedResponse(c, cachedData)
				return
			}
		} else {
			global.GVA_LOG.Info("Cache disabled or not applicable",
				zap.Bool("cacheEnabled", pm.config.Cache.Enabled),
				zap.Bool("shouldApplyCache", pm.shouldApplyCache(c)))
		}

		// 4. Execute request
		c.Next()

		// 5. Record response time and update circuit breaker
		responseTime := time.Since(startTime)
		if pm.circuitBreaker != nil {
			pm.circuitBreaker.recordResponse(responseTime)
		}

		// 6. Cache response
		if pm.config.Cache.Enabled && pm.shouldApplyCache(c) && c.Writer.Status() == http.StatusOK {
			pm.cacheResponse(cacheKey, c)
		}
	}
}

// shouldApplyRateLimit checks whether rate limiting should be applied
func (pm *ProtectionMiddleware) shouldApplyRateLimit(path string) bool {
	for _, skipPath := range pm.config.RateLimit.SkipPaths {
		if path == skipPath {
			return false
		}
	}
	return true
}

// checkRateLimit checks rate limiting
func (pm *ProtectionMiddleware) checkRateLimit(c *gin.Context) bool {
	clientIP := c.ClientIP()
	context, err := pm.rateLimiter.Get(c, clientIP)
	if err != nil {
		global.GVA_LOG.Error("Rate limiter error", zap.Error(err))
		return true // Allow request to pass when error occurs
	}

	if context.Reached {
		global.GVA_LOG.Warn("Rate limit exceeded",
			zap.String("clientIP", clientIP),
			zap.Int64("limit", context.Limit),
			zap.Int64("remaining", context.Remaining))

		c.JSON(http.StatusTooManyRequests, gin.H{
			"success":     false,
			"message":     "Request too frequent, please try again later",
			"retry_after": context.Reset,
		})
		return false
	}

	// Set response headers
	c.Header("X-RateLimit-Limit", strconv.FormatInt(context.Limit, 10))
	c.Header("X-RateLimit-Remaining", strconv.FormatInt(context.Remaining, 10))
	c.Header("X-RateLimit-Reset", strconv.FormatInt(context.Reset, 10))

	return true
}

// shouldApplyCache checks whether caching should be applied
func (pm *ProtectionMiddleware) shouldApplyCache(c *gin.Context) bool {
	// Check paths
	for _, skipPath := range pm.config.Cache.SkipPaths {
		if c.Request.URL.Path == skipPath {
			return false
		}
	}

	// Check HTTP methods
	for _, skipMethod := range pm.config.Cache.SkipMethods {
		if c.Request.Method == skipMethod {
			return false
		}
	}

	return true
}

// generateCacheKey generates cache key
func (pm *ProtectionMiddleware) generateCacheKey(c *gin.Context) string {
	// Generate cache key using request path, method and parameters
	key := fmt.Sprintf("%s:%s:%s", c.Request.Method, c.Request.URL.Path, c.Request.URL.RawQuery)

	// For POST requests, also include request body
	if c.Request.Method == "POST" {
		// Read request body without consuming it
		body, err := c.GetRawData()
		if err == nil && len(body) > 0 {
			hash := md5.Sum(body)
			key += fmt.Sprintf(":%x", hash)

			// Restore the request body so it can be read by handlers
			c.Request.Body = io.NopCloser(bytes.NewReader(body))
		}
	}

	return key
}

// getFromCache gets data from cache
func (pm *ProtectionMiddleware) getFromCache(key string) interface{} {
	pm.cacheMutex.RLock()
	defer pm.cacheMutex.RUnlock()

	entry, exists := pm.cache[key]
	if !exists {
		return nil
	}

	// Check if expired
	if time.Since(entry.Timestamp) > entry.TTL {
		return nil
	}

	return entry.Data
}

// cacheResponse caches response
func (pm *ProtectionMiddleware) cacheResponse(key string, c *gin.Context) {
	pm.cacheMutex.Lock()
	defer pm.cacheMutex.Unlock()

	// Check cache size limit
	if len(pm.cache) >= pm.config.Cache.MaxSize {
		// Remove oldest entry
		pm.evictOldestEntry()
	}

	// Get response data
	responseData := c.Writer.Header().Get("X-Cached-Response")
	if responseData == "" {
		// If no preset response data, log here if needed
		return
	}

	pm.cache[key] = &CacheEntry{
		Data:      responseData,
		Timestamp: time.Now(),
		TTL:       pm.config.Cache.TTL,
	}
}

// evictOldestEntry removes the oldest cache entry
func (pm *ProtectionMiddleware) evictOldestEntry() {
	var oldestKey string
	var oldestTime time.Time

	for key, entry := range pm.cache {
		if oldestKey == "" || entry.Timestamp.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.Timestamp
		}
	}

	if oldestKey != "" {
		delete(pm.cache, oldestKey)
	}
}

// cleanupCache cleans up expired cache entries
func (pm *ProtectionMiddleware) cleanupCache() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		pm.cacheMutex.Lock()
		now := time.Now()
		for key, entry := range pm.cache {
			if now.Sub(entry.Timestamp) > entry.TTL {
				delete(pm.cache, key)
			}
		}
		pm.cacheMutex.Unlock()
	}
}

// handleCachedResponse handles cached response
func (pm *ProtectionMiddleware) handleCachedResponse(c *gin.Context, data interface{}) {
	c.Header("X-Cache", "HIT")
	c.JSON(http.StatusOK, data)
}

// handleCircuitBreakerOpen handles circuit breaker open state
func (pm *ProtectionMiddleware) handleCircuitBreakerOpen(c *gin.Context) {
	// Return degraded response
	c.JSON(http.StatusServiceUnavailable, gin.H{
		"success":  false,
		"message":  "Service temporarily unavailable, please try again later",
		"fallback": true,
	})
}

// Circuit Breaker methods

// isOpen checks if circuit breaker is open
func (cb *CircuitBreaker) isOpen() bool {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state == "open"
}

// recordResponse records response time
func (cb *CircuitBreaker) recordResponse(responseTime time.Duration) {
	if !cb.enabled {
		return
	}

	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	if responseTime > cb.maxResponseTime {
		cb.failureCount++
		cb.lastFailureTime = time.Now()
	} else {
		cb.failureCount = 0
	}

	// Check if circuit breaker needs to be opened
	if cb.failureCount >= cb.recoveryThreshold && cb.state == "closed" {
		cb.state = "open"
		global.GVA_LOG.Warn("Circuit breaker opened due to high failure rate",
			zap.Int("failureCount", cb.failureCount),
			zap.Duration("responseTime", responseTime))
	}
}

// monitor monitors system resources
func (cb *CircuitBreaker) monitor() {
	ticker := time.NewTicker(cb.checkInterval)
	defer ticker.Stop()

	for range ticker.C {
		cb.checkSystemHealth()
	}
}

// checkSystemHealth checks system health status
func (cb *CircuitBreaker) checkSystemHealth() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	// Check memory usage
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	memoryUsage := float64(m.Alloc) / float64(m.Sys)

	// Check CPU usage (simplified version)
	cpuUsage := getCPUUsage()

	// If system resource usage is too high, open circuit breaker
	if memoryUsage > cb.maxMemoryUsage || cpuUsage > cb.maxCPUUsage {
		if cb.state == "closed" {
			cb.state = "open"
			global.GVA_LOG.Warn("Circuit breaker opened due to high resource usage",
				zap.Float64("memoryUsage", memoryUsage),
				zap.Float64("cpuUsage", cpuUsage))
		}
	} else {
		// If system resource usage is normal, try to recover
		if cb.state == "open" && time.Since(cb.lastFailureTime) > cb.checkInterval*2 {
			cb.state = "half-open"
			cb.failureCount = 0
			global.GVA_LOG.Info("Circuit breaker moved to half-open state")
		}
	}
}

// getCPUUsage gets CPU usage (simplified version)
func getCPUUsage() float64 {
	// This is a simplified CPU usage calculation
	// In production environment, it is recommended to use more precise CPU monitoring library
	return 0.0 // Temporarily return 0, can implement more precise CPU monitoring as needed
}
