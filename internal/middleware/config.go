package middleware

import (
	"time"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
)

// ParseProtectionConfig parses protection mechanism configuration
func ParseProtectionConfig(cfg config.Protection) *ProtectionConfig {
	protectionConfig := &ProtectionConfig{}

	// Parse rate limiting configuration
	protectionConfig.RateLimit.Enabled = cfg.RateLimit.Enabled
	protectionConfig.RateLimit.Requests = cfg.RateLimit.Requests
	protectionConfig.RateLimit.Window = parseDuration(cfg.RateLimit.Window, time.Minute)
	protectionConfig.RateLimit.SkipPaths = cfg.RateLimit.SkipPaths

	// Parse cache configuration
	protectionConfig.Cache.Enabled = cfg.Cache.Enabled
	protectionConfig.Cache.TTL = parseDuration(cfg.Cache.TTL, 30*time.Second)
	protectionConfig.Cache.MaxSize = cfg.Cache.MaxSize
	protectionConfig.Cache.SkipPaths = cfg.Cache.SkipPaths
	protectionConfig.Cache.SkipMethods = cfg.Cache.SkipMethods

	// Parse circuit breaker configuration
	protectionConfig.CircuitBreaker.Enabled = cfg.CircuitBreaker.Enabled
	protectionConfig.CircuitBreaker.MaxMemoryUsage = cfg.CircuitBreaker.MaxMemoryUsage
	protectionConfig.CircuitBreaker.MaxCPUUsage = cfg.CircuitBreaker.MaxCPUUsage
	protectionConfig.CircuitBreaker.MaxResponseTime = parseDuration(cfg.CircuitBreaker.MaxResponseTime, 5*time.Second)
	protectionConfig.CircuitBreaker.CheckInterval = parseDuration(cfg.CircuitBreaker.CheckInterval, 10*time.Second)
	protectionConfig.CircuitBreaker.RecoveryThreshold = cfg.CircuitBreaker.RecoveryThreshold

	return protectionConfig
}

// parseDuration parses time string, returns default value if parsing fails
func parseDuration(s string, defaultDuration time.Duration) time.Duration {
	if s == "" {
		return defaultDuration
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return defaultDuration
	}

	return duration
}
