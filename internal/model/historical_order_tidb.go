package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// HistoricalOrderTiDB represents the historical_orders table structure in TiDB
// Indexes:
// - PRIMARY KEY: id
// - UNIQUE: uq_user_order (user_address, oid)
// - INDEX: idx_historical_orders_date_user (trade_date, user_address)
// - INDEX: idx_historical_orders_timestamp (timestamp)
// - INDEX: idx_historical_orders_user_timestamp (user_address, timestamp)
// - INDEX: idx_historical_orders_coin_timestamp (coin, timestamp)
// - INDEX: idx_historical_orders_status (status)
type HistoricalOrderTiDB struct {
	ID          int64           `gorm:"column:id;type:bigint;primaryKey;autoIncrement" json:"id"`
	UserAddress *string         `gorm:"column:user_address;type:varchar(42);uniqueIndex:uq_user_order,priority:1;index:idx_historical_orders_date_user,priority:2;index:idx_historical_orders_user_timestamp,priority:1" json:"user_address"`
	Coin        string          `gorm:"column:coin;type:varchar(20);not null;index:idx_historical_orders_coin_timestamp,priority:1" json:"coin"`
	LimitPx     decimal.Decimal `gorm:"column:limit_px;type:decimal(20,8);not null;comment:限价价格" json:"limit_px"`
	Oid         int64           `gorm:"column:oid;type:bigint;not null;uniqueIndex:uq_user_order,priority:2;comment:订单ID" json:"oid"`
	Side        string          `gorm:"column:side;type:varchar(1);not null;comment:交易方向 B/A" json:"side"`
	Sz          decimal.Decimal `gorm:"column:sz;type:decimal(20,8);not null;comment:订单大小" json:"sz"`
	Timestamp   int64           `gorm:"column:timestamp;type:bigint;not null;comment:时间戳(毫秒);index:idx_historical_orders_timestamp;index:idx_historical_orders_user_timestamp,priority:2;index:idx_historical_orders_coin_timestamp,priority:2" json:"timestamp"`
	Status      string          `gorm:"column:status;type:varchar(20);not null;comment:订单状态;index:idx_historical_orders_status" json:"status"`
	AvgPx       decimal.Decimal `gorm:"column:avg_px;type:decimal(20,8);comment:平均成交价格" json:"avg_px"`
	FilledSz    decimal.Decimal `gorm:"column:filled_sz;type:decimal(20,8);comment:已成交数量" json:"filled_sz"`
	TradeDate   string          `gorm:"column:trade_date;type:varchar(10);not null;comment:交易日期 YYYY-MM-DD;index:idx_historical_orders_date_user,priority:1" json:"trade_date"`
	ProcessedAt *time.Time      `gorm:"column:processed_at;type:datetime;default:CURRENT_TIMESTAMP;comment:处理时间" json:"processed_at"`
}

// TableName specifies the table name for GORM
func (HistoricalOrderTiDB) TableName() string {
	return "historical_orders"
}

// BeforeCreate hook to set default values before creating a record
func (h *HistoricalOrderTiDB) BeforeCreate(tx *gorm.DB) error {
	if h.ProcessedAt == nil {
		now := time.Now()
		h.ProcessedAt = &now
	}
	return nil
}

