package model

import (
	"encoding/json"
	"time"
)

// UserFillData represents the essential trading data for OHLCV processing
// Optimized to contain only the 6 fields needed for kline calculation
type UserFillData struct {
	Index int    `json:"index"` // Trade index
	Coin  string `json:"coin"`  // Symbol name
	Px    string `json:"px"`    // Price as string to preserve precision
	Sz    string `json:"sz"`    // Size as string to preserve precision
	Time  int64  `json:"time"`  // Unix timestamp in milliseconds
	Hash  string `json:"hash"`  // Transaction hash
}

// RawWebSocketMessage represents a raw WebSocket message for immediate NATS publishing
// This bypasses processing to minimize latency
type RawWebSocketMessage struct {
	MessageID string    `json:"message_id"` // Unique message identifier
	Timestamp time.Time `json:"timestamp"`  // Receipt timestamp
	Source    string    `json:"source"`     // Always "websocket"
	RawData   []byte    `json:"raw_data"`   // Raw WebSocket message bytes
}

// NATS subjects for different message types
const (
	SubjectRawUserFills = "xbit.kline.raw_user_fills" // For immediate raw message publishing
	StreamKline         = "xbit-kline"
)

// CreateRawWebSocketMessage creates a new raw WebSocket message for immediate publishing
func CreateRawWebSocketMessage(rawData []byte) *RawWebSocketMessage {
	return &RawWebSocketMessage{
		MessageID: generateMessageID(),
		Timestamp: time.Now(),
		Source:    "websocket",
		RawData:   rawData,
	}
}

// ToJSON converts the raw message to JSON bytes
func (m *RawWebSocketMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON creates a RawWebSocketMessage from JSON bytes
func (m *RawWebSocketMessage) FromJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

// generateMessageID generates a unique message ID
func generateMessageID() string {
	// Use timestamp + random component for uniqueness
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of given length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
