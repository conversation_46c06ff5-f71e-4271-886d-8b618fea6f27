package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// NodeFillTiDB represents the node_fills table structure in TiDB
// Indexes:
// - PRIMARY KEY: id
// - UNIQUE: uq_user_trade (user_address, hash, tid)
// - INDEX: idx_node_fills_date_user (trade_date, user_address)
// - INDEX: idx_node_fills_time (time)
// - INDEX: idx_node_fills_user_time (user_address, time)
// - INDEX: idx_node_fills_coin_time (coin, time)
// - INDEX: idx_node_fills_coin_time_direction (coin, time, direction)
// - INDEX: idx_node_fills_direction (direction)
// - INDEX: idx_node_fills_time_direction (time, direction)
type NodeFillTiDB struct {
	ID            int64           `gorm:"column:id;type:bigint;primaryKey;autoIncrement" json:"id"`
	UserAddress   *string         `gorm:"column:user_address;type:varchar(42);uniqueIndex:uq_user_trade,priority:1;index:idx_node_fills_date_user,priority:2;index:idx_node_fills_user_time,priority:1" json:"user_address"`
	Hash          *string         `gorm:"column:hash;type:varchar(66);uniqueIndex:uq_user_trade,priority:2" json:"hash"`
	Tid           *int64          `gorm:"column:tid;type:bigint;uniqueIndex:uq_user_trade,priority:3" json:"tid"`
	Coin          string          `gorm:"column:coin;type:varchar(20);not null;index:idx_node_fills_coin_time,priority:1;index:idx_node_fills_coin_time_direction,priority:1" json:"coin"`
	Px            decimal.Decimal `gorm:"column:px;type:decimal(20,8);not null" json:"px"`
	Sz            decimal.Decimal `gorm:"column:sz;type:decimal(20,8);not null" json:"sz"`
	Side          string          `gorm:"column:side;type:varchar(1);not null;comment:交易方向 B/S" json:"side"`
	Time          int64           `gorm:"column:time;type:bigint;not null;comment:时间戳(毫秒);index:idx_node_fills_time;index:idx_node_fills_user_time,priority:2;index:idx_node_fills_coin_time,priority:2;index:idx_node_fills_coin_time_direction,priority:2;index:idx_node_fills_time_direction,priority:1" json:"time"`
	StartPosition decimal.Decimal `gorm:"column:start_position;type:decimal(65,18);not null;comment:起始仓位" json:"start_position"`
	Direction     string          `gorm:"column:direction;type:varchar(50);not null;comment:操作方向;index:idx_node_fills_direction;index:idx_node_fills_time_direction,priority:2;index:idx_node_fills_coin_time_direction,priority:3" json:"direction"`
	ClosedPnl     decimal.Decimal `gorm:"column:closed_pnl;type:decimal(65,18);not null;comment:已实现盈亏" json:"closed_pnl"`
	Oid           int64           `gorm:"column:oid;type:bigint;not null;comment:订单ID" json:"oid"`
	Crossed       bool            `gorm:"column:crossed;type:tinyint(1);not null;comment:是否跨价格" json:"crossed"`
	Fee           decimal.Decimal `gorm:"column:fee;type:decimal(65,18);not null;comment:手续费" json:"fee"`
	FeeToken      string          `gorm:"column:fee_token;type:varchar(10);not null;comment:手续费币种" json:"fee_token"`
	TradeDate     string          `gorm:"column:trade_date;type:varchar(10);not null;comment:交易日期 YYYY-MM-DD;index:idx_node_fills_date_user,priority:1" json:"trade_date"`
	TradeType     string          `gorm:"column:trade_type;type:varchar(10);not null;comment:交易类型: perpetual/spot" json:"trade_type"`
	ProcessedAt   *time.Time      `gorm:"column:processed_at;type:datetime;default:CURRENT_TIMESTAMP;comment:处理时间" json:"processed_at"`
}

// TableName specifies the table name for GORM
func (NodeFillTiDB) TableName() string {
	return "node_fills"
}

// BeforeCreate hook to set default values before creating a record
func (n *NodeFillTiDB) BeforeCreate(tx *gorm.DB) error {
	if n.ProcessedAt == nil {
		now := time.Now()
		n.ProcessedAt = &now
	}
	return nil
}
