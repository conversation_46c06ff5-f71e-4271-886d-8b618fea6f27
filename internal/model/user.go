package model

import (
	"time"

	"github.com/google/uuid"
)

// User represents a user in the system
type User struct {
	ID          uuid.UUID  `json:"id"`
	Email       *string    `json:"email"`
	Name        string     `json:"name"`
	Status      string     `json:"status"`
	UserAddress *string    `json:"user_address"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at"`
}

func (User) TableName() string {
	return "users"
}

// GenerateID generates a UUID if not provided
func (u *User) GenerateID() {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
}
