package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// NodeFill represents a trade fill record
// This matches the Hyperliquid exchange data structure
type NodeFill struct {
	ID            int64           `json:"id"`
	UserAddress   string          `json:"user_address"`
	Hash          string          `json:"hash"`
	TID           int64           `json:"tid"`  // Trade ID from exchange
	Coin          string          `json:"coin"` // Symbol
	Px            decimal.Decimal `json:"px"`   // Price
	Sz            decimal.Decimal `json:"sz"`   // Size/Quantity
	Side          string          `json:"side"` // "B" for buy, "A" for ask/sell
	Time          int64           `json:"time"` // Unix timestamp in milliseconds
	StartPosition decimal.Decimal `json:"start_position"`
	Direction     string          `json:"direction"`
	ClosedPnl     decimal.Decimal `json:"closed_pnl"`
	OID           int64           `json:"oid"` // Order ID
	Crossed       int             `json:"crossed"`
	Fee           decimal.Decimal `json:"fee"`
	FeeToken      string          `json:"fee_token"`
	TradeDate     string          `json:"trade_date"`
	TradeType     string          `json:"trade_type"` // "perpetual" or "spot"
	ProcessedAt   time.Time       `json:"processed_at"`
}

func (NodeFill) TableName() string {
	return "node_fills"
}

// GetExecutedTime converts the Unix millisecond timestamp to time.Time
func (nf *NodeFill) GetExecutedTime() time.Time {
	return time.Unix(nf.Time/1000, (nf.Time%1000)*1000000)
}

// GetQuoteVolume calculates the quote volume (price * quantity)
func (nf *NodeFill) GetQuoteVolume() decimal.Decimal {
	return nf.Px.Mul(nf.Sz)
}

// IsValidForKline checks if the fill has all required data for kline generation
func (nf *NodeFill) IsValidForKline() bool {
	return nf.Coin != "" &&
		!nf.Px.IsZero() &&
		!nf.Sz.IsZero() &&
		nf.Time > 0
}

// IsBuy returns true if this is a buy order
func (nf *NodeFill) IsBuy() bool {
	return nf.Side == "B"
}

// IsSell returns true if this is a sell order
func (nf *NodeFill) IsSell() bool {
	return nf.Side == "A"
}
