package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// OrderDetailTiDB represents the order_details table structure in TiDB
// Indexes:
// - PRIMARY KEY: id
// - UNIQUE: uq_user_order (user_address, oid)
// - INDEX: idx_order_details_coin (coin)
// - INDEX: idx_order_details_timestamp (order_timestamp)
// - INDEX: idx_order_details_user_coin (user_address, coin)
// - INDEX: idx_order_details_user_date (user_address, trade_date)
// - INDEX: idx_order_details_user_oid (user_address, oid)
type OrderDetailTiDB struct {
	ID               int64                `gorm:"column:id;type:bigint;primaryKey;autoIncrement" json:"id"`
	UserAddress      string               `gorm:"column:user_address;type:varchar(42);not null;uniqueIndex:uq_user_order,priority:1;index:idx_order_details_user_coin,priority:1;index:idx_order_details_user_date,priority:1;index:idx_order_details_user_oid,priority:1;comment:用户地址" json:"user_address"`
	Oid              int64                `gorm:"column:oid;type:bigint;not null;uniqueIndex:uq_user_order,priority:2;index:idx_order_details_user_oid,priority:2;comment:订单ID" json:"oid"`
	Coin             string               `gorm:"column:coin;type:varchar(20);not null;index:idx_order_details_coin;index:idx_order_details_user_coin,priority:2;comment:币种" json:"coin"`
	Side             string               `gorm:"column:side;type:varchar(1);not null;comment:交易方向 A/B" json:"side"`
	LimitPx          decimal.Decimal      `gorm:"column:limit_px;type:decimal(65,18);not null;comment:限价" json:"limit_px"`
	OrigSz           decimal.Decimal      `gorm:"column:orig_sz;type:decimal(65,18);not null;comment:原始订单数量" json:"orig_sz"`
	TriggerCondition string               `gorm:"column:trigger_condition;type:varchar(50);not null;comment:触发条件" json:"trigger_condition"`
	IsTrigger        bool                 `gorm:"column:is_trigger;type:tinyint(1);not null;comment:是否为触发单" json:"is_trigger"`
	TriggerPx        decimal.Decimal      `gorm:"column:trigger_px;type:decimal(65,18);not null;comment:触发价格" json:"trigger_px"`
	OrderType        string               `gorm:"column:order_type;type:varchar(20);not null;comment:订单类型" json:"order_type"`
	Tif              string               `gorm:"column:tif;type:varchar(30);not null;comment:订单有效期类型" json:"tif"`
	ReduceOnly       bool                 `gorm:"column:reduce_only;type:tinyint(1);not null;comment:是否只减仓" json:"reduce_only"`
	IsPositionTpsl   bool                 `gorm:"column:is_position_tpsl;type:tinyint(1);not null;comment:是否为仓位止盈止损" json:"is_position_tpsl"`
	Cloid            *string              `gorm:"column:cloid;type:varchar(100);comment:客户端订单ID（可选）" json:"cloid,omitempty"`
	Children         *OrderDetailChildren `gorm:"column:children;type:json;comment:子订单列表（JSON格式的OrderDetail数组）" json:"children,omitempty"`
	OrderTimestamp   int64                `gorm:"column:order_timestamp;type:bigint;not null;index:idx_order_details_timestamp;comment:订单创建时间戳(毫秒)" json:"order_timestamp"`
	TradeDate        string               `gorm:"column:trade_date;type:varchar(10);not null;index:idx_order_details_user_date,priority:2;comment:交易日期 YYYY-MM-DD" json:"trade_date"`
	CreatedAt        time.Time            `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`
}

// TableName specifies the table name for GORM
func (OrderDetailTiDB) TableName() string {
	return "order_details"
}

// BeforeCreate hook to set default values before creating a record
func (o *OrderDetailTiDB) BeforeCreate(tx *gorm.DB) error {
	if o.CreatedAt.IsZero() {
		o.CreatedAt = time.Now()
	}
	return nil
}

// OrderDetailChildren represents the children field as JSON
type OrderDetailChildren []OrderDetailChild

// OrderDetailChild represents a child order detail
type OrderDetailChild struct {
	Oid              int64           `json:"oid"`
	Coin             string          `json:"coin"`
	Side             string          `json:"side"`
	LimitPx          decimal.Decimal `json:"limit_px"`
	OrigSz           decimal.Decimal `json:"orig_sz"`
	TriggerCondition string          `json:"trigger_condition"`
	IsTrigger        bool            `json:"is_trigger"`
	TriggerPx        decimal.Decimal `json:"trigger_px"`
	OrderType        string          `json:"order_type"`
	Tif              string          `json:"tif"`
	ReduceOnly       bool            `json:"reduce_only"`
	IsPositionTpsl   bool            `json:"is_position_tpsl"`
	Cloid            *string         `json:"cloid,omitempty"`
}

// Value implements the driver.Valuer interface for JSON encoding
func (c OrderDetailChildren) Value() (driver.Value, error) {
	if c == nil || len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

// Scan implements the sql.Scanner interface for JSON decoding
func (c *OrderDetailChildren) Scan(value interface{}) error {
	if value == nil {
		*c = nil
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return json.Unmarshal([]byte(""), c)
	}
	return json.Unmarshal(bytes, c)
}
