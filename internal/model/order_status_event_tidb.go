package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// OrderStatusEventTiDB represents the order_status_events table structure in TiDB
// Indexes:
// - PRIMARY KEY: id
// - UNIQUE: uq_order_status_event (oid, status, event_timestamp)
// - INDEX: idx_status_events_category (status_category)
// - INDEX: idx_status_events_hash (hash)
// - INDEX: idx_status_events_oid_timestamp (oid, event_timestamp)
// - INDEX: idx_status_events_parsed_time (parsed_time)
// - INDEX: idx_status_events_status (status)
// - INDEX: idx_status_events_timestamp (event_timestamp)
// - INDEX: idx_status_events_user_oid (user_address, oid)
// - INDEX: idx_status_events_user_parsed_time (user_address, parsed_time)
// - INDEX: idx_status_events_user_status (user_address, status)
// - INDEX: idx_status_events_user_time (user_address, event_timestamp)
type OrderStatusEventTiDB struct {
	ID             int64           `gorm:"column:id;type:bigint;primaryKey;autoIncrement" json:"id"`
	UserAddress    string          `gorm:"column:user_address;type:varchar(42);not null;index:idx_status_events_user_oid,priority:1;index:idx_status_events_user_parsed_time,priority:1;index:idx_status_events_user_status,priority:1;index:idx_status_events_user_time,priority:1;comment:用户地址" json:"user_address"`
	Oid            int64           `gorm:"column:oid;type:bigint;not null;uniqueIndex:uq_order_status_event,priority:1;index:idx_status_events_oid_timestamp,priority:1;index:idx_status_events_user_oid,priority:2;comment:订单ID" json:"oid"`
	Status         string          `gorm:"column:status;type:varchar(50);not null;uniqueIndex:uq_order_status_event,priority:2;index:idx_status_events_status;index:idx_status_events_user_status,priority:2;comment:订单状态" json:"status"`
	StatusTime     string          `gorm:"column:status_time;type:varchar(50);not null;comment:状态更新时间" json:"status_time"`
	EventTimestamp int64           `gorm:"column:event_timestamp;type:bigint;not null;uniqueIndex:uq_order_status_event,priority:3;index:idx_status_events_timestamp;index:idx_status_events_oid_timestamp,priority:2;index:idx_status_events_user_time,priority:2;comment:事件时间戳(毫秒)" json:"event_timestamp"`
	Hash           *string         `gorm:"column:hash;type:varchar(66);index:idx_status_events_hash;comment:交易哈希（可选）" json:"hash,omitempty"`
	FilledSz       decimal.Decimal `gorm:"column:filled_sz;type:decimal(65,18);not null;comment:已成交数量" json:"filled_sz"`
	StatusCategory string          `gorm:"column:status_category;type:varchar(20);not null;index:idx_status_events_category;comment:状态分类: active/completed/rejected/cancelled" json:"status_category"`
	ParsedTime     *time.Time      `gorm:"column:parsed_time;type:datetime;index:idx_status_events_parsed_time;index:idx_status_events_user_parsed_time,priority:2;comment:解析后的状态时间" json:"parsed_time,omitempty"`
	CreatedAt      time.Time       `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`
}

// TableName specifies the table name for GORM
func (OrderStatusEventTiDB) TableName() string {
	return "order_status_events"
}

// BeforeCreate hook to set default values before creating a record
func (o *OrderStatusEventTiDB) BeforeCreate(tx *gorm.DB) error {
	if o.CreatedAt.IsZero() {
		o.CreatedAt = time.Now()
	}
	return nil
}

