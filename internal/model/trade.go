package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// Trade represents a trading transaction
type Trade struct {
	ID         uuid.UUID       `json:"id"`
	UserID     uuid.UUID       `json:"user_id"`
	Symbol     string          `json:"symbol"`
	Side       string          `json:"side"` // buy, sell
	Type       string          `json:"type"` // market, limit, stop
	Quantity   decimal.Decimal `json:"quantity"`
	Price      decimal.Decimal `json:"price"`
	FilledQty  decimal.Decimal `json:"filled_qty"`
	Status     string          `json:"status"`
	ExecutedAt *time.Time      `json:"executed_at"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
	DeletedAt  *time.Time      `json:"deleted_at"`

	// Relationships
	User *User `json:"user,omitempty"`
}

func (Trade) TableName() string {
	return "trades"
}

// GenerateID generates a UUID if not provided
func (t *Trade) GenerateID() {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
}
