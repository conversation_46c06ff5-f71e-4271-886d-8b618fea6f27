package service

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestOptimizedNATSFlow tests the complete optimized NATS flow from WebSocket to OHLCV processing
func TestOptimizedNATSFlow(t *testing.T) {
	// Create a mock WebSocket message in the user_fills format
	originalMessage := map[string]interface{}{
		"type": "user_fills",
		"data": []interface{}{
			[]interface{}{478, "DOGE", "0.21041", "5000.0", float64(1760344053318), "0x4026cd626f8f07b141a0042d62f9200205f000480a822683e3ef78b52e82e19b"},
		},
		"timestamp":     int64(1760344053318), // Changed from string to int64
		"batch_index":   5685327,
		"message_index": 0,
	}

	// Convert to JSON bytes (simulating WebSocket input)
	originalData, err := json.Marshal(originalMessage)
	require.NoError(t, err)

	t.Logf("Original message size: %d bytes", len(originalData))

	// NEW APPROACH: Raw message publishing (simulating PublishOptimizedUserFill logic)
	// The optimized publisher now publishes the entire raw WebSocket message for ultra-low latency
	rawPublishedData := originalData // Raw data is published as-is

	t.Logf("Raw published message size: %d bytes", len(rawPublishedData))
	t.Logf("Publishing approach: zero-copy raw publish (no JSON processing)")

	// Test the consumer parsing of raw user_fills message
	natsConsumer := &NATSConsumerService{}

	// Extract trades from the raw user_fills message
	tradesBySymbol, err := natsConsumer.extractTradesFromRawUserFillsMessage(rawPublishedData)
	require.NoError(t, err)
	require.Len(t, tradesBySymbol, 1) // Should have one symbol

	// Get the DOGE trades
	dogeTrades, exists := tradesBySymbol["DOGE"]
	require.True(t, exists)
	require.Len(t, dogeTrades, 1) // Should have one trade

	trade := dogeTrades[0]
	symbol := "DOGE"

	// Verify extracted trade data
	assert.Equal(t, "DOGE", symbol)
	assert.Equal(t, 478, trade.Index)
	assert.Equal(t, 0.21041, trade.Price)
	assert.Equal(t, 5000.0, trade.Volume) // This is actually the size, not price*size
	assert.Equal(t, time.UnixMilli(1760344053318), trade.Timestamp)

	t.Logf("Successfully extracted trade data:")
	t.Logf("  Symbol: %s", symbol)
	t.Logf("  Index: %d", trade.Index)
	t.Logf("  Price: %.5f", trade.Price)
	t.Logf("  Volume: %.2f", trade.Volume)
	t.Logf("  Timestamp: %v", trade.Timestamp)
}

// TestOptimizedVsOriginalFormat compares the size difference between formats
func TestOptimizedVsOriginalFormat(t *testing.T) {
	testCases := []struct {
		name string
		data []interface{}
	}{
		{
			name: "BTC_trade",
			data: []interface{}{1, "BTC", "50000.00", "0.1", float64(time.Now().UnixMilli()), "0xabc123"},
		},
		{
			name: "ETH_trade",
			data: []interface{}{2, "ETH", "3000.50", "2.5", float64(time.Now().UnixMilli()), "0xdef456"},
		},
		{
			name: "DOGE_trade",
			data: []interface{}{3, "DOGE", "0.21041", "5000.0", float64(time.Now().UnixMilli()), "0x789xyz"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Original format
			originalMessage := map[string]interface{}{
				"type":          "user_fills",
				"data":          []interface{}{tc.data}, // Wrap in array for user_fills format
				"timestamp":     time.Now().UnixMilli(), // Changed from string to int64
				"batch_index":   12345,
				"message_index": 0,
			}
			originalData, _ := json.Marshal(originalMessage)

			// Optimized format (just the array)
			optimizedData, _ := json.Marshal(tc.data)

			sizeDiff := len(originalData) - len(optimizedData)
			reductionPercent := float64(sizeDiff) / float64(len(originalData)) * 100

			t.Logf("Original size: %d bytes", len(originalData))
			t.Logf("Optimized size: %d bytes", len(optimizedData))
			t.Logf("Size reduction: %d bytes (%.2f%%)", sizeDiff, reductionPercent)

			// Verify we're getting significant size reduction
			assert.Greater(t, reductionPercent, 50.0, "Should achieve at least 50% size reduction")
		})
	}
}

// TestOptimizedMessageValidation tests validation of the optimized message format
func TestOptimizedMessageValidation(t *testing.T) {
	natsConsumer := &NATSConsumerService{}

	testCases := []struct {
		name        string
		data        interface{}
		shouldError bool
		errorMsg    string
	}{
		{
			name:        "Valid_trade",
			data:        []interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			shouldError: false,
		},
		{
			name:        "Invalid_too_few_fields",
			data:        []interface{}{1, "BTC", "50000.00", "0.1"},
			shouldError: true,
			errorMsg:    "expected 6 fields, got 4",
		},
		{
			name:        "Invalid_too_many_fields",
			data:        []interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123", "extra"},
			shouldError: true,
			errorMsg:    "expected 6 fields, got 7",
		},
		{
			name:        "Invalid_index_type",
			data:        []interface{}{"invalid", "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			shouldError: true,
			errorMsg:    "invalid index field",
		},
		{
			name:        "Invalid_coin_type",
			data:        []interface{}{1, 123, "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			shouldError: true,
			errorMsg:    "invalid coin field",
		},
		{
			name:        "Invalid_price_format",
			data:        []interface{}{1, "BTC", "invalid_price", "0.1", float64(1760344053318), "0xabc123"},
			shouldError: true,
			errorMsg:    "invalid price",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			messageData, err := json.Marshal(tc.data)
			require.NoError(t, err)

			trade, symbol, err := natsConsumer.extractTradeDataFromOptimizedMessage(messageData)

			if tc.shouldError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
				assert.Empty(t, symbol)
				assert.Equal(t, TradeData{}, trade)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, symbol)
				assert.NotEqual(t, TradeData{}, trade)
			}
		})
	}
}
