package service

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// BackfillJobService manages all OHLCV backfill jobs
type BackfillJobService struct {
	metricsService *BackfillMetricsService
	config         config.Backfill
}

// NewBackfillJobService creates a new backfill job service
func NewBackfillJobService() *BackfillJobService {
	return &BackfillJobService{
		metricsService: NewBackfillMetricsService(),
		config:         global.GVA_CONFIG.Backfill,
	}
}

// timeframeMap maps string timeframes to model timeframes
var timeframeMap = map[string]model.KlineTimeframe{
	"1m":  model.Timeframe1m,
	"3m":  model.Timeframe3m,
	"5m":  model.Timeframe5m,
	"15m": model.Timeframe15m,
	"30m": model.Timeframe30m,
	"1h":  model.Timeframe1h,
	"2h":  model.Timeframe2h,
	"4h":  model.Timeframe4h,
	"8h":  model.Timeframe8h,
	"12h": model.Timeframe12h,
	"1d":  model.Timeframe1d,
	"3d":  model.Timeframe3d,
	"1w":  model.Timeframe1w,
	"1mo": model.Timeframe1mo,
}

// ExecuteBackfillJob executes a backfill job for a specific timeframe
func (s *BackfillJobService) ExecuteBackfillJob(jobID string) error {
	if !s.config.Enabled {
		global.GVA_LOG.Debug("Backfill jobs are disabled", zap.String("job_id", jobID))
		return nil
	}

	// Find the job configuration
	var jobConfig *config.BackfillJob
	for _, job := range s.config.Jobs {
		if job.ID == jobID {
			jobConfig = &job
			break
		}
	}

	if jobConfig == nil {
		return fmt.Errorf("backfill job not found: %s", jobID)
	}

	if !jobConfig.Enabled {
		global.GVA_LOG.Debug("Backfill job is disabled", zap.String("job_id", jobID))
		return nil
	}

	// Convert string timeframe to model timeframe
	timeframe, exists := timeframeMap[jobConfig.Timeframe]
	if !exists {
		return fmt.Errorf("unsupported timeframe: %s", jobConfig.Timeframe)
	}

	global.GVA_LOG.Info("Starting backfill job execution",
		zap.String("job_id", jobID),
		zap.String("timeframe", jobConfig.Timeframe),
		zap.Int("candle_count", jobConfig.CandleCount))

	// Record job start
	s.metricsService.RecordJobStart(jobID, jobConfig.Timeframe)

	// Parse timeout for individual operations (not the entire job)
	operationTimeout, err := time.ParseDuration(s.config.Timeout)
	if err != nil {
		operationTimeout = 30 * time.Second // Default timeout
		global.GVA_LOG.Warn("Failed to parse timeout, using default",
			zap.String("timeout_config", s.config.Timeout),
			zap.Duration("default_timeout", operationTimeout))
	}

	// Create context without timeout for the entire job (let it run as long as needed)
	// Individual operations will have their own timeouts
	ctx := context.Background()

	// Create backfill configuration
	backfillConfig := BackfillConfig{
		CandleCount: jobConfig.CandleCount,
		Timeout:     operationTimeout,
	}

	// Create job-specific backfill service with proxy assignment
	backfillService := NewOHLCVBackfillServiceWithJobID(jobID)

	// Cleanup HTTP clients to prevent resource leaks
	defer backfillService.CleanupHTTPClients()

	// Execute the backfill
	startTime := time.Now()
	err = backfillService.BackfillTimeframe(ctx, timeframe, backfillConfig)
	duration := time.Since(startTime)

	if err != nil {
		// Record failure
		s.metricsService.RecordJobFailure(jobID, duration, err)

		global.GVA_LOG.Error("Backfill job failed",
			zap.String("job_id", jobID),
			zap.String("timeframe", jobConfig.Timeframe),
			zap.Duration("duration", duration),
			zap.Error(err))
		return fmt.Errorf("backfill job %s failed: %w", jobID, err)
	}

	// Record success (we don't have klines count here, so we'll use 0 for now)
	// TODO: Modify BackfillTimeframe to return klines count
	s.metricsService.RecordJobSuccess(jobID, duration, 0)

	global.GVA_LOG.Info("Backfill job completed successfully",
		zap.String("job_id", jobID),
		zap.String("timeframe", jobConfig.Timeframe),
		zap.Duration("duration", duration))

	return nil
}

// GetEnabledJobs returns all enabled backfill jobs
func (s *BackfillJobService) GetEnabledJobs() []config.BackfillJob {
	if !s.config.Enabled {
		return []config.BackfillJob{}
	}

	var enabledJobs []config.BackfillJob
	for _, job := range s.config.Jobs {
		if job.Enabled {
			enabledJobs = append(enabledJobs, job)
		}
	}

	return enabledJobs
}

// ValidateConfiguration validates the backfill configuration
func (s *BackfillJobService) ValidateConfiguration() error {
	if !s.config.Enabled {
		return nil
	}

	if len(s.config.Jobs) == 0 {
		return fmt.Errorf("no backfill jobs configured")
	}

	// Validate each job
	for _, job := range s.config.Jobs {
		if job.ID == "" {
			return fmt.Errorf("backfill job ID cannot be empty")
		}

		if job.Timeframe == "" {
			return fmt.Errorf("backfill job timeframe cannot be empty for job %s", job.ID)
		}

		if _, exists := timeframeMap[job.Timeframe]; !exists {
			return fmt.Errorf("unsupported timeframe %s for job %s", job.Timeframe, job.ID)
		}

		if job.CandleCount <= 0 {
			return fmt.Errorf("candle count must be greater than 0 for job %s", job.ID)
		}

		if job.Cron == "" {
			return fmt.Errorf("cron expression cannot be empty for job %s", job.ID)
		}
	}

	// Validate proxy configuration if enabled
	if err := s.validateProxyConfiguration(); err != nil {
		return fmt.Errorf("proxy configuration validation failed: %w", err)
	}

	global.GVA_LOG.Info("Backfill configuration validation passed",
		zap.Int("total_jobs", len(s.config.Jobs)),
		zap.Int("enabled_jobs", len(s.GetEnabledJobs())))

	return nil
}

// validateProxyConfiguration validates the proxy configuration
func (s *BackfillJobService) validateProxyConfiguration() error {
	if !s.config.Proxies.Enabled {
		return nil // Proxy configuration is optional
	}

	// Validate proxy URLs
	if len(s.config.Proxies.URLs) == 0 {
		return fmt.Errorf("proxy is enabled but no proxy URLs configured")
	}

	// Validate each proxy URL format
	for i, proxyURL := range s.config.Proxies.URLs {
		if proxyURL == "" {
			return fmt.Errorf("proxy URL at index %d is empty", i)
		}

		// Parse URL to validate format
		parsedURL, err := url.Parse(proxyURL)
		if err != nil {
			return fmt.Errorf("invalid proxy URL at index %d (%s): %w", i, proxyURL, err)
		}

		// Check if it's HTTP or HTTPS
		if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
			return fmt.Errorf("proxy URL at index %d must use http or https scheme: %s", i, proxyURL)
		}

		// Check if host is present
		if parsedURL.Host == "" {
			return fmt.Errorf("proxy URL at index %d missing host: %s", i, proxyURL)
		}
	}

	// Validate concurrency setting
	if s.config.Proxies.Concurrency < 0 {
		return fmt.Errorf("proxy concurrency cannot be negative: %d", s.config.Proxies.Concurrency)
	}

	// Validate rotation strategy
	validRotations := map[string]bool{
		"round-robin": true,
		"random":      true,
	}
	if s.config.Proxies.Rotation != "" && !validRotations[s.config.Proxies.Rotation] {
		return fmt.Errorf("invalid proxy rotation strategy: %s (valid: round-robin, random)", s.config.Proxies.Rotation)
	}

	// Validate health check configuration if enabled
	if s.config.Proxies.HealthCheck.Enabled {
		if s.config.Proxies.HealthCheck.Interval == "" {
			return fmt.Errorf("health check interval cannot be empty when health check is enabled")
		}

		if _, err := time.ParseDuration(s.config.Proxies.HealthCheck.Interval); err != nil {
			return fmt.Errorf("invalid health check interval: %w", err)
		}

		if s.config.Proxies.HealthCheck.Timeout == "" {
			return fmt.Errorf("health check timeout cannot be empty when health check is enabled")
		}

		if _, err := time.ParseDuration(s.config.Proxies.HealthCheck.Timeout); err != nil {
			return fmt.Errorf("invalid health check timeout: %w", err)
		}

		if s.config.Proxies.HealthCheck.URL != "" {
			if _, err := url.Parse(s.config.Proxies.HealthCheck.URL); err != nil {
				return fmt.Errorf("invalid health check URL: %w", err)
			}
		}
	}

	global.GVA_LOG.Info("Proxy configuration validation passed",
		zap.Int("proxy_count", len(s.config.Proxies.URLs)),
		zap.Int("concurrency", s.config.Proxies.Concurrency),
		zap.String("rotation", s.config.Proxies.Rotation),
		zap.Bool("health_check_enabled", s.config.Proxies.HealthCheck.Enabled))

	return nil
}

// CreateJobExecutor creates a job executor function for a specific job ID
func (s *BackfillJobService) CreateJobExecutor(jobID string) func() {
	return func() {
		if err := s.ExecuteBackfillJob(jobID); err != nil {
			global.GVA_LOG.Error("Backfill job execution failed",
				zap.String("job_id", jobID),
				zap.Error(err))
		}
	}
}

// GetJobByID returns a job configuration by ID
func (s *BackfillJobService) GetJobByID(jobID string) (*config.BackfillJob, error) {
	for _, job := range s.config.Jobs {
		if job.ID == jobID {
			return &job, nil
		}
	}
	return nil, fmt.Errorf("job not found: %s", jobID)
}

// ListAllJobs returns all configured backfill jobs with their status
func (s *BackfillJobService) ListAllJobs() []map[string]interface{} {
	var jobs []map[string]interface{}

	for _, job := range s.config.Jobs {
		jobInfo := map[string]interface{}{
			"id":             job.ID,
			"timeframe":      job.Timeframe,
			"candle_count":   job.CandleCount,
			"cron":           job.Cron,
			"enabled":        job.Enabled,
			"global_enabled": s.config.Enabled,
		}
		jobs = append(jobs, jobInfo)
	}

	return jobs
}

// GetJobStatistics returns statistics about the configured jobs
func (s *BackfillJobService) GetJobStatistics() map[string]interface{} {
	totalJobs := len(s.config.Jobs)
	enabledJobs := len(s.GetEnabledJobs())

	timeframes := make(map[string]int)
	for _, job := range s.config.Jobs {
		timeframes[job.Timeframe]++
	}

	return map[string]interface{}{
		"total_jobs":     totalJobs,
		"enabled_jobs":   enabledJobs,
		"disabled_jobs":  totalJobs - enabledJobs,
		"global_enabled": s.config.Enabled,
		"timeout":        s.config.Timeout,
		"timeframes":     timeframes,
	}
}

// GetMetricsService returns the metrics service
func (s *BackfillJobService) GetMetricsService() *BackfillMetricsService {
	return s.metricsService
}

// GetJobMetrics returns metrics for a specific job
func (s *BackfillJobService) GetJobMetrics(jobID string) (*JobExecutionMetrics, bool) {
	return s.metricsService.GetJobMetrics(jobID)
}

// GetAllMetrics returns all backfill metrics
func (s *BackfillJobService) GetAllMetrics() *BackfillMetrics {
	return s.metricsService.GetAllMetrics()
}

// GetMetricsSummary returns summary statistics
func (s *BackfillJobService) GetMetricsSummary() map[string]interface{} {
	return s.metricsService.GetSummaryStats()
}

// LogMetricsSummary logs a periodic summary of metrics
func (s *BackfillJobService) LogMetricsSummary() {
	s.metricsService.LogPeriodicSummary()
}

// PerformPeriodicCleanup performs periodic cleanup of metrics to prevent memory growth
func (s *BackfillJobService) PerformPeriodicCleanup() {
	global.GVA_LOG.Info("Starting periodic cleanup")

	// Perform metrics cleanup
	s.metricsService.PerformPeriodicCleanup()

	global.GVA_LOG.Info("Periodic cleanup completed")
}
