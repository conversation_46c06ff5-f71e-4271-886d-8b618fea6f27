package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// NATSConsumerService handles consuming messages from NATS and processing them
type NATSConsumerService struct {
	natsClient        *nats.Conn
	js                nats.JetStreamContext
	realtimeService   *RealtimeKlineService
	redisKlineService *RedisKlineService
	redisClient       *redis.Client
	subscription      *nats.Subscription
	batchSize         int
	maxWait           time.Duration
	processingStats   *ProcessingStats
	mu                sync.RWMutex

	// Optimization settings
	batchInterval time.Duration // How often to process batches (0.5s)
	maxBatchSize  int           // Maximum messages per batch
}

// ProcessingStats tracks processing statistics
type ProcessingStats struct {
	TotalProcessed   int64     `json:"total_processed"`
	TotalErrors      int64     `json:"total_errors"`
	LastProcessedAt  time.Time `json:"last_processed_at"`
	ProcessingRate   float64   `json:"processing_rate"` // messages per second
	AverageLatency   float64   `json:"average_latency"` // milliseconds
	BatchesProcessed int64     `json:"batches_processed"`
	AvgBatchSize     float64   `json:"avg_batch_size"`
	RedisOperations  int64     `json:"redis_operations"`
}

// BatchTradeData represents processed trade data for a single coin
type BatchTradeData struct {
	Symbol     string
	Trades     []TradeData
	TimeFrames map[model.KlineTimeframe]map[int64]*RedisKline // timeframe -> timestamp -> kline
}

// TradeData represents a single trade extracted from NATS message
type TradeData struct {
	BatchIndex int // Batch index from WebSocket message for proper cross-batch ordering
	Index      int // Transaction index within the batch for proper ordering
	Price      float64
	Volume     float64
	Timestamp  time.Time
}

// RedisKeyInfo represents information needed to construct Redis keys
type RedisKeyInfo struct {
	Symbol    string
	Timeframe model.KlineTimeframe
	Timestamp int64
	Key       string
}

// NewNATSConsumerService creates a new NATS consumer service
func NewNATSConsumerService() *NATSConsumerService {
	natsClient := initializer.GetNATS()
	realtimeService := NewRealtimeKlineService()
	redisKlineService := NewRedisKlineService()

	return &NATSConsumerService{
		natsClient:        natsClient.GetConn(),
		js:                natsClient.GetJetStream(),
		realtimeService:   realtimeService,
		redisKlineService: redisKlineService,
		redisClient:       global.GVA_REDIS,
		batchSize:         1000, // Process in batches for better performance
		maxWait:           500 * time.Millisecond,
		processingStats:   &ProcessingStats{},

		// Optimization settings for maximum performance
		batchInterval: 500 * time.Millisecond, // Process every 0.5 seconds
		maxBatchSize:  1000,                   // Maximum messages per batch
	}
}

// SetBatchSize sets the batch size for message processing
func (n *NATSConsumerService) SetBatchSize(size int) {
	n.batchSize = size
}

// SetMaxWait sets the maximum wait time for batching
func (n *NATSConsumerService) SetMaxWait(duration time.Duration) {
	n.maxWait = duration
}

// ensureStream creates the NATS stream if it doesn't exist
func (n *NATSConsumerService) ensureStream() error {
	// Check if stream exists
	_, err := n.js.StreamInfo(model.StreamKline)
	if err != nil {
		// Stream doesn't exist, create it
		streamConfig := &nats.StreamConfig{
			Name:        model.StreamKline,
			Description: "Stream for kline/OHLCV processing",
			Subjects:    []string{model.SubjectRawUserFills},
			MaxAge:      24 * time.Hour,     // 24 hours
			MaxBytes:    1024 * 1024 * 1024, // 1GB
			Storage:     nats.FileStorage,   // Explicit file storage for persistence
		}

		_, err = n.js.AddStream(streamConfig)
		if err != nil {
			global.GVA_LOG.Error("Failed to create NATS stream",
				zap.String("stream", model.StreamKline),
				zap.Error(err))
			return fmt.Errorf("failed to create stream: %w", err)
		}

		global.GVA_LOG.Info("Created NATS stream successfully",
			zap.String("stream", model.StreamKline))
	}

	return nil
}

// checkConsumerExists checks if the durable consumer already exists
func (n *NATSConsumerService) checkConsumerExists() (bool, error) {
	consumerInfo, err := n.js.ConsumerInfo(model.StreamKline, "kline-processor")
	if err != nil {
		if err == nats.ErrConsumerNotFound {
			return false, nil // Consumer doesn't exist, which is fine
		}
		return false, fmt.Errorf("failed to check consumer info: %w", err)
	}

	global.GVA_LOG.Info("Found existing durable consumer",
		zap.String("consumer", consumerInfo.Name),
		zap.Uint64("delivered", consumerInfo.Delivered.Consumer),
		zap.Int("ack_pending", consumerInfo.NumAckPending))

	return true, nil
}

// processMessages continuously processes messages from the NATS subscription using optimized batch processing
func (n *NATSConsumerService) processMessages(ctx context.Context) {
	ticker := time.NewTicker(n.batchInterval)
	defer ticker.Stop()

	var messageBatch []*nats.Msg

	for {
		select {
		case <-ctx.Done():
			global.GVA_LOG.Info("Stopping NATS message processing")
			// Process any remaining messages in batch before stopping
			if len(messageBatch) > 0 {
				n.processBatch(ctx, messageBatch)
			}
			return

		case <-ticker.C:
			// Process batch every 0.5 seconds
			if len(messageBatch) > 0 {
				n.processBatch(ctx, messageBatch)
				messageBatch = messageBatch[:0] // Reset batch
			}

		default:
			// Collect messages for batch processing
			msg, err := n.subscription.NextMsg(50 * time.Millisecond) // Short timeout for batch collection
			if err != nil {
				if err == nats.ErrTimeout {
					// Timeout is normal during batch collection
					continue
				}
				global.GVA_LOG.Error("Failed to get next message", zap.Error(err))
				time.Sleep(100 * time.Millisecond) // Brief pause before retrying
				continue
			}

			// Add message to batch
			messageBatch = append(messageBatch, msg)

			// Process immediately if batch is full
			if len(messageBatch) >= n.maxBatchSize {
				n.processBatch(ctx, messageBatch)
				messageBatch = messageBatch[:0] // Reset batch
			}
		}
	}
}

// StartProcessing starts processing messages from NATS
func (n *NATSConsumerService) StartProcessing(ctx context.Context) error {
	// Ensure stream exists (but don't create conflicting consumer)
	if err := n.ensureStream(); err != nil {
		return err
	}

	// Check if durable consumer already exists (for logging purposes)
	exists, err := n.checkConsumerExists()
	if err != nil {
		global.GVA_LOG.Warn("Failed to check existing consumer", zap.Error(err))
	} else if exists {
		global.GVA_LOG.Info("Reconnecting to existing durable consumer")
	} else {
		global.GVA_LOG.Info("Creating new durable consumer")
	}

	global.GVA_LOG.Info("Starting NATS message processing",
		zap.Int("batch_size", n.batchSize),
		zap.Duration("max_wait", n.maxWait))

	// Create durable queue subscription with proper consumer configuration
	sub, err := n.js.QueueSubscribeSync(
		model.SubjectRawUserFills,
		"kline-processors",
		nats.Durable("kline-processor"),
		nats.DeliverAll(),            // Explicit delivery policy for compatibility
		nats.AckExplicit(),           // Require explicit acknowledgment
		nats.MaxDeliver(3),           // Retry failed messages up to 3 times
		nats.AckWait(30*time.Second), // Wait 30 seconds for acknowledgment
	)
	if err != nil {
		return fmt.Errorf("failed to create queue subscription: %w", err)
	}

	n.subscription = sub

	// Start message processing in a goroutine
	go n.processMessages(ctx)

	global.GVA_LOG.Info("NATS queue subscription created successfully")
	return nil
}

// processBatch processes a batch of messages using optimized Redis operations
func (n *NATSConsumerService) processBatch(ctx context.Context, messages []*nats.Msg) {
	if len(messages) == 0 {
		return
	}

	startTime := time.Now()
	batchSize := len(messages)

	global.GVA_LOG.Debug("Processing message batch",
		zap.Int("batch_size", batchSize))

	// Step 1: Parse all messages and group by symbol
	tradesBySymbol, parseErrors := n.parseAndGroupMessages(messages)

	// Step 2: Batch read existing OHLCV data from Redis
	existingData, readErrors := n.batchReadOHLCVData(ctx, tradesBySymbol)

	// Step 3: Process all trades in memory
	updatedData := n.processTradesInMemory(tradesBySymbol, existingData)

	// Step 4: Batch write all updates to Redis
	writeErrors := n.batchWriteOHLCVData(ctx, updatedData)

	// Step 5: Acknowledge messages based on processing results
	totalErrors := parseErrors + readErrors + writeErrors
	successCount := batchSize - totalErrors

	n.acknowledgeMessages(messages, totalErrors == 0)

	// Update statistics
	duration := time.Since(startTime)
	n.updateBatchStats(successCount, totalErrors, duration, batchSize)

	global.GVA_LOG.Info("Batch processing completed",
		zap.Int("batch_size", batchSize),
		zap.Int("success_count", successCount),
		zap.Int("error_count", totalErrors),
		zap.Duration("duration", duration),
		zap.Float64("ops_per_sec", float64(batchSize)/duration.Seconds()))
}

// parseAndGroupMessages parses raw NATS messages and groups trades by symbol
func (n *NATSConsumerService) parseAndGroupMessages(messages []*nats.Msg) (map[string][]TradeData, int) {
	tradesBySymbol := make(map[string][]TradeData)
	errorCount := 0
	totalTrades := 0

	for _, msg := range messages {
		// Try to parse as raw user_fills message first (new format)
		trades, err := n.extractTradesFromRawUserFillsMessage(msg.Data)
		if err != nil {
			// Fallback: Try to parse as optimized single trade array (legacy format)
			trade, symbol, fallbackErr := n.extractTradeDataFromOptimizedMessage(msg.Data)
			if fallbackErr != nil {
				global.GVA_LOG.Error("Failed to extract trade data from message",
					zap.Error(err),
					zap.Error(fallbackErr))
				errorCount++
				continue
			}
			// Single trade from legacy format
			tradesBySymbol[symbol] = append(tradesBySymbol[symbol], trade)
			totalTrades++
		} else {
			// Multiple trades from new user_fills format
			for symbol, symbolTrades := range trades {
				tradesBySymbol[symbol] = append(tradesBySymbol[symbol], symbolTrades...)
				totalTrades += len(symbolTrades)
			}
		}
	}

	global.GVA_LOG.Debug("Parsed and grouped raw messages",
		zap.Int("total_messages", len(messages)),
		zap.Int("total_trades", totalTrades),
		zap.Int("unique_symbols", len(tradesBySymbol)),
		zap.Int("parse_errors", errorCount))

	return tradesBySymbol, errorCount
}

// extractTradesFromRawUserFillsMessage extracts multiple trades from raw user_fills WebSocket message
func (n *NATSConsumerService) extractTradesFromRawUserFillsMessage(messageData []byte) (map[string][]TradeData, error) {
	// Define WebSocket message structure locally to avoid import cycles
	type WebSocketMessage struct {
		Type         string        `json:"type"`
		Data         []interface{} `json:"data"`
		Timestamp    int64         `json:"timestamp"`
		BatchIndex   int           `json:"batch_index"`
		MessageIndex int           `json:"message_index"`
	}

	// Parse the raw WebSocket message
	var wsMessage WebSocketMessage
	if err := json.Unmarshal(messageData, &wsMessage); err != nil {
		return nil, fmt.Errorf("failed to parse raw user_fills message: %w", err)
	}

	// Validate message type
	if wsMessage.Type != "user_fills" {
		return nil, fmt.Errorf("invalid message type: expected 'user_fills', got '%s'", wsMessage.Type)
	}

	if len(wsMessage.Data) == 0 {
		return nil, fmt.Errorf("empty data array in user_fills message")
	}

	tradesBySymbol := make(map[string][]TradeData)

	// Process each trade in the data array
	for i, tradeData := range wsMessage.Data {
		// Validate that each trade is an array with 6 elements
		tradeArray, ok := tradeData.([]interface{})
		if !ok {
			global.GVA_LOG.Error("Invalid trade data format in user_fills",
				zap.Int("trade_index", i),
				zap.String("expected", "array"),
				zap.String("got", fmt.Sprintf("%T", tradeData)))
			continue
		}

		if len(tradeArray) != 6 {
			global.GVA_LOG.Error("Invalid trade array length in user_fills",
				zap.Int("trade_index", i),
				zap.Int("expected", 6),
				zap.Int("got", len(tradeArray)))
			continue
		}

		// Extract trade data using the same logic as extractTradeDataFromOptimizedMessage
		trade, symbol, err := n.extractTradeFromArray(tradeArray, wsMessage.BatchIndex)
		if err != nil {
			global.GVA_LOG.Error("Failed to extract trade from user_fills array",
				zap.Int("trade_index", i),
				zap.Error(err))
			continue
		}

		// Group by symbol
		tradesBySymbol[symbol] = append(tradesBySymbol[symbol], trade)
	}

	if len(tradesBySymbol) == 0 {
		return nil, fmt.Errorf("no valid trades found in user_fills message")
	}

	return tradesBySymbol, nil
}

// extractTradeFromArray extracts trade data from a 6-element array (shared logic)
func (n *NATSConsumerService) extractTradeFromArray(dataArray []interface{}, batchIndex int) (TradeData, string, error) {
	// Validate data array length
	if len(dataArray) != 6 {
		return TradeData{}, "", fmt.Errorf("invalid data array: expected 6 fields, got %d", len(dataArray))
	}

	// Extract and validate each field from the data array
	indexFloat, ok := dataArray[0].(float64) // JSON numbers are float64
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid index field: expected number, got %T", dataArray[0])
	}
	index := int(indexFloat)

	coin, ok := dataArray[1].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid coin field: expected string, got %T", dataArray[1])
	}

	px, ok := dataArray[2].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid price field: expected string, got %T", dataArray[2])
	}

	sz, ok := dataArray[3].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid size field: expected string, got %T", dataArray[3])
	}

	timeFloat, ok := dataArray[4].(float64) // JSON numbers are float64
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid time field: expected number, got %T", dataArray[4])
	}

	_, ok = dataArray[5].(string) // Hash not used in calculation
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid hash field: expected string, got %T", dataArray[5])
	}

	// Parse price
	price, err := strconv.ParseFloat(px, 64)
	if err != nil {
		return TradeData{}, "", fmt.Errorf("invalid price: %w", err)
	}

	// Parse volume
	volume, err := strconv.ParseFloat(sz, 64)
	if err != nil {
		return TradeData{}, "", fmt.Errorf("invalid volume: %w", err)
	}

	// Convert timestamp
	timestamp := time.UnixMilli(int64(timeFloat))

	return TradeData{
		BatchIndex: batchIndex,
		Index:      index,
		Price:      price,
		Volume:     volume,
		Timestamp:  timestamp,
	}, coin, nil
}

// extractTradeDataFromOptimizedMessage extracts trade data from optimized NATS message (direct array format)
func (n *NATSConsumerService) extractTradeDataFromOptimizedMessage(messageData []byte) (TradeData, string, error) {
	// Parse the optimized message (direct 6-element array)
	var dataArray []interface{}
	if err := json.Unmarshal(messageData, &dataArray); err != nil {
		return TradeData{}, "", fmt.Errorf("failed to parse optimized message array: %w", err)
	}

	// Use shared extraction logic with default BatchIndex 0 for legacy format
	return n.extractTradeFromArray(dataArray, 0)
}

// extractTradeDataFromRaw extracts trade data from raw WebSocket message bytes (legacy method)
func (n *NATSConsumerService) extractTradeDataFromRaw(rawData []byte) (TradeData, string, error) {
	// Define WebSocket message structure locally to avoid import cycles
	type WebSocketMessage struct {
		Type         string        `json:"type"`
		Data         []interface{} `json:"data"`
		Timestamp    int64         `json:"timestamp"` // Changed from string to int64 for Unix timestamp
		BatchIndex   int           `json:"batch_index"`
		MessageIndex int           `json:"message_index"`
	}

	// Parse the raw WebSocket message
	var wsMessage WebSocketMessage
	if err := json.Unmarshal(rawData, &wsMessage); err != nil {
		return TradeData{}, "", fmt.Errorf("failed to parse WebSocket message: %w", err)
	}

	// Validate message type
	if wsMessage.Type != "user_fill" {
		return TradeData{}, "", fmt.Errorf("invalid message type: expected 'user_fill', got '%s'", wsMessage.Type)
	}

	// Validate data array length
	if len(wsMessage.Data) != 6 {
		return TradeData{}, "", fmt.Errorf("invalid data array: expected 6 fields, got %d", len(wsMessage.Data))
	}

	// Extract and validate each field from the data array
	indexFloat, ok := wsMessage.Data[0].(float64) // JSON numbers are float64
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid index field: expected number, got %T", wsMessage.Data[0])
	}
	index := int(indexFloat)

	coin, ok := wsMessage.Data[1].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid coin field: expected string, got %T", wsMessage.Data[1])
	}

	px, ok := wsMessage.Data[2].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid price field: expected string, got %T", wsMessage.Data[2])
	}

	sz, ok := wsMessage.Data[3].(string)
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid size field: expected string, got %T", wsMessage.Data[3])
	}

	timeFloat, ok := wsMessage.Data[4].(float64) // JSON numbers are float64
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid time field: expected number, got %T", wsMessage.Data[4])
	}

	_, ok = wsMessage.Data[5].(string) // Hash not used in OHLCV calculation
	if !ok {
		return TradeData{}, "", fmt.Errorf("invalid hash field: expected string, got %T", wsMessage.Data[5])
	}

	// Parse price
	price, err := strconv.ParseFloat(px, 64)
	if err != nil {
		return TradeData{}, "", fmt.Errorf("invalid price: %w", err)
	}

	// Parse volume
	volume, err := strconv.ParseFloat(sz, 64)
	if err != nil {
		return TradeData{}, "", fmt.Errorf("invalid volume: %w", err)
	}

	// Convert timestamp
	timestamp := time.UnixMilli(int64(timeFloat))

	return TradeData{
		BatchIndex: wsMessage.BatchIndex,
		Index:      index,
		Price:      price,
		Volume:     volume,
		Timestamp:  timestamp,
	}, coin, nil
}

// batchReadOHLCVData performs a single batch Redis read for all required OHLCV data
func (n *NATSConsumerService) batchReadOHLCVData(ctx context.Context, tradesBySymbol map[string][]TradeData) (map[string]*RedisKline, int) {
	if len(tradesBySymbol) == 0 {
		return make(map[string]*RedisKline), 0
	}

	// Step 1: Generate all Redis keys that need to be read
	redisKeys := n.generateRedisKeys(tradesBySymbol)

	if len(redisKeys) == 0 {
		return make(map[string]*RedisKline), 0
	}

	global.GVA_LOG.Debug("Batch reading OHLCV data",
		zap.Int("total_keys", len(redisKeys)),
		zap.Int("symbols", len(tradesBySymbol)))

	// Step 2: Perform single batch read using Redis pipeline
	existingData := make(map[string]*RedisKline)
	errorCount := 0

	// Use pipeline for batch reading
	pipe := n.redisClient.Pipeline()

	// Add all ZRangeByScore commands to pipeline
	cmdMap := make(map[string]*redis.StringSliceCmd)
	for _, keyInfo := range redisKeys {
		score := float64(keyInfo.Timestamp)
		cmd := pipe.ZRangeByScore(ctx, keyInfo.Key, &redis.ZRangeBy{
			Min: fmt.Sprintf("%f", score),
			Max: fmt.Sprintf("%f", score),
		})
		// Use composite key (key + timestamp) to avoid overwriting commands for same key but different timestamps
		compositeKey := fmt.Sprintf("%s:%d", keyInfo.Key, keyInfo.Timestamp)
		cmdMap[compositeKey] = cmd
	}

	// Execute pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to execute Redis batch read pipeline", zap.Error(err))
		return existingData, len(redisKeys) // Return error count
	}

	// Step 3: Process results and create composite keys with timestamps
	keyInfoMap := make(map[string]RedisKeyInfo) // Map composite key to KeyInfo
	for _, keyInfo := range redisKeys {
		compositeKey := fmt.Sprintf("%s:%d", keyInfo.Key, keyInfo.Timestamp)
		keyInfoMap[compositeKey] = keyInfo
	}

	for compositeKey, cmd := range cmdMap {
		result, err := cmd.Result()
		if err != nil {
			global.GVA_LOG.Warn("Failed to read Redis key",
				zap.String("composite_key", compositeKey),
				zap.Error(err))
			errorCount++
			continue
		}

		if len(result) > 0 {
			var kline RedisKline
			if err := json.Unmarshal([]byte(result[0]), &kline); err != nil {
				global.GVA_LOG.Warn("Failed to unmarshal kline data",
					zap.String("composite_key", compositeKey),
					zap.Error(err))
				errorCount++
				continue
			}
			// The composite key is already correct, just store the kline
			existingData[compositeKey] = &kline
		}
	}

	global.GVA_LOG.Debug("Batch read completed",
		zap.Int("keys_read", len(existingData)),
		zap.Int("errors", errorCount))

	return existingData, errorCount
}

// generateRedisKeys generates all Redis keys that need to be read for the given trades
func (n *NATSConsumerService) generateRedisKeys(tradesBySymbol map[string][]TradeData) []RedisKeyInfo {
	var redisKeys []RedisKeyInfo
	keySet := make(map[string]bool) // To avoid duplicates

	timeframes := model.GetAllTimeframes()

	for symbol, trades := range tradesBySymbol {
		for _, trade := range trades {
			for _, timeframe := range timeframes {
				// Calculate bucket time for this timeframe
				bucketTime := timeframe.TruncateTime(trade.Timestamp)
				timestamp := bucketTime.UnixMilli()

				// Generate Redis key
				key := fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))

				// Use key+timestamp as unique identifier to avoid duplicates
				uniqueKey := fmt.Sprintf("%s:%d", key, timestamp)
				if !keySet[uniqueKey] {
					keySet[uniqueKey] = true
					redisKeys = append(redisKeys, RedisKeyInfo{
						Symbol:    symbol,
						Timeframe: timeframe,
						Timestamp: timestamp,
						Key:       key,
					})
				}
			}
		}
	}

	global.GVA_LOG.Debug("Generated Redis keys",
		zap.Int("total_keys", len(redisKeys)),
		zap.Int("symbols", len(tradesBySymbol)),
		zap.Int("timeframes", len(timeframes)))

	return redisKeys
}

// processTradesInMemory processes all trades in memory and calculates updated OHLCV data
func (n *NATSConsumerService) processTradesInMemory(tradesBySymbol map[string][]TradeData, existingData map[string]*RedisKline) map[string]*RedisKline {
	updatedData := make(map[string]*RedisKline)
	timeframes := model.GetAllTimeframes()

	for symbol, trades := range tradesBySymbol {
		// Sort trades by (BatchIndex, Index) to ensure proper chronological order across batches
		sort.Slice(trades, func(i, j int) bool {
			if trades[i].BatchIndex != trades[j].BatchIndex {
				return trades[i].BatchIndex < trades[j].BatchIndex
			}
			return trades[i].Index < trades[j].Index
		})

		// Group trades by timeframe and bucket timestamp
		tradesByBucket := make(map[string][]TradeData) // key format: "timeframe:timestamp"

		for _, trade := range trades {
			for _, timeframe := range timeframes {
				// Calculate bucket time for this timeframe
				bucketTime := timeframe.TruncateTime(trade.Timestamp)
				timestamp := bucketTime.UnixMilli()

				// Create bucket key
				bucketKey := fmt.Sprintf("%s:%d", string(timeframe), timestamp)
				tradesByBucket[bucketKey] = append(tradesByBucket[bucketKey], trade)
			}
		}

		// Process each bucket to calculate OHLCV
		for bucketKey, bucketTrades := range tradesByBucket {
			// Parse bucket key to get timeframe and timestamp
			parts := strings.Split(bucketKey, ":")
			if len(parts) != 2 {
				continue
			}
			timeframe := model.KlineTimeframe(parts[0])
			timestamp, err := strconv.ParseInt(parts[1], 10, 64)
			if err != nil {
				continue
			}

			// Generate Redis key
			redisKey := fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))

			// Create composite key to lookup existing data
			lookupKey := fmt.Sprintf("%s:%d", redisKey, timestamp)
			existingKline := existingData[lookupKey]

			// Calculate OHLCV for this bucket
			kline := n.CalculateOHLCVForBucket(symbol, timeframe, timestamp, bucketTrades, existingKline)

			// Store updated kline using composite key to avoid collisions
			// Use the same composite key format as in batchReadOHLCVData
			updatedData[lookupKey] = kline
		}
	}

	global.GVA_LOG.Debug("Processed trades in memory",
		zap.Int("symbols", len(tradesBySymbol)),
		zap.Int("updated_klines", len(updatedData)))

	return updatedData
}

// CalculateOHLCVForBucket calculates OHLCV data for a bucket of trades (exported for testing)
func (n *NATSConsumerService) CalculateOHLCVForBucket(symbol string, timeframe model.KlineTimeframe, timestamp int64, trades []TradeData, existingKline *RedisKline) *RedisKline {
	if len(trades) == 0 {
		return existingKline
	}

	// Sort trades by timestamp to ensure proper chronological order
	sort.Slice(trades, func(i, j int) bool {
		if trades[i].Timestamp.Equal(trades[j].Timestamp) {
			// If timestamps are equal, use (BatchIndex, Index) as composite tiebreaker
			if trades[i].BatchIndex != trades[j].BatchIndex {
				return trades[i].BatchIndex < trades[j].BatchIndex
			}
			return trades[i].Index < trades[j].Index
		}
		return trades[i].Timestamp.Before(trades[j].Timestamp)
	})

	// Initialize kline
	var kline *RedisKline
	if existingKline != nil && existingKline.Timestamp == timestamp {
		// Start with existing data
		kline = &RedisKline{
			Timestamp:       existingKline.Timestamp,
			Symbol:          existingKline.Symbol,
			Open:            existingKline.Open,
			High:            existingKline.High,
			Low:             existingKline.Low,
			Close:           existingKline.Close,
			Volume:          existingKline.Volume,
			TradesCount:     existingKline.TradesCount,
			Timeframe:       existingKline.Timeframe,
			OpenIndex:       existingKline.OpenIndex,
			CloseIndex:      existingKline.CloseIndex,
			OpenBatchIndex:  existingKline.OpenBatchIndex,
			CloseBatchIndex: existingKline.CloseBatchIndex,
			OpenTimestamp:   existingKline.OpenTimestamp,
			CloseTimestamp:  existingKline.CloseTimestamp,
		}
	} else {
		// Create new kline with first trade (earliest timestamp)
		firstTrade := trades[0]
		kline = &RedisKline{
			Timestamp:       timestamp,
			Symbol:          symbol,
			Open:            firstTrade.Price,
			High:            firstTrade.Price,
			Low:             firstTrade.Price,
			Close:           firstTrade.Price,
			Volume:          firstTrade.Volume,
			TradesCount:     1,
			Timeframe:       string(timeframe),
			OpenIndex:       firstTrade.Index,
			CloseIndex:      firstTrade.Index,
			OpenBatchIndex:  firstTrade.BatchIndex,
			CloseBatchIndex: firstTrade.BatchIndex,
			OpenTimestamp:   firstTrade.Timestamp.UnixMilli(),
			CloseTimestamp:  firstTrade.Timestamp.UnixMilli(),
		}
		// Skip first trade since we already processed it
		trades = trades[1:]
	}

	// Process remaining trades
	for _, trade := range trades {
		tradeTimestamp := trade.Timestamp.UnixMilli()

		// Update High
		if trade.Price > kline.High {
			kline.High = trade.Price
		}

		// Update Low
		if trade.Price < kline.Low {
			kline.Low = trade.Price
		}

		// Update Open if this trade is earlier than current open
		// Use timestamp for comparison, with (BatchIndex, Index) as composite tiebreaker
		isEarlierTrade := tradeTimestamp < kline.OpenTimestamp ||
			(tradeTimestamp == kline.OpenTimestamp &&
				(trade.BatchIndex < kline.OpenBatchIndex ||
					(trade.BatchIndex == kline.OpenBatchIndex && trade.Index < kline.OpenIndex)))

		if isEarlierTrade {
			kline.Open = trade.Price
			kline.OpenIndex = trade.Index
			kline.OpenBatchIndex = trade.BatchIndex
			kline.OpenTimestamp = tradeTimestamp
		}

		// Update Close if this trade is later than current close
		// Use timestamp for comparison, with (BatchIndex, Index) as composite tiebreaker
		isLaterTrade := tradeTimestamp > kline.CloseTimestamp ||
			(tradeTimestamp == kline.CloseTimestamp &&
				(trade.BatchIndex > kline.CloseBatchIndex ||
					(trade.BatchIndex == kline.CloseBatchIndex && trade.Index >= kline.CloseIndex)))

		if isLaterTrade {
			kline.Close = trade.Price
			kline.CloseIndex = trade.Index
			kline.CloseBatchIndex = trade.BatchIndex
			kline.CloseTimestamp = tradeTimestamp
		}

		// Update Volume and Count
		kline.Volume += trade.Volume
		kline.TradesCount++
	}

	return kline
}

// updateKlineWithTrade updates a kline with new trade data (DEPRECATED - use calculateOHLCVForBucket instead)
func (n *NATSConsumerService) updateKlineWithTrade(kline *RedisKline, trade TradeData) {
	// Update High
	if trade.Price > kline.High {
		kline.High = trade.Price
	}

	// Update Low
	if trade.Price < kline.Low {
		kline.Low = trade.Price
	}

	// Update Close (always the latest price)
	kline.Close = trade.Price

	// Update Volume
	kline.Volume += trade.Volume

	// Update Trades Count
	kline.TradesCount++
}

// batchWriteOHLCVData performs a single batch Redis write for all updated OHLCV data
func (n *NATSConsumerService) batchWriteOHLCVData(ctx context.Context, updatedData map[string]*RedisKline) int {
	if len(updatedData) == 0 {
		return 0
	}

	global.GVA_LOG.Debug("Batch writing OHLCV data",
		zap.Int("total_klines", len(updatedData)))

	// Create single pipeline for all write operations
	pipe := n.redisClient.Pipeline()
	errorCount := 0

	// Get TTL configurations from the centralized RedisKlineService
	ttlConfigs := n.redisKlineService.GetAllTTLConfigs()

	for compositeKey, kline := range updatedData {
		if kline == nil {
			continue
		}

		// Extract Redis key from composite key (format: "redisKey:timestamp")
		parts := strings.Split(compositeKey, ":")
		if len(parts) < 3 {
			global.GVA_LOG.Error("Invalid composite key format", zap.String("key", compositeKey))
			errorCount++
			continue
		}
		// Reconstruct Redis key (everything except the last part which is timestamp)
		redisKey := strings.Join(parts[:len(parts)-1], ":")

		// Serialize kline data
		klineData, err := json.Marshal(kline)
		if err != nil {
			global.GVA_LOG.Error("Failed to marshal kline data",
				zap.String("composite_key", compositeKey),
				zap.String("redis_key", redisKey),
				zap.Error(err))
			errorCount++
			continue
		}

		score := float64(kline.Timestamp)

		// Remove old entry first (if exists)
		pipe.ZRemRangeByScore(ctx, redisKey, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score))

		// Add/update the kline in sorted set
		pipe.ZAdd(ctx, redisKey, redis.Z{
			Score:  score,
			Member: string(klineData),
		})

		// Set TTL for the key
		timeframe := model.KlineTimeframe(kline.Timeframe)
		if ttlConfig, exists := ttlConfigs[timeframe]; exists {
			pipe.Expire(ctx, redisKey, ttlConfig.TTL)

			// Trim to keep only the specified number of candles for this timeframe
			pipe.ZRemRangeByRank(ctx, redisKey, 0, int64(-ttlConfig.MaxCandles-1))
		}
	}

	// Execute entire pipeline in single network call
	results, err := pipe.Exec(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to execute batch write pipeline", zap.Error(err))
		return len(updatedData) // Return error count
	}

	// Verify pipeline commands executed successfully (for debugging TTL issues)
	pipelineErrorCount := 0
	for i, result := range results {
		if result.Err() != nil {
			global.GVA_LOG.Warn("NATS batch pipeline command failed",
				zap.Int("command_index", i),
				zap.Error(result.Err()))
			pipelineErrorCount++
		}
	}

	if pipelineErrorCount > 0 {
		global.GVA_LOG.Warn("Some NATS batch pipeline commands failed",
			zap.Int("failed_commands", pipelineErrorCount),
			zap.Int("total_commands", len(results)))
	}

	global.GVA_LOG.Debug("Batch write completed",
		zap.Int("klines_written", len(updatedData)),
		zap.Int("errors", errorCount))

	return errorCount
}

// acknowledgeMessages acknowledges or rejects messages based on processing success
func (n *NATSConsumerService) acknowledgeMessages(messages []*nats.Msg, success bool) {
	for _, msg := range messages {
		if success {
			msg.Ack() // Acknowledge successful processing
		} else {
			msg.Nak() // Negative acknowledgment - will retry
		}
	}
}

// updateBatchStats updates processing statistics for batch processing
func (n *NATSConsumerService) updateBatchStats(processed, errors int, duration time.Duration, batchSize int) {
	n.mu.Lock()
	defer n.mu.Unlock()

	n.processingStats.TotalProcessed += int64(processed)
	n.processingStats.TotalErrors += int64(errors)
	n.processingStats.LastProcessedAt = time.Now()
	n.processingStats.BatchesProcessed++
	n.processingStats.RedisOperations += 2 // 1 read + 1 write operation per batch

	if duration > 0 && processed > 0 {
		rate := float64(processed) / duration.Seconds()
		n.processingStats.ProcessingRate = rate
		n.processingStats.AverageLatency = duration.Seconds() * 1000 / float64(processed) // ms per message
	}

	// Calculate average batch size
	if n.processingStats.BatchesProcessed > 0 {
		n.processingStats.AvgBatchSize = float64(n.processingStats.TotalProcessed) / float64(n.processingStats.BatchesProcessed)
	}
}

// updateStats updates processing statistics (legacy method for compatibility)
func (n *NATSConsumerService) updateStats(processed, errors int, duration time.Duration) {
	n.updateBatchStats(processed, errors, duration, processed)
}

// GetStats returns current processing statistics
func (n *NATSConsumerService) GetStats() ProcessingStats {
	n.mu.RLock()
	defer n.mu.RUnlock()
	return *n.processingStats
}

// Stop stops the consumer (but preserves the durable consumer for restart)
func (n *NATSConsumerService) Stop() {
	if n.subscription != nil {
		global.GVA_LOG.Info("Stopping NATS consumer subscription...")

		// Drain the subscription to allow pending messages to be processed
		if err := n.subscription.Drain(); err != nil {
			global.GVA_LOG.Warn("Failed to drain subscription, using unsubscribe", zap.Error(err))
			// Fallback to unsubscribe if drain fails
			n.subscription.Unsubscribe()
		} else {
			global.GVA_LOG.Info("Subscription drained successfully")
		}

		// Give additional time for any pending acknowledgments to be processed
		time.Sleep(200 * time.Millisecond)

		// Flush the connection to ensure all pending operations are sent
		if n.natsClient != nil && n.natsClient.Status() == nats.CONNECTED {
			if err := n.natsClient.Flush(); err != nil {
				global.GVA_LOG.Warn("Failed to flush NATS connection", zap.Error(err))
			} else {
				global.GVA_LOG.Debug("NATS connection flushed successfully")
			}
		}

		global.GVA_LOG.Info("NATS consumer stopped (durable consumer preserved)")
	}
}

// DeleteConsumer manually deletes the durable consumer (use with caution)
// This should only be called when you want to reset the consumer position
func (n *NATSConsumerService) DeleteConsumer() error {
	err := n.js.DeleteConsumer(model.StreamKline, "kline-processor")
	if err != nil {
		if err == nats.ErrConsumerNotFound {
			global.GVA_LOG.Info("Consumer already deleted or doesn't exist")
			return nil
		}
		return fmt.Errorf("failed to delete consumer: %w", err)
	}

	global.GVA_LOG.Info("Manually deleted durable consumer",
		zap.String("consumer", "kline-processor"))
	return nil
}
