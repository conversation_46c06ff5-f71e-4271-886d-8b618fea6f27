package service

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestWebSocketMessageFormats tests both old and new WebSocket message formats
func TestWebSocketMessageFormats(t *testing.T) {
	ws := &WebSocketService{}

	t.Run("Single_trade_user_fills_format", func(t *testing.T) {
		// Test the user_fills format with a single trade
		singleTradeMessage := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			},
			"timestamp":     int64(1760344053318),
			"batch_index":   12345,
			"message_index": 0,
		}

		messageBytes, err := json.Marshal(singleTradeMessage)
		require.NoError(t, err)

		// Test fast path detection
		assert.True(t, ws.isUserFillMessage(messageBytes))

		// Test parsing
		userFillData, err := ws.parseMessage(messageBytes)
		require.NoError(t, err)

		assert.Equal(t, 1, userFillData.Index)
		assert.Equal(t, "BTC", userFillData.Coin)
		assert.Equal(t, "50000.00", userFillData.Px)
		assert.Equal(t, "0.1", userFillData.Sz)
		assert.Equal(t, int64(1760344053318), userFillData.Time)
		assert.Equal(t, "0xabc123", userFillData.Hash)
	})

	t.Run("Multiple_trades_user_fills_format", func(t *testing.T) {
		// Test the user_fills format with multiple trades
		multipleTradesMessage := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
				[]interface{}{2, "ETH", "3000.50", "2.5", float64(1760344053319), "0xdef456"},
			},
			"timestamp":     int64(1760344053320),
			"batch_index":   12346,
			"message_index": 1,
		}

		messageBytes, err := json.Marshal(multipleTradesMessage)
		require.NoError(t, err)

		// Test fast path detection
		assert.True(t, ws.isUserFillMessage(messageBytes))

		// Test parsing (should return first trade)
		userFillData, err := ws.parseMessage(messageBytes)
		require.NoError(t, err)

		assert.Equal(t, 1, userFillData.Index)
		assert.Equal(t, "BTC", userFillData.Coin)
		assert.Equal(t, "50000.00", userFillData.Px)
		assert.Equal(t, "0.1", userFillData.Sz)
		assert.Equal(t, int64(1760344053318), userFillData.Time)
		assert.Equal(t, "0xabc123", userFillData.Hash)
	})

	t.Run("Timestamp_as_number", func(t *testing.T) {
		// Test that numeric timestamps work correctly
		message := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "DOGE", "0.21041", "5000.0", float64(1760344053318), "0x789xyz"},
			},
			"timestamp":     int64(1760344053318), // Numeric timestamp
			"batch_index":   12347,
			"message_index": 2,
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		// Parse the WebSocket message directly
		var wsMessage WebSocketMessage
		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		// Verify timestamp is correctly parsed as int64
		assert.Equal(t, int64(1760344053318), wsMessage.Timestamp)
		assert.Equal(t, "user_fills", wsMessage.Type)
	})

	t.Run("Non_user_fills_message", func(t *testing.T) {
		// Test that non-user_fills messages are not detected
		connectionMessage := map[string]interface{}{
			"type":    "connection",
			"status":  "connected",
			"message": "WebSocket connection established",
		}

		messageBytes, err := json.Marshal(connectionMessage)
		require.NoError(t, err)

		// Should not be detected as user_fills message
		assert.False(t, ws.isUserFillMessage(messageBytes))
	})
}

// TestWebSocketMessageUnmarshaling tests the JSON unmarshaling with different timestamp types
func TestWebSocketMessageUnmarshaling(t *testing.T) {
	testCases := []struct {
		name      string
		jsonData  string
		expectErr bool
	}{
		{
			name:      "Numeric_timestamp",
			jsonData:  `{"type":"user_fills","data":[[1,"BTC","50000","0.1",1760344053318,"0xabc"]],"timestamp":1760344053318}`,
			expectErr: false,
		},
		{
			name:      "String_timestamp_should_fail",
			jsonData:  `{"type":"user_fills","data":[[1,"BTC","50000","0.1",1760344053318,"0xabc"]],"timestamp":"2025-10-13T08:27:34.594004"}`,
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var wsMessage WebSocketMessage
			err := json.Unmarshal([]byte(tc.jsonData), &wsMessage)

			if tc.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, "user_fills", wsMessage.Type)
				assert.Equal(t, int64(1760344053318), wsMessage.Timestamp)
			}
		})
	}
}
