package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// WebSocketMessage represents the actual message format from the WebSocket server
type WebSocketMessage struct {
	Type         string        `json:"type"`
	Data         []interface{} `json:"data"`
	Timestamp    int64         `json:"timestamp"` // Changed from string to int64 for Unix timestamp
	BatchIndex   int           `json:"batch_index"`
	MessageIndex int           `json:"message_index"`
}

// UserFillData represents the essential trade data extracted from WebSocket messages
type UserFillData struct {
	Index int    `json:"index"`
	Coin  string `json:"coin"`
	Px    string `json:"px"`   // Price
	Sz    string `json:"sz"`   // Size
	Time  int64  `json:"time"` // Unix timestamp in milliseconds
	Hash  string `json:"hash"` // Transaction hash
}

// ToUserFillData converts WebSocket data array to UserFillData
func (w *WebSocketMessage) ToUserFillData() (*UserFillData, error) {
	if len(w.Data) != 6 {
		return nil, fmt.Errorf("invalid data array: expected 6 fields, got %d", len(w.Data))
	}

	// Extract and validate each field from the data array
	index, ok := w.Data[0].(float64) // JSON numbers are float64
	if !ok {
		return nil, fmt.Errorf("invalid index field: expected number, got %T", w.Data[0])
	}

	coin, ok := w.Data[1].(string)
	if !ok {
		return nil, fmt.Errorf("invalid coin field: expected string, got %T", w.Data[1])
	}

	px, ok := w.Data[2].(string)
	if !ok {
		return nil, fmt.Errorf("invalid price field: expected string, got %T", w.Data[2])
	}

	sz, ok := w.Data[3].(string)
	if !ok {
		return nil, fmt.Errorf("invalid size field: expected string, got %T", w.Data[3])
	}

	time, ok := w.Data[4].(float64) // JSON numbers are float64
	if !ok {
		return nil, fmt.Errorf("invalid time field: expected number, got %T", w.Data[4])
	}

	hash, ok := w.Data[5].(string)
	if !ok {
		return nil, fmt.Errorf("invalid hash field: expected string, got %T", w.Data[5])
	}

	return &UserFillData{
		Index: int(index),
		Coin:  coin,
		Px:    px,
		Sz:    sz,
		Time:  int64(time),
		Hash:  hash,
	}, nil
}

// WebSocketService handles WebSocket connections and message processing
type WebSocketService struct {
	conn              *websocket.Conn
	url               string
	reconnectInterval time.Duration
	pingInterval      time.Duration
	pongTimeout       time.Duration
	natsPublisher     *NATSPublisherService
	isConnected       bool
	stopChan          chan struct{}
	// Async publishing channels for minimal latency with enhanced throughput
	rawPublishChan     chan []byte
	publishWorkerDone  chan struct{}
	workerCount        int
	maxWorkerCount     int
	channelUtilization chan int // For monitoring channel usage
}

// NewWebSocketService creates a new WebSocket service instance
func NewWebSocketService() *WebSocketService {
	wsConfig := global.GVA_CONFIG.WebSocket

	// Parse durations
	reconnectInterval, _ := time.ParseDuration(wsConfig.ReconnectInterval)
	if reconnectInterval == 0 {
		reconnectInterval = 5 * time.Second
	}

	pingInterval, _ := time.ParseDuration(wsConfig.PingInterval)
	if pingInterval == 0 {
		pingInterval = 30 * time.Second
	}

	pongTimeout, _ := time.ParseDuration(wsConfig.PongTimeout)
	if pongTimeout == 0 {
		pongTimeout = 10 * time.Second
	}

	// Handle both old format (host:port) and new format (full URL with protocol)
	var wsURL string
	if strings.HasPrefix(wsConfig.Host, "ws://") || strings.HasPrefix(wsConfig.Host, "wss://") {
		// New format: host already includes protocol scheme
		wsURL = wsConfig.Host + wsConfig.Path
	} else {
		// Old format: construct URL with protocol, host, and port
		wsURL = fmt.Sprintf("ws://%s:%s%s", wsConfig.Host, wsConfig.Port, wsConfig.Path)
	}

	// Initialize NATS publisher
	natsPublisher := NewNATSPublisherService()

	// Ensure NATS stream exists
	if err := natsPublisher.EnsureStream(); err != nil {
		global.GVA_LOG.Error("Failed to ensure NATS stream", zap.Error(err))
	}

	return &WebSocketService{
		url:               wsURL,
		reconnectInterval: reconnectInterval,
		pingInterval:      pingInterval,
		pongTimeout:       pongTimeout,
		natsPublisher:     natsPublisher,
		stopChan:          make(chan struct{}),
		// Enhanced buffered channel for async publishing with larger buffer and monitoring
		rawPublishChan:     make(chan []byte, 10000), // Increased buffer size 10x
		publishWorkerDone:  make(chan struct{}),
		workerCount:        1,                   // Start with 1 worker
		maxWorkerCount:     5,                   // Max 5 workers for high throughput
		channelUtilization: make(chan int, 100), // Monitor channel usage
	}
}

// Connect establishes WebSocket connection using gorilla/websocket dialer
func (ws *WebSocketService) Connect(ctx context.Context) error {
	global.GVA_LOG.Info("Connecting to WebSocket", zap.String("url", ws.url))

	// Create WebSocket dialer
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	// Connect to WebSocket
	conn, _, err := dialer.Dial(ws.url, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to WebSocket: %w", err)
	}

	ws.conn = conn
	ws.isConnected = true

	global.GVA_LOG.Info("WebSocket connection established successfully")
	return nil
}

// startPublishWorker starts multiple async publishing worker goroutines with dynamic scaling
func (ws *WebSocketService) startPublishWorker(ctx context.Context) {
	// Start initial worker
	ws.startSingleWorker(ctx, 1)

	// Start monitoring goroutine for dynamic scaling
	go ws.monitorAndScale(ctx)
}

// startSingleWorker starts a single worker goroutine
func (ws *WebSocketService) startSingleWorker(ctx context.Context, workerID int) {
	go func() {
		if workerID == 1 {
			defer close(ws.publishWorkerDone) // Only first worker closes the done channel
		}

		global.GVA_LOG.Info("Starting NATS publish worker", zap.Int("worker_id", workerID))

		for {
			select {
			case <-ctx.Done():
				global.GVA_LOG.Info("Worker stopping due to context cancellation", zap.Int("worker_id", workerID))
				return
			case <-ws.stopChan:
				global.GVA_LOG.Info("Worker stopping due to stop signal", zap.Int("worker_id", workerID))
				return
			case rawData := <-ws.rawPublishChan:
				// Measure publish latency
				publishStart := time.Now()

				// Publish optimized message (only data array) to NATS for maximum efficiency
				if err := ws.natsPublisher.PublishOptimizedUserFill(ctx, rawData); err != nil {
					global.GVA_LOG.Error("Failed to publish optimized message to NATS",
						zap.Int("worker_id", workerID),
						zap.Error(err))
				} else {
					publishLatency := time.Since(publishStart)
					global.GVA_LOG.Debug("Optimized message published to NATS",
						zap.Int("worker_id", workerID),
						zap.Duration("publish_latency_ns", publishLatency),
						zap.Int("message_size", len(rawData)))
				}
			}
		}
	}()
}

// monitorAndScale monitors channel utilization and scales workers dynamically
func (ws *WebSocketService) monitorAndScale(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Second) // Check every second
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ws.stopChan:
			return
		case <-ticker.C:
			// Check channel utilization
			channelLen := len(ws.rawPublishChan)
			channelCap := cap(ws.rawPublishChan)
			utilization := float64(channelLen) / float64(channelCap) * 100

			// Scale up if utilization > 70% and we haven't reached max workers
			if utilization > 70.0 && ws.workerCount < ws.maxWorkerCount {
				ws.workerCount++
				global.GVA_LOG.Info("Scaling up NATS publish workers",
					zap.Int("new_worker_count", ws.workerCount),
					zap.Float64("channel_utilization", utilization),
					zap.Int("channel_length", channelLen))
				ws.startSingleWorker(ctx, ws.workerCount)
			}

			// Log utilization for monitoring (only if > 50% to avoid spam)
			if utilization > 50.0 {
				global.GVA_LOG.Debug("NATS publish channel utilization",
					zap.Float64("utilization_percent", utilization),
					zap.Int("channel_length", channelLen),
					zap.Int("channel_capacity", channelCap),
					zap.Int("active_workers", ws.workerCount))
			}
		}
	}
}

// Start begins the WebSocket connection and message processing loop
func (ws *WebSocketService) Start(ctx context.Context) error {
	// Start the async publishing worker
	ws.startPublishWorker(ctx)
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ws.stopChan:
			return nil
		default:
			if err := ws.Connect(ctx); err != nil {
				global.GVA_LOG.Error("Failed to connect to WebSocket", zap.Error(err))
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(ws.reconnectInterval):
					continue
				}
			}

			// Start message processing
			if err := ws.processMessages(ctx); err != nil {
				global.GVA_LOG.Error("WebSocket connection lost", zap.Error(err))
				ws.isConnected = false
				if ws.conn != nil {
					ws.conn.Close()
					ws.conn = nil
				}

				// Wait before reconnecting
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(ws.reconnectInterval):
					continue
				}
			}
		}
	}
}

// processMessages handles incoming WebSocket messages
func (ws *WebSocketService) processMessages(ctx context.Context) error {
	// Set up ping/pong handling
	ws.conn.SetPongHandler(func(string) error {
		return ws.conn.SetReadDeadline(time.Now().Add(ws.pongTimeout))
	})

	// Start ping ticker
	pingTicker := time.NewTicker(ws.pingInterval)
	defer pingTicker.Stop()

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-pingTicker.C:
				if ws.conn != nil {
					if err := ws.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
						global.GVA_LOG.Error("Failed to send ping", zap.Error(err))
						return
					}
				}
			}
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// Set read deadline
			ws.conn.SetReadDeadline(time.Now().Add(ws.pongTimeout))

			// Read message
			_, message, err := ws.conn.ReadMessage()
			if err != nil {
				return fmt.Errorf("failed to read message: %w", err)
			}

			// Process message
			if err := ws.handleMessage(message); err != nil {
				global.GVA_LOG.Error("Failed to handle message", zap.Error(err))
			}
		}
	}
}

// isUserFillMessage performs a fast check to see if this is a user_fills message
// without full JSON parsing to minimize latency
func (ws *WebSocketService) isUserFillMessage(message []byte) bool {
	// Quick string search for "user_fills" in the message
	// This is much faster than JSON unmarshaling
	return bytes.Contains(message, []byte(`"type":"user_fills"`)) ||
		bytes.Contains(message, []byte(`"type": "user_fills"`))
}

// handleMessage processes individual WebSocket messages with optimized fast-path publishing
func (ws *WebSocketService) handleMessage(message []byte) error {
	// Start timing for performance monitoring
	startTime := time.Now()

	// FAST PATH: For user_fill messages, publish raw data immediately to minimize latency
	// We do a quick check without full JSON parsing first
	if ws.isUserFillMessage(message) {
		// Try non-blocking send first
		select {
		case ws.rawPublishChan <- message:
			// Successfully queued for immediate publishing
			fastPathLatency := time.Since(startTime)
			global.GVA_LOG.Debug("Fast-path message queued",
				zap.Duration("latency_ns", fastPathLatency),
				zap.Int("message_size", len(message)))
			return nil // Message handled via fast path, no need for normal processing
		default:
			// Channel is full, try emergency scaling and fallback to direct publish
			channelLen := len(ws.rawPublishChan)
			channelCap := cap(ws.rawPublishChan)
			utilization := float64(channelLen) / float64(channelCap) * 100

			global.GVA_LOG.Warn("Raw publish channel full, using direct publish",
				zap.Float64("channel_utilization", utilization),
				zap.Int("channel_length", channelLen),
				zap.Int("active_workers", ws.workerCount))

			// Emergency scaling: start additional worker if possible
			if ws.workerCount < ws.maxWorkerCount {
				ws.workerCount++
				global.GVA_LOG.Info("Emergency scaling: starting additional worker",
					zap.Int("new_worker_count", ws.workerCount))
				ws.startSingleWorker(context.Background(), ws.workerCount)
			}

			// Direct publish to avoid message loss (blocking but ensures delivery)
			if err := ws.natsPublisher.PublishOptimizedUserFill(context.Background(), message); err != nil {
				global.GVA_LOG.Error("Failed to publish optimized message directly", zap.Error(err))
				return err
			}

			global.GVA_LOG.Debug("Message published directly due to channel congestion",
				zap.Duration("direct_publish_latency", time.Since(startTime)))
			return nil // Message handled via direct publish
		}
	}

	// NORMAL PATH: Continue with standard processing for compatibility
	// Parse the WebSocket message
	var wsMessage WebSocketMessage
	if err := json.Unmarshal(message, &wsMessage); err != nil {
		return fmt.Errorf("failed to unmarshal WebSocket message: %w", err)
	}

	// Debug: Log the received message
	global.GVA_LOG.Debug("Received WebSocket message",
		zap.String("type", wsMessage.Type),
		zap.Int("data_length", len(wsMessage.Data)),
		zap.Int64("timestamp", wsMessage.Timestamp))

	// Handle different message types
	switch wsMessage.Type {
	case "user_fills":
		// Process trade data - handle multiple trades in the data array
		for i, tradeData := range wsMessage.Data {
			// Convert the individual trade data
			if tradeArray, ok := tradeData.([]interface{}); ok && len(tradeArray) == 6 {
				// Create a temporary WebSocketMessage for each trade
				tempMessage := WebSocketMessage{
					Type:      "user_fills",
					Data:      tradeArray,
					Timestamp: wsMessage.Timestamp,
				}

				userFillData, err := tempMessage.ToUserFillData()
				if err != nil {
					global.GVA_LOG.Error("Failed to convert individual trade data",
						zap.Int("trade_index", i),
						zap.Error(err))
					continue // Skip this trade but continue with others
				}

				global.GVA_LOG.Debug("Processed user_fills trade",
					zap.Int("trade_index", i),
					zap.Int("index", userFillData.Index),
					zap.String("coin", userFillData.Coin),
					zap.String("price", userFillData.Px),
					zap.String("size", userFillData.Sz))
			} else {
				global.GVA_LOG.Error("Invalid trade data format",
					zap.Int("trade_index", i),
					zap.String("expected", "array with 6 elements"),
					zap.String("got", fmt.Sprintf("%T with %d elements", tradeData, len(tradeArray))))
			}
		}

		return nil

	case "connection":
		// Handle connection status messages (just log them)
		global.GVA_LOG.Info("WebSocket connection status",
			zap.String("message", string(message)))
		return nil

	default:
		// Log unknown message types but don't error
		global.GVA_LOG.Warn("Unknown WebSocket message type",
			zap.String("type", wsMessage.Type),
			zap.String("message", string(message)))
		return nil
	}
}

// Stop gracefully stops the WebSocket service
func (ws *WebSocketService) Stop() {
	global.GVA_LOG.Info("Stopping WebSocket service",
		zap.Int("active_workers", ws.workerCount),
		zap.Int("pending_messages", len(ws.rawPublishChan)))

	close(ws.stopChan)

	// Close the raw publish channel and wait for workers to finish
	close(ws.rawPublishChan)
	close(ws.channelUtilization)
	<-ws.publishWorkerDone

	if ws.conn != nil {
		ws.conn.Close()
		ws.conn = nil
	}
	ws.isConnected = false

	global.GVA_LOG.Info("WebSocket service stopped successfully")
}

// IsConnected returns the connection status
func (ws *WebSocketService) IsConnected() bool {
	return ws.isConnected
}

// GetPublishStats returns current publishing statistics
func (ws *WebSocketService) GetPublishStats() map[string]interface{} {
	channelLen := len(ws.rawPublishChan)
	channelCap := cap(ws.rawPublishChan)
	utilization := float64(channelLen) / float64(channelCap) * 100

	return map[string]interface{}{
		"channel_length":      channelLen,
		"channel_capacity":    channelCap,
		"channel_utilization": utilization,
		"active_workers":      ws.workerCount,
		"max_workers":         ws.maxWorkerCount,
		"is_connected":        ws.isConnected,
	}
}

// GetRawPublishChan returns the raw publish channel for testing purposes
func (ws *WebSocketService) GetRawPublishChan() chan []byte {
	return ws.rawPublishChan
}

// IsUserFillMessage exposes the fast message detection for testing
func (ws *WebSocketService) IsUserFillMessage(message []byte) bool {
	return ws.isUserFillMessage(message)
}

// parseMessage is a helper method for testing that parses WebSocket messages
// without publishing to NATS
func (ws *WebSocketService) parseMessage(message []byte) (*UserFillData, error) {
	// Parse the WebSocket message
	var wsMessage WebSocketMessage
	if err := json.Unmarshal(message, &wsMessage); err != nil {
		return nil, fmt.Errorf("failed to unmarshal WebSocket message: %w", err)
	}

	// Only handle user_fills messages
	if wsMessage.Type != "user_fills" {
		return nil, fmt.Errorf("unsupported message type for testing: %s", wsMessage.Type)
	}

	// For user_fills, return the first trade data for testing
	if len(wsMessage.Data) == 0 {
		return nil, fmt.Errorf("empty data array in user_fills message")
	}

	// Create a temporary message with the first trade
	if tradeArray, ok := wsMessage.Data[0].([]interface{}); ok && len(tradeArray) == 6 {
		tempMessage := WebSocketMessage{
			Type:      "user_fills",
			Data:      tradeArray,
			Timestamp: wsMessage.Timestamp,
		}
		return tempMessage.ToUserFillData()
	}

	return nil, fmt.Errorf("invalid trade data format in user_fills message")
}
