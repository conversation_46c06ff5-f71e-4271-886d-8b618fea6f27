package service

import (
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// Global shared proxy manager instance to prevent goroutine leaks
var (
	sharedProxyManager *ProxyManager
	proxyManagerMutex  sync.Mutex
	proxyManagerOnce   sync.Once
)

// ProxyInfo represents information about a proxy
type ProxyInfo struct {
	URL         string
	IsHealthy   bool
	LastChecked time.Time
	FailCount   int
	LastError   error
}

// ProxyManager manages HTTP proxy rotation and health checking
type ProxyManager struct {
	config       config.BackfillProxies
	proxies      []*ProxyInfo
	currentIdx   int
	mutex        sync.RWMutex
	healthTicker *time.Ticker
	stopChan     chan struct{}
}

// NewProxyManager creates a new proxy manager
func NewProxyManager(config config.BackfillProxies) *ProxyManager {
	pm := &ProxyManager{
		config:   config,
		proxies:  make([]*ProxyInfo, 0, len(config.URLs)),
		stopChan: make(chan struct{}),
	}

	// Initialize proxy info
	for _, proxyURL := range config.URLs {
		pm.proxies = append(pm.proxies, &ProxyInfo{
			URL:         proxyURL,
			IsHealthy:   true, // Assume healthy initially
			LastChecked: time.Now(),
			FailCount:   0,
		})
	}

	// Start health checking if enabled
	if config.HealthCheck.Enabled {
		pm.startHealthChecking()
	}

	global.GVA_LOG.Info("Proxy manager initialized",
		zap.Int("proxy_count", len(pm.proxies)),
		zap.Bool("health_check_enabled", config.HealthCheck.Enabled),
		zap.String("rotation_strategy", config.Rotation))

	return pm
}

// GetNextProxy returns the next available proxy using the configured rotation strategy
func (pm *ProxyManager) GetNextProxy() (*ProxyInfo, error) {
	if !pm.config.Enabled {
		return nil, fmt.Errorf("proxy manager is disabled")
	}

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// Find healthy proxies
	healthyProxies := make([]*ProxyInfo, 0)
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy {
			healthyProxies = append(healthyProxies, proxy)
		}
	}

	if len(healthyProxies) == 0 {
		return nil, fmt.Errorf("no healthy proxies available")
	}

	var selectedProxy *ProxyInfo

	switch pm.config.Rotation {
	case "random":
		selectedProxy = healthyProxies[rand.Intn(len(healthyProxies))]
	case "round-robin":
		fallthrough
	default:
		// Find the next healthy proxy in round-robin fashion
		for i := 0; i < len(pm.proxies); i++ {
			idx := (pm.currentIdx + i) % len(pm.proxies)
			if pm.proxies[idx].IsHealthy {
				selectedProxy = pm.proxies[idx]
				pm.currentIdx = (idx + 1) % len(pm.proxies)
				break
			}
		}
	}

	if selectedProxy == nil {
		return nil, fmt.Errorf("failed to select proxy")
	}

	global.GVA_LOG.Debug("Selected proxy",
		zap.String("proxy_url", selectedProxy.URL),
		zap.String("strategy", pm.config.Rotation))

	return selectedProxy, nil
}

// GetProxyForJob returns a specific proxy assigned to a job based on job ID
func (pm *ProxyManager) GetProxyForJob(jobID string) (*ProxyInfo, error) {
	if !pm.config.Enabled {
		return nil, fmt.Errorf("proxy manager is disabled")
	}

	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.proxies) == 0 {
		return nil, fmt.Errorf("no proxies configured")
	}

	// Use hash of job ID to consistently assign proxy
	hash := 0
	for _, char := range jobID {
		hash = hash*31 + int(char)
	}
	proxyIdx := hash % len(pm.proxies)

	proxy := pm.proxies[proxyIdx]
	if !proxy.IsHealthy {
		// If assigned proxy is unhealthy, try to find a healthy one
		for i := 0; i < len(pm.proxies); i++ {
			idx := (proxyIdx + i) % len(pm.proxies)
			if pm.proxies[idx].IsHealthy {
				proxy = pm.proxies[idx]
				break
			}
		}
	}

	if !proxy.IsHealthy {
		return nil, fmt.Errorf("no healthy proxies available for job %s", jobID)
	}

	global.GVA_LOG.Debug("Assigned proxy to job",
		zap.String("job_id", jobID),
		zap.String("proxy_url", proxy.URL),
		zap.Int("proxy_index", proxyIdx))

	return proxy, nil
}

// MarkProxyFailed marks a proxy as failed and potentially unhealthy
func (pm *ProxyManager) MarkProxyFailed(proxyURL string, err error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for _, proxy := range pm.proxies {
		if proxy.URL == proxyURL {
			proxy.FailCount++
			proxy.LastError = err
			proxy.LastChecked = time.Now()

			// Mark as unhealthy if too many failures
			if proxy.FailCount >= 3 {
				proxy.IsHealthy = false
				global.GVA_LOG.Warn("Proxy marked as unhealthy",
					zap.String("proxy_url", proxyURL),
					zap.Int("fail_count", proxy.FailCount),
					zap.Error(err))
			} else {
				global.GVA_LOG.Debug("Proxy failure recorded",
					zap.String("proxy_url", proxyURL),
					zap.Int("fail_count", proxy.FailCount),
					zap.Error(err))
			}
			break
		}
	}
}

// MarkProxySuccess marks a proxy as successful
func (pm *ProxyManager) MarkProxySuccess(proxyURL string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for _, proxy := range pm.proxies {
		if proxy.URL == proxyURL {
			proxy.FailCount = 0
			proxy.LastError = nil
			proxy.LastChecked = time.Now()
			if !proxy.IsHealthy {
				proxy.IsHealthy = true
				global.GVA_LOG.Info("Proxy marked as healthy",
					zap.String("proxy_url", proxyURL))
			}
			break
		}
	}
}

// GetHealthyProxyCount returns the number of healthy proxies
func (pm *ProxyManager) GetHealthyProxyCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	count := 0
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy {
			count++
		}
	}
	return count
}

// GetProxyStats returns statistics about all proxies
func (pm *ProxyManager) GetProxyStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	stats := make(map[string]interface{})
	healthyCount := 0
	totalCount := len(pm.proxies)

	proxyDetails := make([]map[string]interface{}, 0, totalCount)
	for _, proxy := range pm.proxies {
		if proxy.IsHealthy {
			healthyCount++
		}

		detail := map[string]interface{}{
			"url":          proxy.URL,
			"is_healthy":   proxy.IsHealthy,
			"fail_count":   proxy.FailCount,
			"last_checked": proxy.LastChecked,
		}
		if proxy.LastError != nil {
			detail["last_error"] = proxy.LastError.Error()
		}
		proxyDetails = append(proxyDetails, detail)
	}

	stats["total_proxies"] = totalCount
	stats["healthy_proxies"] = healthyCount
	stats["unhealthy_proxies"] = totalCount - healthyCount
	stats["rotation_strategy"] = pm.config.Rotation
	stats["enabled"] = pm.config.Enabled
	stats["proxies"] = proxyDetails

	return stats
}

// startHealthChecking starts the health checking goroutine
func (pm *ProxyManager) startHealthChecking() {
	interval, err := time.ParseDuration(pm.config.HealthCheck.Interval)
	if err != nil {
		global.GVA_LOG.Error("Invalid health check interval, using default 5m",
			zap.String("interval", pm.config.HealthCheck.Interval),
			zap.Error(err))
		interval = 5 * time.Minute
	}

	pm.healthTicker = time.NewTicker(interval)

	go func() {
		for {
			select {
			case <-pm.stopChan:
				return
			case <-pm.healthTicker.C:
				pm.performHealthChecks()
			}
		}
	}()

	global.GVA_LOG.Info("Health checking started",
		zap.Duration("interval", interval))
}

// performHealthChecks performs health checks on all proxies
func (pm *ProxyManager) performHealthChecks() {
	timeout, err := time.ParseDuration(pm.config.HealthCheck.Timeout)
	if err != nil {
		timeout = 10 * time.Second
	}

	healthCheckURL := pm.config.HealthCheck.URL
	if healthCheckURL == "" {
		healthCheckURL = "https://httpbin.org/ip"
	}

	global.GVA_LOG.Debug("Starting health checks",
		zap.String("url", healthCheckURL),
		zap.Duration("timeout", timeout))

	for _, proxy := range pm.proxies {
		go pm.checkProxyHealth(proxy, healthCheckURL, timeout)
	}
}

// checkProxyHealth checks the health of a single proxy
func (pm *ProxyManager) checkProxyHealth(proxy *ProxyInfo, healthCheckURL string, timeout time.Duration) {
	proxyURL, err := url.Parse(proxy.URL)
	if err != nil {
		pm.MarkProxyFailed(proxy.URL, fmt.Errorf("invalid proxy URL: %w", err))
		return
	}

	client := &http.Client{
		Timeout: timeout,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		},
	}

	resp, err := client.Get(healthCheckURL)
	if err != nil {
		pm.MarkProxyFailed(proxy.URL, fmt.Errorf("health check failed: %w", err))
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		pm.MarkProxySuccess(proxy.URL)
		global.GVA_LOG.Debug("Proxy health check passed",
			zap.String("proxy_url", proxy.URL))
	} else {
		pm.MarkProxyFailed(proxy.URL, fmt.Errorf("health check returned status %d", resp.StatusCode))
	}
}

// Stop stops the proxy manager and health checking
func (pm *ProxyManager) Stop() {
	close(pm.stopChan)
	if pm.healthTicker != nil {
		pm.healthTicker.Stop()
	}
	global.GVA_LOG.Info("Proxy manager stopped")
}

// GetSharedProxyManager returns a shared proxy manager instance to prevent goroutine leaks
func GetSharedProxyManager() *ProxyManager {
	proxyManagerOnce.Do(func() {
		if global.GVA_CONFIG.Backfill.Proxies.Enabled {
			sharedProxyManager = NewProxyManager(global.GVA_CONFIG.Backfill.Proxies)
			global.GVA_LOG.Info("Shared proxy manager initialized")
		}
	})
	return sharedProxyManager
}

// StopSharedProxyManager stops the shared proxy manager (call during shutdown)
func StopSharedProxyManager() {
	proxyManagerMutex.Lock()
	defer proxyManagerMutex.Unlock()

	if sharedProxyManager != nil {
		sharedProxyManager.Stop()
		sharedProxyManager = nil
		global.GVA_LOG.Info("Shared proxy manager stopped")
	}
}
