package service

import (
	"fmt"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"go.uber.org/zap"
)

func TestCalculateOHLCVForBucket_TimestampBasedOrdering(t *testing.T) {
	service := &NATSConsumerService{}

	// Test case based on the reported issue
	// Expected values from data verification:
	// Open: 113598.0 (first trade by timestamp)
	// Close: 113522.0 (last trade by timestamp)
	// High: 113598.0 (maximum price)
	// Low: 113522.0 (minimum price)
	// Volume: 18.29392 (sum of all volumes)
	// Trade count: 341

	// Create test trades with timestamps that don't match index order
	trades := []TradeData{
		{
			BatchIndex: 100, // Same batch for all trades in this test
			Index:      10,  // Higher index but earlier timestamp
			Price:      113598.0,
			Volume:     5.0,
			Timestamp:  time.UnixMilli(1760414100000), // Earliest timestamp
		},
		{
			BatchIndex: 100, // Same batch for all trades in this test
			Index:      5,   // Lower index but later timestamp
			Price:      113550.0,
			Volume:     3.0,
			Timestamp:  time.UnixMilli(1760414120000), // Middle timestamp
		},
		{
			BatchIndex: 100, // Same batch for all trades in this test
			Index:      1,   // Lowest index but latest timestamp
			Price:      113522.0,
			Volume:     2.0,
			Timestamp:  time.UnixMilli(1760414159999), // Latest timestamp
		},
		{
			BatchIndex: 100, // Same batch for all trades in this test
			Index:      15,  // Highest index but middle timestamp
			Price:      113580.0,
			Volume:     4.0,
			Timestamp:  time.UnixMilli(1760414110000), // Middle timestamp
		},
	}

	// Calculate OHLCV for 1-minute timeframe
	timestamp := int64(1760414100000) // Start of 1-minute candle
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, nil)

	// Verify the results
	assert.NotNil(t, result)
	assert.Equal(t, "BTC", result.Symbol)
	assert.Equal(t, timestamp, result.Timestamp)
	assert.Equal(t, "1m", result.Timeframe)

	// Verify OHLCV values based on timestamp ordering (not index ordering)
	assert.Equal(t, 113598.0, result.Open, "Open should be price from earliest timestamp")
	assert.Equal(t, 113522.0, result.Close, "Close should be price from latest timestamp")
	assert.Equal(t, 113598.0, result.High, "High should be maximum price")
	assert.Equal(t, 113522.0, result.Low, "Low should be minimum price")
	assert.Equal(t, 14.0, result.Volume, "Volume should be sum of all trade volumes")
	assert.Equal(t, int64(4), result.TradesCount, "Should count all trades")

	// Verify timestamp tracking
	assert.Equal(t, int64(1760414100000), result.OpenTimestamp, "Open timestamp should match earliest trade")
	assert.Equal(t, int64(1760414159999), result.CloseTimestamp, "Close timestamp should match latest trade")

	// Verify index tracking (for backward compatibility)
	assert.Equal(t, 10, result.OpenIndex, "Open index should match trade with earliest timestamp")
	assert.Equal(t, 1, result.CloseIndex, "Close index should match trade with latest timestamp")
}

func TestCalculateOHLCVForBucket_WithExistingKline(t *testing.T) {
	service := &NATSConsumerService{}

	// Create existing kline data
	existingKline := &RedisKline{
		Timestamp:       1760414100000,
		Symbol:          "BTC",
		Open:            113600.0,
		High:            113650.0,
		Low:             113500.0,
		Close:           113550.0,
		Volume:          10.0,
		TradesCount:     5,
		Timeframe:       "1m",
		OpenIndex:       1,
		CloseIndex:      5,
		OpenBatchIndex:  100,
		CloseBatchIndex: 100,
		OpenTimestamp:   1760414100000,
		CloseTimestamp:  1760414150000,
	}

	// New trades that should update the existing kline
	trades := []TradeData{
		{
			BatchIndex: 200,      // Different batch from existing data
			Index:      20,       // Higher index but earlier timestamp than existing close
			Price:      113700.0, // New high
			Volume:     2.0,
			Timestamp:  time.UnixMilli(1760414080000), // Earlier than existing open
		},
		{
			BatchIndex: 200,      // Same batch as previous trade
			Index:      25,       // Higher index and later timestamp
			Price:      113400.0, // New low
			Volume:     3.0,
			Timestamp:  time.UnixMilli(1760414180000), // Later than existing close
		},
	}

	timestamp := int64(1760414100000)
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, existingKline)

	// Verify the results
	assert.NotNil(t, result)

	// Open should be updated to the earlier trade
	assert.Equal(t, 113700.0, result.Open, "Open should be updated to price from earliest timestamp")
	assert.Equal(t, int64(1760414080000), result.OpenTimestamp, "Open timestamp should be updated")
	assert.Equal(t, 20, result.OpenIndex, "Open index should be updated")

	// Close should be updated to the later trade
	assert.Equal(t, 113400.0, result.Close, "Close should be updated to price from latest timestamp")
	assert.Equal(t, int64(1760414180000), result.CloseTimestamp, "Close timestamp should be updated")
	assert.Equal(t, 25, result.CloseIndex, "Close index should be updated")

	// High should be updated
	assert.Equal(t, 113700.0, result.High, "High should be updated to new maximum")

	// Low should be updated
	assert.Equal(t, 113400.0, result.Low, "Low should be updated to new minimum")

	// Volume and count should be accumulated
	assert.Equal(t, 15.0, result.Volume, "Volume should be accumulated")
	assert.Equal(t, int64(7), result.TradesCount, "Trade count should be accumulated")
}

func TestCalculateOHLCVForBucket_EmptyTrades(t *testing.T) {
	service := &NATSConsumerService{}

	existingKline := &RedisKline{
		Timestamp:       1760414100000,
		Symbol:          "BTC",
		Open:            113600.0,
		High:            113650.0,
		Low:             113500.0,
		Close:           113550.0,
		Volume:          10.0,
		TradesCount:     5,
		Timeframe:       "1m",
		OpenIndex:       0,
		CloseIndex:      0,
		OpenBatchIndex:  0,
		CloseBatchIndex: 0,
		OpenTimestamp:   1760414100000,
		CloseTimestamp:  1760414100000,
	}

	// Test with empty trades
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, 1760414100000, []TradeData{}, existingKline)

	// Should return the existing kline unchanged
	assert.Equal(t, existingKline, result)

	// Test with nil existing kline and empty trades
	result = service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, 1760414100000, []TradeData{}, nil)

	// Should return nil
	assert.Nil(t, result)
}

func TestCalculateOHLCVForBucket_SameTimestampTiebreaker(t *testing.T) {
	service := &NATSConsumerService{}

	// Test trades with same timestamp but different indices
	trades := []TradeData{
		{
			BatchIndex: 300, // Same batch for all trades
			Index:      10,
			Price:      113600.0,
			Volume:     2.0,
			Timestamp:  time.UnixMilli(1760414100000), // Same timestamp
		},
		{
			BatchIndex: 300, // Same batch
			Index:      5,   // Lower index - should be open
			Price:      113550.0,
			Volume:     3.0,
			Timestamp:  time.UnixMilli(1760414100000), // Same timestamp
		},
		{
			BatchIndex: 300, // Same batch
			Index:      15,  // Higher index - should be close
			Price:      113580.0,
			Volume:     1.0,
			Timestamp:  time.UnixMilli(1760414100000), // Same timestamp
		},
	}

	timestamp := int64(1760414100000)
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, nil)

	// When timestamps are equal, index should be used as tiebreaker
	assert.Equal(t, 113550.0, result.Open, "Open should be from trade with lowest index when timestamps are equal")
	assert.Equal(t, 113580.0, result.Close, "Close should be from trade with highest index when timestamps are equal")
	assert.Equal(t, 5, result.OpenIndex, "Open index should be lowest")
	assert.Equal(t, 15, result.CloseIndex, "Close index should be highest")
}

func TestCalculateOHLCVForBucket_RealWorldScenario(t *testing.T) {
	service := &NATSConsumerService{}

	// Test case based on the exact issue reported
	// This simulates the scenario where index order != timestamp order
	// Expected correct values (from data verification):
	// - Open: 113598.0 (first by timestamp)
	// - Close: 113522.0 (last by timestamp)
	// - High: 113598.0
	// - Low: 113522.0

	trades := []TradeData{
		// Trade that was processed first (low index) but happened in middle (middle timestamp)
		{
			BatchIndex: 334, // Same batch as in the original issue
			Index:      0,
			Price:      113503.0, // This was incorrectly used as close in the bug
			Volume:     5.637599999999978,
			Timestamp:  time.UnixMilli(1760414130000), // Middle timestamp
		},
		// Trade that was processed later (high index) but happened first (earliest timestamp)
		{
			BatchIndex: 334, // Same batch
			Index:      96,
			Price:      113598.0, // This should be the correct open
			Volume:     7.0,
			Timestamp:  time.UnixMilli(1760414100000), // Earliest timestamp
		},
		// Trade that should be the actual close (latest timestamp)
		{
			BatchIndex: 334,      // Same batch
			Index:      50,       // Middle index
			Price:      113522.0, // This should be the correct close
			Volume:     5.65632,
			Timestamp:  time.UnixMilli(1760414159999), // Latest timestamp in the window
		},
	}

	// Calculate OHLCV for 1-minute timeframe starting at 1760414100000
	timestamp := int64(1760414100000)
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, nil)

	// Verify the results match the expected correct values
	assert.NotNil(t, result)
	assert.Equal(t, "BTC", result.Symbol)
	assert.Equal(t, timestamp, result.Timestamp)
	assert.Equal(t, "1m", result.Timeframe)

	// These should now be correct (based on timestamp, not index)
	assert.Equal(t, 113598.0, result.Open, "Open should be from earliest timestamp (index 96)")
	assert.Equal(t, 113522.0, result.Close, "Close should be from latest timestamp (index 50)")
	assert.Equal(t, 113598.0, result.High, "High should be maximum price")
	assert.Equal(t, 113503.0, result.Low, "Low should be minimum price")

	// Volume should be sum of all trades
	expectedVolume := 5.637599999999978 + 7.0 + 5.65632
	assert.InDelta(t, expectedVolume, result.Volume, 0.000001, "Volume should be sum of all trade volumes")
	assert.Equal(t, int64(3), result.TradesCount, "Should count all trades")

	// Verify timestamp tracking
	assert.Equal(t, int64(1760414100000), result.OpenTimestamp, "Open timestamp should be earliest")
	assert.Equal(t, int64(1760414159999), result.CloseTimestamp, "Close timestamp should be latest")

	// Verify index tracking (for backward compatibility)
	assert.Equal(t, 96, result.OpenIndex, "Open index should match trade with earliest timestamp")
	assert.Equal(t, 50, result.CloseIndex, "Close index should match trade with latest timestamp")
}

func TestTimestampBucketingIssue(t *testing.T) {
	service := &NATSConsumerService{}

	// Reproduce the exact issue: trades from 1760416020000-1760416079999 window
	// being incorrectly assigned to 1760416140000 bucket
	trades := []TradeData{
		{
			BatchIndex: 400, // Different batch to test cross-batch scenarios
			Index:      7,
			Price:      113434.0, // First trade price
			Volume:     0.01639,
			Timestamp:  time.UnixMilli(1760416020030), // Should be in 1760416020000 bucket
		},
		{
			BatchIndex: 400, // Same batch
			Index:      40,
			Price:      113367.0, // Last trade price
			Volume:     0.00668,
			Timestamp:  time.UnixMilli(1760416079044), // Should be in 1760416020000 bucket
		},
		{
			BatchIndex: 400, // Same batch
			Index:      25,
			Price:      113435.0, // High price
			Volume:     0.05,
			Timestamp:  time.UnixMilli(1760416050000), // Should be in 1760416020000 bucket
		},
	}

	// Test the bucket calculation directly
	for _, trade := range trades {
		bucketTime := model.Timeframe1m.TruncateTime(trade.Timestamp)
		expectedBucket := int64(1760416020000)
		actualBucket := bucketTime.UnixMilli()

		assert.Equal(t, expectedBucket, actualBucket,
			"Trade at %d should be in bucket %d, but got %d",
			trade.Timestamp.UnixMilli(), expectedBucket, actualBucket)
	}

	// Test the OHLCV calculation with correct bucket
	correctBucket := int64(1760416020000)
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, correctBucket, trades, nil)

	// Verify the results
	assert.NotNil(t, result)
	assert.Equal(t, correctBucket, result.Timestamp, "Kline timestamp should match bucket")
	assert.Equal(t, 113434.0, result.Open, "Open should be from earliest timestamp")
	assert.Equal(t, 113367.0, result.Close, "Close should be from latest timestamp")
	assert.Equal(t, 113435.0, result.High, "High should be maximum price")
	assert.Equal(t, 113367.0, result.Low, "Low should be minimum price")

	// Critical check: open_timestamp should be within the candle's time window
	assert.True(t, result.OpenTimestamp >= correctBucket,
		"Open timestamp (%d) should be >= candle start (%d)", result.OpenTimestamp, correctBucket)
	assert.True(t, result.OpenTimestamp < correctBucket+60000,
		"Open timestamp (%d) should be < candle end (%d)", result.OpenTimestamp, correctBucket+60000)

	// Critical check: close_timestamp should be within the candle's time window
	assert.True(t, result.CloseTimestamp >= correctBucket,
		"Close timestamp (%d) should be >= candle start (%d)", result.CloseTimestamp, correctBucket)
	assert.True(t, result.CloseTimestamp < correctBucket+60000,
		"Close timestamp (%d) should be < candle end (%d)", result.CloseTimestamp, correctBucket+60000)
}

func TestProcessTradesInMemoryBucketingIssue(t *testing.T) {
	// Initialize logger to avoid nil pointer
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}

	service := &NATSConsumerService{}

	// Simulate the exact scenario from the issue
	tradesBySymbol := map[string][]TradeData{
		"BTC": {
			{
				Index:     7,
				Price:     113434.0,
				Volume:    0.01639,
				Timestamp: time.UnixMilli(1760416020030), // Should go to 1760416020000 bucket
			},
			{
				Index:     40,
				Price:     113367.0,
				Volume:    0.00668,
				Timestamp: time.UnixMilli(1760416079044), // Should go to 1760416020000 bucket
			},
		},
	}

	// Test with no existing data
	existingData := make(map[string]*RedisKline)

	// Process trades in memory
	result := service.processTradesInMemory(tradesBySymbol, existingData)

	// Check that the correct bucket was created
	expectedBucket := int64(1760416020000)
	expectedKey := fmt.Sprintf("kline:BTC:1m:%d", expectedBucket)

	kline, exists := result[expectedKey]
	assert.True(t, exists, "Expected kline should exist for bucket %d", expectedBucket)
	assert.NotNil(t, kline, "Kline should not be nil")

	if kline != nil {
		assert.Equal(t, expectedBucket, kline.Timestamp, "Kline timestamp should match expected bucket")
		assert.Equal(t, 113434.0, kline.Open, "Open should be from earliest timestamp")
		assert.Equal(t, 113367.0, kline.Close, "Close should be from latest timestamp")

		// Critical: verify timestamps are within the correct window
		assert.True(t, kline.OpenTimestamp >= expectedBucket,
			"Open timestamp (%d) should be >= bucket start (%d)", kline.OpenTimestamp, expectedBucket)
		assert.True(t, kline.OpenTimestamp < expectedBucket+60000,
			"Open timestamp (%d) should be < bucket end (%d)", kline.OpenTimestamp, expectedBucket+60000)
		assert.True(t, kline.CloseTimestamp >= expectedBucket,
			"Close timestamp (%d) should be >= bucket start (%d)", kline.CloseTimestamp, expectedBucket)
		assert.True(t, kline.CloseTimestamp < expectedBucket+60000,
			"Close timestamp (%d) should be < bucket end (%d)", kline.CloseTimestamp, expectedBucket+60000)
	}

	// Verify no other buckets were created
	wrongBucket := int64(1760416140000)
	wrongKey := fmt.Sprintf("kline:BTC:1m:%d", wrongBucket)
	_, wrongExists := result[wrongKey]
	assert.False(t, wrongExists, "Wrong bucket %d should not exist", wrongBucket)
}

// Commented out - this test was for a different scenario
/*
func TestExistingKlineDataMergeIssue(t *testing.T) {
	// Initialize logger to avoid nil pointer
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}

	service := &NATSConsumerService{}

	// Simulate the scenario where there's existing kline data in the wrong bucket
	// This might be causing trades to be merged into the wrong candle
	existingData := map[string]*RedisKline{
		"kline:BTC:1m:1760416140000": { // Wrong bucket (2 minutes later)
			Timestamp:       1760416140000,
			Symbol:          "BTC",
			Open:            113354.0,
			High:            113420.0,
			Low:             113343.0,
			Close:           113415.0,
			Volume:          2.4156199999999974,
			TradesCount:     191,
			Timeframe:       "1m",
			OpenIndex:       7,
			CloseIndex:      40,
			OpenBatchIndex:  500,
			CloseBatchIndex: 500,
			OpenTimestamp:   1760416141081, // This is AFTER the candle start - suspicious!
			CloseTimestamp:  1760416199255, // This is way outside the 1-minute window
		},
	}

	// New trades that should go to the correct bucket (1760416020000)
	tradesBySymbol := map[string][]TradeData{
		"BTC": {
			{
				Index:     7,
				Price:     113434.0,
				Volume:    0.01639,
				Timestamp: time.UnixMilli(1760416020030), // Should go to 1760416020000 bucket
			},
			{
				Index:     40,
				Price:     113367.0,
				Volume:    0.00668,
				Timestamp: time.UnixMilli(1760416079044), // Should go to 1760416020000 bucket
			},
		},
	}

	// Process trades in memory
	result := service.processTradesInMemory(tradesBySymbol, existingData)

	// The correct bucket should be created
	correctBucket := int64(1760416020000)
	correctKey := fmt.Sprintf("kline:BTC:1m:%d", correctBucket)

	correctKline, correctExists := result[correctKey]
	assert.True(t, correctExists, "Correct bucket %d should exist", correctBucket)
	assert.NotNil(t, correctKline, "Correct kline should not be nil")

	if correctKline != nil {
		assert.Equal(t, correctBucket, correctKline.Timestamp, "Correct kline timestamp should match bucket")
		assert.Equal(t, 113434.0, correctKline.Open, "Open should be from earliest timestamp")
		assert.Equal(t, 113367.0, correctKline.Close, "Close should be from latest timestamp")

		// Verify timestamps are within the correct window
		assert.True(t, correctKline.OpenTimestamp >= correctBucket,
			"Open timestamp (%d) should be >= correct bucket start (%d)", correctKline.OpenTimestamp, correctBucket)
		assert.True(t, correctKline.OpenTimestamp < correctBucket+60000,
			"Open timestamp (%d) should be < correct bucket end (%d)", correctKline.OpenTimestamp, correctBucket+60000)
	}

	// The wrong bucket should still exist but unchanged
	wrongBucket := int64(1760416140000)
	wrongKey := fmt.Sprintf("kline:BTC:1m:%d", wrongBucket)

	wrongKline, wrongExists := result[wrongKey]
	assert.True(t, wrongExists, "Wrong bucket %d should still exist (unchanged)", wrongBucket)
	assert.NotNil(t, wrongKline, "Wrong kline should not be nil")

	if wrongKline != nil {
		// The wrong bucket should be unchanged since no trades belong to it
		assert.Equal(t, wrongBucket, wrongKline.Timestamp, "Wrong kline timestamp should match its bucket")
		assert.Equal(t, 113354.0, wrongKline.Open, "Wrong bucket open should be unchanged")
		assert.Equal(t, 113415.0, wrongKline.Close, "Wrong bucket close should be unchanged")
		assert.Equal(t, int64(191), wrongKline.TradesCount, "Wrong bucket trade count should be unchanged")
	}
}
*/

func TestRedisKeyCollisionBugFix(t *testing.T) {
	// Initialize logger to avoid nil pointer
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}

	service := &NATSConsumerService{}

	// Test the specific bug: multiple trades for same symbol/timeframe but different time buckets
	// This should generate multiple Redis keys that were previously colliding
	tradesBySymbol := map[string][]TradeData{
		"BTC": {
			// Trade in first minute bucket (1760416020000)
			{
				Index:     1,
				Price:     113434.0,
				Volume:    0.01639,
				Timestamp: time.UnixMilli(1760416020030),
			},
			// Trade in second minute bucket (1760416080000)
			{
				Index:     2,
				Price:     113400.0,
				Volume:    0.02,
				Timestamp: time.UnixMilli(1760416080030),
			},
			// Trade in third minute bucket (1760416140000)
			{
				Index:     3,
				Price:     113500.0,
				Volume:    0.03,
				Timestamp: time.UnixMilli(1760416140030),
			},
		},
	}

	// Test generateRedisKeys function
	redisKeys := service.generateRedisKeys(tradesBySymbol)

	// Should generate 3 different keys for 1m timeframe (one for each minute bucket)
	oneMinuteKeys := []RedisKeyInfo{}
	for _, keyInfo := range redisKeys {
		if keyInfo.Timeframe == model.Timeframe1m {
			oneMinuteKeys = append(oneMinuteKeys, keyInfo)
		}
	}

	assert.Equal(t, 3, len(oneMinuteKeys), "Should generate 3 different 1m keys for 3 different time buckets")

	// Verify the timestamps are correct
	expectedTimestamps := []int64{1760416020000, 1760416080000, 1760416140000}
	actualTimestamps := []int64{}
	for _, keyInfo := range oneMinuteKeys {
		actualTimestamps = append(actualTimestamps, keyInfo.Timestamp)
	}

	// Sort both slices for comparison
	sort.Slice(expectedTimestamps, func(i, j int) bool { return expectedTimestamps[i] < expectedTimestamps[j] })
	sort.Slice(actualTimestamps, func(i, j int) bool { return actualTimestamps[i] < actualTimestamps[j] })

	assert.Equal(t, expectedTimestamps, actualTimestamps, "Generated timestamps should match expected buckets")

	// Test the full pipeline
	existingData := make(map[string]*RedisKline)
	result := service.processTradesInMemory(tradesBySymbol, existingData)

	// Should create 3 separate klines for the 3 different minute buckets
	bucket1Key := fmt.Sprintf("kline:BTC:1m:%d", 1760416020000)
	bucket2Key := fmt.Sprintf("kline:BTC:1m:%d", 1760416080000)
	bucket3Key := fmt.Sprintf("kline:BTC:1m:%d", 1760416140000)

	kline1, exists1 := result[bucket1Key]
	kline2, exists2 := result[bucket2Key]
	kline3, exists3 := result[bucket3Key]

	assert.True(t, exists1, "First bucket should exist")
	assert.True(t, exists2, "Second bucket should exist")
	assert.True(t, exists3, "Third bucket should exist")

	if kline1 != nil && kline2 != nil && kline3 != nil {
		assert.Equal(t, 113434.0, kline1.Open, "First bucket should have correct price")
		assert.Equal(t, 113400.0, kline2.Open, "Second bucket should have correct price")
		assert.Equal(t, 113500.0, kline3.Open, "Third bucket should have correct price")

		assert.Equal(t, int64(1760416020000), kline1.Timestamp, "First bucket should have correct timestamp")
		assert.Equal(t, int64(1760416080000), kline2.Timestamp, "Second bucket should have correct timestamp")
		assert.Equal(t, int64(1760416140000), kline3.Timestamp, "Third bucket should have correct timestamp")
	}
}

func TestCalculateOHLCVForBucket_CrossBatchOrdering(t *testing.T) {
	service := &NATSConsumerService{}

	// Test the exact scenario from the bug report:
	// BatchIndex resets to 1 at the beginning of each batch
	// We need to ensure (BatchIndex, Index) composite ordering works correctly
	trades := []TradeData{
		// Trade from batch 334, index 33 (should be processed before batch 335 trades)
		{
			BatchIndex: 334,
			Index:      33,
			Price:      106805.0, // This should be the Open price
			Volume:     0.1,
			Timestamp:  time.UnixMilli(1760769240000), // Same timestamp as in bug report
		},
		// Trade from batch 335, index 1 (should be processed after batch 334 trades)
		{
			BatchIndex: 335,
			Index:      1,
			Price:      106796.0, // This was incorrectly used as Open in the bug
			Volume:     0.2,
			Timestamp:  time.UnixMilli(1760769240000), // Same timestamp
		},
		// Trade from batch 335, index 5 (should be the Close)
		{
			BatchIndex: 335,
			Index:      5,
			Price:      106784.0, // This should be the Close price
			Volume:     0.58434,
			Timestamp:  time.UnixMilli(1760769299999), // End of the minute
		},
	}

	// Calculate OHLCV for 1-minute timeframe
	timestamp := int64(1760769240000) // Start of the minute from bug report
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, nil)

	// Verify the results match the expected Hyperliquid API response
	assert.NotNil(t, result)
	assert.Equal(t, "BTC", result.Symbol)
	assert.Equal(t, timestamp, result.Timestamp)
	assert.Equal(t, "1m", result.Timeframe)

	// The key assertion: Open should be 106805.0 (from batch 334, index 33)
	// NOT 106796.0 (from batch 335, index 1)
	assert.Equal(t, 106805.0, result.Open, "Open should be from earliest (BatchIndex, Index) combination")
	assert.Equal(t, 106784.0, result.Close, "Close should be from latest timestamp")
	assert.Equal(t, 106805.0, result.High, "High should be maximum price")
	assert.Equal(t, 106784.0, result.Low, "Low should be minimum price")
	assert.Equal(t, 0.88434, result.Volume, "Volume should be sum of all trade volumes")
	assert.Equal(t, int64(3), result.TradesCount, "Trades count should be 3")
}

func TestCalculateOHLCVForBucket_ExactBugReproduction(t *testing.T) {
	service := &NATSConsumerService{}

	// Test with the exact scenario from the bug report:
	// Expected: Open=106805.0, Close=106784.0 (from Hyperliquid API)
	// Actual (before fix): Open=106796.0, Close=106784.0 (incorrect Open)
	trades := []TradeData{
		// Simulate multiple trades from different batches with indices that reset
		// This represents the real scenario where batch_index increments but index resets to 1
		{
			BatchIndex: 334,
			Index:      33,
			Price:      106805.0, // This should be the Open (earliest by composite ordering)
			Volume:     0.1,
			Timestamp:  time.UnixMilli(1760769240000),
		},
		{
			BatchIndex: 335,
			Index:      1,        // Index resets to 1 in new batch
			Price:      106796.0, // This was incorrectly used as Open before the fix
			Volume:     0.2,
			Timestamp:  time.UnixMilli(1760769240000), // Same timestamp
		},
		{
			BatchIndex: 335,
			Index:      3,
			Price:      106800.0,
			Volume:     0.15,
			Timestamp:  time.UnixMilli(1760769250000),
		},
		{
			BatchIndex: 335,
			Index:      5,
			Price:      106784.0, // This should be the Close (latest timestamp)
			Volume:     0.53434,
			Timestamp:  time.UnixMilli(1760769299999),
		},
	}

	timestamp := int64(1760769240000)
	result := service.CalculateOHLCVForBucket("BTC", model.Timeframe1m, timestamp, trades, nil)

	// Verify the fix: Open should be 106805.0 (from batch 334, index 33)
	// NOT 106796.0 (from batch 335, index 1)
	assert.NotNil(t, result)
	assert.Equal(t, 106805.0, result.Open, "Open should be 106805.0 from batch 334, index 33")
	assert.Equal(t, 106784.0, result.Close, "Close should be 106784.0 from latest timestamp")
	assert.Equal(t, 106805.0, result.High, "High should be 106805.0")
	assert.Equal(t, 106784.0, result.Low, "Low should be 106784.0")
	assert.InDelta(t, 0.98434, result.Volume, 0.0001, "Volume should be sum of all trades")
	assert.Equal(t, int64(4), result.TradesCount, "Should have 4 trades")

	// Verify that the composite ordering is working correctly
	assert.Equal(t, 334, result.OpenBatchIndex, "Open should be from batch 334")
	assert.Equal(t, 33, result.OpenIndex, "Open should be from index 33")
	assert.Equal(t, 335, result.CloseBatchIndex, "Close should be from batch 335")
	assert.Equal(t, 5, result.CloseIndex, "Close should be from index 5")
}
