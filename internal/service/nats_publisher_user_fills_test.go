package service

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPublishOptimizedUserFillWithUserFillsFormat tests the NATS publisher with the new user_fills format
func TestPublishOptimizedUserFillWithUserFillsFormat(t *testing.T) {
	t.Run("Single_trade_user_fills", func(t *testing.T) {
		// Create a user_fills message with a single trade
		message := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			},
			"timestamp":     int64(1760344053318),
			"batch_index":   12345,
			"message_index": 0,
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		// Test that the message parsing works correctly (simulating PublishOptimizedUserFill logic)
		var wsMessage struct {
			Type string        `json:"type"`
			Data []interface{} `json:"data"`
		}

		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		assert.Equal(t, "user_fills", wsMessage.Type)
		assert.Len(t, wsMessage.Data, 1)

		// Verify the trade data structure
		tradeArray, ok := wsMessage.Data[0].([]interface{})
		require.True(t, ok)
		assert.Len(t, tradeArray, 6)

		// Verify individual fields
		assert.Equal(t, float64(1), tradeArray[0])             // Index
		assert.Equal(t, "BTC", tradeArray[1])                  // Coin
		assert.Equal(t, "50000.00", tradeArray[2])             // Price
		assert.Equal(t, "0.1", tradeArray[3])                  // Size
		assert.Equal(t, float64(1760344053318), tradeArray[4]) // Timestamp
		assert.Equal(t, "0xabc123", tradeArray[5])             // Hash

		t.Logf("Successfully parsed user_fills message with %d trades", len(wsMessage.Data))
	})

	t.Run("Multiple_trades_user_fills", func(t *testing.T) {
		// Create a user_fills message with multiple trades
		message := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
				[]interface{}{2, "ETH", "3000.50", "2.5", float64(1760344053319), "0xdef456"},
				[]interface{}{3, "DOGE", "0.21041", "5000.0", float64(1760344053320), "0x789xyz"},
			},
			"timestamp":     int64(1760344053321),
			"batch_index":   12346,
			"message_index": 1,
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		// Test that the message parsing works correctly
		var wsMessage struct {
			Type string        `json:"type"`
			Data []interface{} `json:"data"`
		}

		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		assert.Equal(t, "user_fills", wsMessage.Type)
		assert.Len(t, wsMessage.Data, 3)

		// Verify each trade
		expectedTrades := []struct {
			index int
			coin  string
			price string
			size  string
		}{
			{1, "BTC", "50000.00", "0.1"},
			{2, "ETH", "3000.50", "2.5"},
			{3, "DOGE", "0.21041", "5000.0"},
		}

		for i, expected := range expectedTrades {
			tradeArray, ok := wsMessage.Data[i].([]interface{})
			require.True(t, ok, "Trade %d should be an array", i)
			assert.Len(t, tradeArray, 6, "Trade %d should have 6 elements", i)

			assert.Equal(t, float64(expected.index), tradeArray[0], "Trade %d index", i)
			assert.Equal(t, expected.coin, tradeArray[1], "Trade %d coin", i)
			assert.Equal(t, expected.price, tradeArray[2], "Trade %d price", i)
			assert.Equal(t, expected.size, tradeArray[3], "Trade %d size", i)
		}

		t.Logf("Successfully parsed user_fills message with %d trades", len(wsMessage.Data))
	})

	t.Run("Invalid_message_type", func(t *testing.T) {
		// Test that non-user_fills messages are rejected
		message := map[string]interface{}{
			"type":      "user_fill", // Old format should be rejected
			"data":      []interface{}{1, "BTC", "50000.00", "0.1", float64(1760344053318), "0xabc123"},
			"timestamp": int64(1760344053318),
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		// This would fail in the actual PublishOptimizedUserFill method
		var wsMessage struct {
			Type string        `json:"type"`
			Data []interface{} `json:"data"`
		}

		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		// Verify it's the old format
		assert.Equal(t, "user_fill", wsMessage.Type)
		assert.Len(t, wsMessage.Data, 6) // Direct array, not nested

		t.Logf("Correctly identified old format message type: %s", wsMessage.Type)
	})

	t.Run("Empty_data_array", func(t *testing.T) {
		// Test that empty data arrays are handled
		message := map[string]interface{}{
			"type":      "user_fills",
			"data":      []interface{}{},
			"timestamp": int64(1760344053318),
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		var wsMessage struct {
			Type string        `json:"type"`
			Data []interface{} `json:"data"`
		}

		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		assert.Equal(t, "user_fills", wsMessage.Type)
		assert.Len(t, wsMessage.Data, 0)

		t.Logf("Correctly handled empty data array")
	})

	t.Run("Invalid_trade_format", func(t *testing.T) {
		// Test that invalid trade formats are handled
		message := map[string]interface{}{
			"type": "user_fills",
			"data": []interface{}{
				[]interface{}{1, "BTC", "50000.00", "0.1"}, // Missing timestamp and hash
				"invalid_trade_format",                     // Not an array
			},
			"timestamp": int64(1760344053318),
		}

		messageBytes, err := json.Marshal(message)
		require.NoError(t, err)

		var wsMessage struct {
			Type string        `json:"type"`
			Data []interface{} `json:"data"`
		}

		err = json.Unmarshal(messageBytes, &wsMessage)
		require.NoError(t, err)

		assert.Equal(t, "user_fills", wsMessage.Type)
		assert.Len(t, wsMessage.Data, 2)

		// First trade is invalid (too few elements)
		tradeArray1, ok := wsMessage.Data[0].([]interface{})
		assert.True(t, ok)
		assert.Len(t, tradeArray1, 4) // Should be 6

		// Second trade is not an array
		_, ok = wsMessage.Data[1].([]interface{})
		assert.False(t, ok)

		t.Logf("Correctly identified invalid trade formats")
	})
}
