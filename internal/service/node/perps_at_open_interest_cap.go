package node

type PerpsAtOpenInterestCap struct {
	Tokens []string `json:"tokens"`
}

func NewPerpsAtOpenInterestCap() *PerpsAtOpenInterestCap {
	return &PerpsAtOpenInterestCap{
		Tokens: []string{"BADGER", "<PERSON>NT<PERSON>", "F<PERSON>", "<PERSON>O<PERSON>", "PURR"},
	}
}

func (p *PerpsAtOpenInterestCap) GetTokens() []string {
	return p.Tokens
}

func (p *PerpsAtOpenInterestCap) ContainsToken(token string) bool {
	for _, t := range p.Tokens {
		if t == token {
			return true
		}
	}
	return false
}

func (p *PerpsAtOpenInterestCap) AddToken(token string) {
	if !p.ContainsToken(token) {
		p.Tokens = append(p.Tokens, token)
	}
}

func (p *PerpsAtOpenInterestCap) RemoveToken(token string) {
	for i, t := range p.Tokens {
		if t == token {
			p.Tokens = append(p.Token<PERSON>[:i], p.Tokens[i+1:]...)
			break
		}
	}
}
