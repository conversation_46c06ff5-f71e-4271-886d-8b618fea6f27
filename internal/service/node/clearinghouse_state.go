package node

// ClearinghouseState represents the complete clearinghouse state data
type ClearinghouseState struct {
	AssetPositions             []AssetPosition `json:"assetPositions"`
	CrossMaintenanceMarginUsed string          `json:"crossMaintenanceMarginUsed"`
	CrossMarginSummary         MarginSummary   `json:"crossMarginSummary"`
	MarginSummary              MarginSummary   `json:"marginSummary"`
	Time                       int64           `json:"time"`
	Withdrawable               string          `json:"withdrawable"`
}

// AssetPosition represents an asset position in the clearinghouse state
type AssetPosition struct {
	Position PositionData `json:"position"`
	Type     string       `json:"type"`
}

// PositionData represents detailed position information
type PositionData struct {
	Coin           string     `json:"coin"`
	CumFunding     CumFunding `json:"cumFunding"`
	EntryPx        string     `json:"entryPx"`
	Leverage       Leverage   `json:"leverage"`
	LiquidationPx  string     `json:"liquidationPx"`
	MarginUsed     string     `json:"marginUsed"`
	MaxLeverage    int        `json:"maxLeverage"`
	PositionValue  string     `json:"positionValue"`
	ReturnOnEquity string     `json:"returnOnEquity"`
	Szi            string     `json:"szi"`
	UnrealizedPnl  string     `json:"unrealizedPnl"`
}

// CumFunding represents cumulative funding information
type CumFunding struct {
	AllTime     string `json:"allTime"`
	SinceChange string `json:"sinceChange"`
	SinceOpen   string `json:"sinceOpen"`
}

// Leverage represents leverage information
type Leverage struct {
	RawUsd string `json:"rawUsd"`
	Type   string `json:"type"`
	Value  int    `json:"value"`
}

// MarginSummary represents margin summary information
type MarginSummary struct {
	AccountValue    string `json:"accountValue"`
	TotalMarginUsed string `json:"totalMarginUsed"`
	TotalNtlPos     string `json:"totalNtlPos"`
	TotalRawUsd     string `json:"totalRawUsd"`
}
