package node

import (
	"encoding/json"
)

// LeadingVault represents a leading vault
type LeadingVault struct {
	VaultAddress string `json:"vaultAddress"`
	Equity       string `json:"equity"`
	APY          string `json:"apy"`
}

// Meta represents metadata information
type Meta struct {
	Universe        []WebUniverseItem  `json:"universe"`
	MarginTables    []MarginTableEntry `json:"marginTables"`
	CollateralToken int                `json:"collateralToken"`
}

// UnmarshalJSON [[id, data], [id, data]]
func (m *Meta) UnmarshalJSON(data []byte) error {
	type Alias Meta
	aux := &struct {
		MarginTables []json.RawMessage `json:"marginTables"`
		*Alias
	}{
		Alias: (*Alias)(m),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 手动解析 marginTables 数组格式
	for _, raw := range aux.MarginTables {
		var entry []json.RawMessage
		if err := json.Unmarshal(raw, &entry); err != nil {
			return err
		}

		if len(entry) != 2 {
			continue
		}

		var id int
		if err := json.Unmarshal(entry[0], &id); err != nil {
			return err
		}

		var data MarginTableData
		if err := json.Unmarshal(entry[1], &data); err != nil {
			return err
		}

		m.MarginTables = append(m.MarginTables, MarginTableEntry{
			AssetID: id,
			Data:    data,
		})
	}

	return nil
}

// UniverseItem represents an item in the universe
type WebUniverseItem struct {
	SzDecimals    int    `json:"szDecimals"`
	Name          string `json:"name"`
	MaxLeverage   int    `json:"maxLeverage"`
	MarginTableId int    `json:"marginTableId"`
	IsDelisted    *bool  `json:"isDelisted,omitempty"`
	OnlyIsolated  *bool  `json:"onlyIsolated,omitempty"`
}

// AssetCtx represents an asset context
type AssetCtx struct {
	AssetID int    `json:"assetId"`
	Name    string `json:"name"`
}

// TwapState represents a TWAP state
type TwapState struct {
	Coin string `json:"coin"`
	Twap string `json:"twap"`
}

// WebData2Request represents the request parameters for webData2 API
type WebData2Request struct {
	Type string `json:"type"` // eg "webData2"
	User string `json:"user"` // userAddress（option）
}

// WebData2Response represents the response structure for webData2 API
type WebData2Response struct {
	ClearinghouseState     ClearinghouseState `json:"clearinghouseState"`
	LeadingVaults          []LeadingVault     `json:"leadingVaults"`
	TotalVaultEquity       string             `json:"totalVaultEquity"`
	OpenOrders             []OpenOrder        `json:"openOrders"`
	AgentAddress           *string            `json:"agentAddress"`
	AgentValidUntil        *int64             `json:"agentValidUntil"`
	CumLedger              string             `json:"cumLedger"`
	Meta                   Meta               `json:"meta"`
	AssetCtxs              []AssetCtx         `json:"assetCtxs"`
	ServerTime             int64              `json:"serverTime"`
	IsVault                bool               `json:"isVault"`
	User                   string             `json:"user"`
	TwapStates             []TwapState        `json:"twapStates"`
	PerpsAtOpenInterestCap []string           `json:"perpsAtOpenInterestCap"`
}
