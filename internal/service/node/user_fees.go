package node

import "time"

type DailyUserVolume struct {
	Date      string `json:"date"`
	UserCross string `json:"userCross"`
	UserAdd   string `json:"userAdd"`
	Exchange  string `json:"exchange"`
}

type FeeTier struct {
	NtlCutoff           string `json:"ntlCutoff,omitempty"`
	Cross               string `json:"cross,omitempty"`
	Add                 string `json:"add,omitempty"`
	SpotCross           string `json:"spotCross,omitempty"`
	SpotAdd             string `json:"spotAdd,omitempty"`
	MakerFractionCutoff string `json:"makerFractionCutoff,omitempty"`
}

type FeeTiers struct {
	VIP []FeeTier `json:"vip"`
	MM  []FeeTier `json:"mm"`
}

type StakingDiscountTier struct {
	BpsOfMaxSupply string `json:"bpsOfMaxSupply"`
	Discount       string `json:"discount"`
}

type FeeSchedule struct {
	Cross                string                `json:"cross"`
	Add                  string                `json:"add"`
	SpotCross            string                `json:"spotCross"`
	SpotAdd              string                `json:"spotAdd"`
	Tiers                FeeTiers              `json:"tiers"`
	ReferralDiscount     string                `json:"referralDiscount"`
	StakingDiscountTiers []StakingDiscountTier `json:"stakingDiscountTiers"`
}

type StakingLink struct {
	Type        string `json:"type"`
	StakingUser string `json:"stakingUser"`
}

type ActiveStakingDiscount struct {
	BpsOfMaxSupply string `json:"bpsOfMaxSupply"`
	Discount       string `json:"discount"`
}

type UserFeesData struct {
	DailyUserVlm                []DailyUserVolume     `json:"dailyUserVlm"`
	FeeSchedule                 FeeSchedule           `json:"feeSchedule"`
	UserCrossRate               string                `json:"userCrossRate"`
	UserAddRate                 string                `json:"userAddRate"`
	UserSpotCrossRate           string                `json:"userSpotCrossRate"`
	UserSpotAddRate             string                `json:"userSpotAddRate"`
	ActiveReferralDiscount      string                `json:"activeReferralDiscount"`
	Trial                       *string               `json:"trial"`
	FeeTrialReward              string                `json:"feeTrialReward"`
	NextTrialAvailableTimestamp *time.Time            `json:"nextTrialAvailableTimestamp"`
	StakingLink                 StakingLink           `json:"stakingLink"`
	ActiveStakingDiscount       ActiveStakingDiscount `json:"activeStakingDiscount"`
}
