package node

import (
	"math/big"
)

type SpotDeployState struct {
	States     []TokenState `json:"states"`
	GasAuction GasAuction   `json:"gasAuction"`
}

type TokenState struct {
	Token                        int64                         `json:"token"`
	Spec                         TokenSpec                     `json:"spec"`
	FullName                     string                        `json:"fullName"`
	Spots                        []int64                       `json:"spots"`
	MaxSupply                    int64                         `json:"maxSupply"`
	HyperliquidityGenesisBalance string                        `json:"hyperliquidityGenesisBalance"`
	TotalGenesisBalanceWei       string                        `json:"totalGenesisBalanceWei"`
	UserGenesisBalances          []UserGenesisBalance          `json:"userGenesisBalances"`
	ExistingTokenGenesisBalances []ExistingTokenGenesisBalance `json:"existingTokenGenesisBalances"`
}

type TokenSpec struct {
	Name        string `json:"name"`
	SzDecimals  int    `json:"szDecimals"`
	WeiDecimals int    `json:"weiDecimals"`
}

type UserGenesisBalance struct {
	Address string `json:"address"`
	Balance string `json:"balance"`
}

type ExistingTokenGenesisBalance struct {
	TokenID int64  `json:"tokenId"`
	Balance string `json:"balance"`
}

type GasAuction struct {
	StartTimeSeconds int64   `json:"startTimeSeconds"`
	DurationSeconds  int64   `json:"durationSeconds"`
	StartGas         string  `json:"startGas"`
	CurrentGas       *string `json:"currentGas"` 
	EndGas           string  `json:"endGas"`
}

func (ts *TokenState) GetTotalGenesisBalanceWei() *big.Int {
	balance, ok := new(big.Int).SetString(ts.TotalGenesisBalanceWei, 10)
	if !ok {
		return big.NewInt(0)
	}
	return balance
}

func (ts *TokenState) GetTotalUserGenesisBalances() *big.Int {
	total := big.NewInt(0)
	for _, balance := range ts.UserGenesisBalances {
		balanceInt, ok := new(big.Int).SetString(balance.Balance, 10)
		if ok {
			total.Add(total, balanceInt)
		}
	}
	return total
}

func (ga *GasAuction) IsActive(currentTime int64) bool {
	return currentTime >= ga.StartTimeSeconds && currentTime <= ga.StartTimeSeconds+ga.DurationSeconds
}

func (ga *GasAuction) GetRemainingTime(currentTime int64) int64 {
	endTime := ga.StartTimeSeconds + ga.DurationSeconds
	if currentTime >= endTime {
		return 0
	}
	return endTime - currentTime
}
