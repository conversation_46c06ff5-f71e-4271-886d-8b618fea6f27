package node

import (
	"encoding/json"
)

type UniverseItem struct {
	Name         string `json:"name"`
	SzDecimals   int    `json:"szDecimals"`
	MaxLeverage  int    `json:"maxLeverage"`
	OnlyIsolated *bool  `json:"onlyIsolated,omitempty"`
	IsDelisted   *bool  `json:"isDelisted,omitempty"`
}

type MarginTier struct {
	LowerBound  string `json:"lowerBound"`
	MaxLeverage int    `json:"maxLeverage"`
}

type MarginTableData struct {
	Description string       `json:"description"`
	MarginTiers []MarginTier `json:"marginTiers"`
}

type MarginTableEntry struct {
	AssetID int             `json:"assetId"`
	Data    MarginTableData `json:"data"`
}

type MateData struct {
	Universe     []UniverseItem     `json:"universe"`
	MarginTables []MarginTableEntry `json:"marginTables"`
}

func (md *MateData) UnmarshalJSON(data []byte) error {
	type Alias MateData
	aux := &struct {
		MarginTables []json.RawMessage `json:"marginTables"`
		*Alias
	}{
		Alias: (*Alias)(md),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	for _, raw := range aux.MarginTables {
		var entry []json.RawMessage
		if err := json.Unmarshal(raw, &entry); err != nil {
			return err
		}

		if len(entry) != 2 {
			continue
		}

		var assetID int
		if err := json.Unmarshal(entry[0], &assetID); err != nil {
			return err
		}

		var data MarginTableData
		if err := json.Unmarshal(entry[1], &data); err != nil {
			return err
		}

		md.MarginTables = append(md.MarginTables, MarginTableEntry{
			AssetID: assetID,
			Data:    data,
		})
	}

	return nil
}
