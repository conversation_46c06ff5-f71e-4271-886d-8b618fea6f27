package node

import (
	"github.com/shopspring/decimal"
)

// SubAccountResponse represents the response structure for sub-account data
type SubAccountResponse struct {
	Name               string                 `json:"name"`
	SubAccountUser     string                 `json:"subAccountUser"`
	Master             string                 `json:"master"`
	ClearinghouseState ClearinghouseStateData `json:"clearinghouseState"`
	SpotState          SpotStateData          `json:"spotState"`
}

// ClearinghouseStateData represents the clearinghouse state data
type ClearinghouseStateData struct {
	MarginSummary              MarginSummaryData `json:"marginSummary"`
	CrossMarginSummary         MarginSummaryData `json:"crossMarginSummary"`
	CrossMaintenanceMarginUsed string            `json:"crossMaintenanceMarginUsed"`
	Withdrawable               string            `json:"withdrawable"`
	AssetPositions             []interface{}     `json:"assetPositions"`
	Time                       int64             `json:"time"`
}

// MarginSummaryData represents margin summary data
type MarginSummaryData struct {
	AccountValue    string `json:"accountValue"`
	TotalNtlPos     string `json:"totalNtlPos"`
	TotalRawUsd     string `json:"totalRawUsd"`
	TotalMarginUsed string `json:"totalMarginUsed"`
}

// SpotStateData represents the spot state data
type SpotStateData struct {
	Balances []SpotBalanceData `json:"balances"`
}

// SpotBalanceData represents a single spot balance entry
type SpotBalanceData struct {
	Coin     string `json:"coin"`
	Token    int    `json:"token"`
	Total    string `json:"total"`
	Hold     string `json:"hold"`
	EntryNtl string `json:"entryNtl"`
}

// ConvertToDecimal converts string values to decimal.Decimal for margin summary
func (m *MarginSummaryData) ConvertToDecimal() (accountValue, totalNtlPos, totalRawUsd, totalMarginUsed decimal.Decimal, err error) {
	accountValue, err = decimal.NewFromString(m.AccountValue)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, err
	}

	totalNtlPos, err = decimal.NewFromString(m.TotalNtlPos)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, err
	}

	totalRawUsd, err = decimal.NewFromString(m.TotalRawUsd)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, err
	}

	totalMarginUsed, err = decimal.NewFromString(m.TotalMarginUsed)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, err
	}

	return accountValue, totalNtlPos, totalRawUsd, totalMarginUsed, nil
}

// ConvertToDecimal converts string values to decimal.Decimal for clearinghouse state
func (c *ClearinghouseStateData) ConvertToDecimal() (crossMaintenanceMarginUsed, withdrawable decimal.Decimal, err error) {
	crossMaintenanceMarginUsed, err = decimal.NewFromString(c.CrossMaintenanceMarginUsed)
	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	withdrawable, err = decimal.NewFromString(c.Withdrawable)
	if err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	return crossMaintenanceMarginUsed, withdrawable, nil
}
