package node

import (
	"encoding/json"
)

type EvmContract struct {
	Address *string     `json:"address,omitempty"`
	Value   *string     `json:"value,omitempty"`
	Raw     interface{} `json:"-"` 
}

func (e *EvmContract) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		e.Value = &str
		e.Raw = str
		return nil
	}

	var obj map[string]interface{}
	if err := json.Unmarshal(data, &obj); err == nil {
		e.Raw = obj
		if addr, ok := obj["address"].(string); ok {
			e.Address = &addr
		}
		if val, ok := obj["value"].(string); ok {
			e.Value = &val
		}
		if e.Address == nil && e.Value == nil {
			for _, v := range obj {
				if strVal, ok := v.(string); ok && strVal != "" {
					s := strVal
					e.Value = &s
					break
				}
			}
		}
		return nil
	}

	return nil
}

func (e EvmContract) MarshalJSON() ([]byte, error) {
	if e.Raw != nil {
		return json.Marshal(e.Raw)
	}
	if e.Value != nil {
		return json.Marshal(*e.Value)
	}
	if e.Address != nil {
		return json.Marshal(*e.Address)
	}
	return json.Marshal(nil)
}

func (e EvmContract) String() string {
	if e.Value != nil {
		return *e.Value
	}
	if e.Address != nil {
		return *e.Address
	}
	return ""
}

func (e EvmContract) IsEmpty() bool {
	return e.Address == nil && e.Value == nil && e.Raw == nil
}

type SpotMeta struct {
	Tokens   []Token    `json:"tokens"`
	Universe []Universe `json:"universe"`
}

type Token struct {
	Name        string      `json:"name"`
	SzDecimals  int         `json:"szDecimals"`
	WeiDecimals int         `json:"weiDecimals"`
	Index       int         `json:"index"`
	TokenID     string      `json:"tokenId"`
	IsCanonical bool        `json:"isCanonical"`
	EvmContract EvmContract `json:"evmContract"`
	FullName    *string     `json:"fullName"`
}

type Universe struct {
	Name        string `json:"name"`
	Tokens      []int  `json:"tokens"`
	Index       int    `json:"index"`
	IsCanonical bool   `json:"isCanonical"`
}
