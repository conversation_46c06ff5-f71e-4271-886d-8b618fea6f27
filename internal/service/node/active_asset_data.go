package node

type ActiveAssetData struct {
	User             string        `json:"user"`
	Coin             string        `json:"coin"`
	Leverage         AssetLeverage `json:"leverage"`
	MaxTradeSzs      []string      `json:"maxTradeSzs"`
	AvailableToTrade []string      `json:"availableToTrade"`
	MarkPx           string        `json:"markPx"`
}

// Leverage 表示杠杆信息
type AssetLeverage struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}
