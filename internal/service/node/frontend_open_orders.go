package node

type FrontendOpenOrder struct {
	Coin             string `json:"coin"`            
	IsPositionTpsl   bool   `json:"isPositionTpsl"`   
	IsTrigger        bool   `json:"isTrigger"`        
	LimitPx          string `json:"limitPx"`        
	Oid              int64  `json:"oid"`             
	OrderType        string `json:"orderType"`     
	OrigSz           string `json:"origSz"`           
	ReduceOnly       bool   `json:"reduceOnly"`      
	Side             string `json:"side"`            
	Sz               string `json:"sz"`               
	Timestamp        int64  `json:"timestamp"`        
	TriggerCondition string `json:"triggerCondition"` 
	TriggerPx        string `json:"triggerPx"`       
}

type FrontendOpenOrdersResponse []FrontendOpenOrder