package service

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// HyperliquidCandleRequest represents the request structure for Hyperliquid API
type HyperliquidCandleRequest struct {
	Type string                      `json:"type"`
	Req  HyperliquidCandleRequestReq `json:"req"`
}

type HyperliquidCandleRequestReq struct {
	Coin      string `json:"coin"`
	Interval  string `json:"interval"`
	StartTime uint64 `json:"startTime"`
	EndTime   uint64 `json:"endTime"`
}

// HyperliquidCandleResponse represents the response structure from Hyperliquid API
type HyperliquidCandleResponse struct {
	Coin      string `json:"s"` // Symbol/coin
	Interval  string `json:"i"` // Interval
	TimeOpen  uint64 `json:"t"` // Open time timestamp in milliseconds
	TimeClose uint64 `json:"T"` // Close time timestamp in milliseconds
	Open      string `json:"o"` // Open price as string
	High      string `json:"h"` // High price as string
	Low       string `json:"l"` // Low price as string
	Close     string `json:"c"` // Close price as string
	Volume    string `json:"v"` // Volume traded as string
	NumTrades uint64 `json:"n"` // Number of trades as integer
}

// HyperliquidAllMidsResponse represents the allMids response for getting available symbols
// The response is a map where keys are symbol names and values are mid prices
type HyperliquidAllMidsResponse map[string]string

// OHLCVBackfillService handles OHLCV data backfill from Hyperliquid API
type OHLCVBackfillService struct {
	clickhouse                  *sql.DB
	httpClient                  *http.Client
	apiURL                      string
	maxRetries                  int
	retryDelay                  time.Duration
	rateLimitDelay              time.Duration // Delay between API requests for rate limiting
	interSymbolDelay            time.Duration // Delay between processing different symbols
	baseTimeout                 time.Duration // Base timeout for HTTP requests
	progressiveTimeoutIncrement time.Duration // Timeout increment per retry
	maxTimeout                  time.Duration // Maximum timeout for HTTP requests
	tlsHandshakeTimeout         time.Duration // TLS handshake timeout
	responseHeaderTimeout       time.Duration // Response header timeout
	idleConnTimeout             time.Duration // Idle connection timeout
	databaseTimeout             time.Duration // Timeout for database operations
	cachedSymbols               []string
	symbolsCacheTime            time.Time
	symbolsCacheTTL             time.Duration
	symbolsMutex                sync.RWMutex
	proxyManager                *ProxyManager // Proxy manager for rate limit avoidance
	jobID                       string        // Job ID for proxy assignment

	// HTTP client pool for reuse to prevent accumulation
	clientPool      map[string]*http.Client // Key: proxy URL or "direct"
	clientPoolMutex sync.RWMutex
}

// BackfillConfig represents configuration for backfill operations
type BackfillConfig struct {
	CandleCount int           `json:"candle_count"`
	Timeout     time.Duration `json:"timeout"`
}

// parseDurationWithDefault parses a duration string with a fallback default
func parseDurationWithDefault(durationStr string, defaultDuration time.Duration) time.Duration {
	if durationStr == "" {
		return defaultDuration
	}

	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		// Only log if logger is available (avoid nil pointer in tests)
		if global.GVA_LOG != nil {
			global.GVA_LOG.Warn("Failed to parse duration, using default",
				zap.String("duration_str", durationStr),
				zap.Duration("default", defaultDuration),
				zap.Error(err))
		}
		return defaultDuration
	}

	return duration
}

// NewOHLCVBackfillService creates a new OHLCV backfill service
func NewOHLCVBackfillService() *OHLCVBackfillService {
	return NewOHLCVBackfillServiceWithJobID("")
}

// NewOHLCVBackfillServiceWithJobID creates a new OHLCV backfill service with job ID for proxy assignment
func NewOHLCVBackfillServiceWithJobID(jobID string) *OHLCVBackfillService {
	timing := global.GVA_CONFIG.Backfill.Timing

	// Use shared proxy manager instance to prevent goroutine leaks
	var proxyManager *ProxyManager
	if global.GVA_CONFIG.Backfill.Proxies.Enabled {
		proxyManager = GetSharedProxyManager()
	}

	return &OHLCVBackfillService{
		clickhouse:                  global.GVA_DB_CLICKHOUSE,
		apiURL:                      "https://api.hyperliquid.xyz/info",
		maxRetries:                  5,
		retryDelay:                  parseDurationWithDefault(timing.RetryDelay, 3*time.Second),
		rateLimitDelay:              parseDurationWithDefault(timing.RateLimitDelay, 2*time.Second),
		interSymbolDelay:            parseDurationWithDefault(timing.InterSymbolDelay, 50*time.Millisecond),
		baseTimeout:                 parseDurationWithDefault(timing.BaseTimeout, 120*time.Second),
		progressiveTimeoutIncrement: parseDurationWithDefault(timing.ProgressiveTimeoutIncrement, 60*time.Second),
		maxTimeout:                  parseDurationWithDefault(timing.MaxTimeout, 300*time.Second),
		tlsHandshakeTimeout:         parseDurationWithDefault(timing.TLSHandshakeTimeout, 30*time.Second),
		responseHeaderTimeout:       parseDurationWithDefault(timing.ResponseHeaderTimeout, 60*time.Second),
		idleConnTimeout:             parseDurationWithDefault(timing.IdleConnTimeout, 120*time.Second),
		databaseTimeout:             parseDurationWithDefault(timing.DatabaseTimeout, 2*time.Minute),
		symbolsCacheTTL:             parseDurationWithDefault(timing.SymbolsCacheTTL, 5*time.Minute),
		proxyManager:                proxyManager,
		jobID:                       jobID,
		clientPool:                  make(map[string]*http.Client),
	}
}

// createHTTPClient creates an HTTP client with progressive timeout based on retry attempt
func (s *OHLCVBackfillService) createHTTPClient(retryAttempt int) *http.Client {
	return s.createHTTPClientWithProxy(retryAttempt, nil)
}

// createHTTPClientWithProxy creates or reuses an HTTP client with optional proxy support
func (s *OHLCVBackfillService) createHTTPClientWithProxy(retryAttempt int, proxyInfo *ProxyInfo) *http.Client {
	// Determine client key for pooling
	clientKey := "direct"
	if proxyInfo != nil {
		clientKey = proxyInfo.URL
	}

	// Check if we already have a client for this proxy/direct connection
	s.clientPoolMutex.RLock()
	if client, exists := s.clientPool[clientKey]; exists {
		s.clientPoolMutex.RUnlock()
		return client
	}
	s.clientPoolMutex.RUnlock()

	// Create new client if not in pool
	s.clientPoolMutex.Lock()
	defer s.clientPoolMutex.Unlock()

	// Double-check after acquiring write lock
	if client, exists := s.clientPool[clientKey]; exists {
		return client
	}

	// Progressive timeout: base + (retry * progressiveTimeoutIncrement), max maxTimeout
	timeout := s.baseTimeout + time.Duration(retryAttempt)*s.progressiveTimeoutIncrement
	if timeout > s.maxTimeout {
		timeout = s.maxTimeout
	}

	transport := &http.Transport{
		TLSHandshakeTimeout:   s.tlsHandshakeTimeout,   // Configurable TLS handshake timeout
		ResponseHeaderTimeout: s.responseHeaderTimeout, // Configurable header timeout
		IdleConnTimeout:       s.idleConnTimeout,       // Configurable idle connection timeout
		MaxIdleConns:          50,                      // Reduced idle connections for stability
		MaxIdleConnsPerHost:   5,                       // Reduced per-host connections for stability
		DisableKeepAlives:     false,                   // Keep connections alive for efficiency
		DisableCompression:    false,                   // Allow compression
	}

	// Configure proxy if provided
	if proxyInfo != nil {
		proxyURL, err := url.Parse(proxyInfo.URL)
		if err != nil {
			global.GVA_LOG.Error("Failed to parse proxy URL, using direct connection",
				zap.String("proxy_url", proxyInfo.URL),
				zap.Error(err))
		} else {
			transport.Proxy = http.ProxyURL(proxyURL)
			global.GVA_LOG.Debug("HTTP client configured with proxy",
				zap.String("proxy_url", proxyInfo.URL))
		}
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	// Store in pool for reuse
	s.clientPool[clientKey] = client

	global.GVA_LOG.Debug("Created new HTTP client",
		zap.String("client_key", clientKey),
		zap.Duration("timeout", timeout))

	return client
}

// GetSymbols retrieves symbols with caching
func (s *OHLCVBackfillService) GetSymbols(ctx context.Context) ([]string, error) {
	s.symbolsMutex.RLock()
	if len(s.cachedSymbols) > 0 && time.Since(s.symbolsCacheTime) < s.symbolsCacheTTL {
		symbols := make([]string, len(s.cachedSymbols))
		copy(symbols, s.cachedSymbols)
		s.symbolsMutex.RUnlock()

		global.GVA_LOG.Debug("Using cached symbols",
			zap.Int("symbol_count", len(symbols)),
			zap.Duration("cache_age", time.Since(s.symbolsCacheTime)))

		return symbols, nil
	}
	s.symbolsMutex.RUnlock()

	// Cache miss or expired, fetch fresh symbols
	return s.FetchSymbols(ctx)
}

// FetchSymbols retrieves all available symbols from Hyperliquid API
func (s *OHLCVBackfillService) FetchSymbols(ctx context.Context) ([]string, error) {
	// Add rate limiting delay
	time.Sleep(s.rateLimitDelay)

	client := s.createHTTPClient(0) // Use base timeout for symbol fetching

	requestBody := map[string]interface{}{
		"type": "allMids",
		"dex":  "",
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var allMidsResponse HyperliquidAllMidsResponse
	if err := json.Unmarshal(body, &allMidsResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	symbols := make([]string, 0, len(allMidsResponse))
	for symbol := range allMidsResponse {
		symbols = append(symbols, symbol)
	}

	// Update cache
	s.symbolsMutex.Lock()
	s.cachedSymbols = make([]string, len(symbols))
	copy(s.cachedSymbols, symbols)
	s.symbolsCacheTime = time.Now()
	s.symbolsMutex.Unlock()

	global.GVA_LOG.Info("Fetched symbols from Hyperliquid API",
		zap.Int("symbol_count", len(symbols)))

	return symbols, nil
}

// FetchCandles retrieves OHLCV candles for a specific symbol and timeframe
func (s *OHLCVBackfillService) FetchCandles(ctx context.Context, symbol string, interval string, startTime, endTime uint64) ([]HyperliquidCandleResponse, error) {
	var lastErr error

	for retry := 0; retry < s.maxRetries; retry++ {
		client := s.createHTTPClient(retry) // Progressive timeout based on retry

		candles, err := s.fetchCandlesWithClient(ctx, client, symbol, interval, startTime, endTime)
		if err == nil {
			return candles, nil
		}

		lastErr = err

		// Check if it's a rate limit error (429)
		if isRateLimitError(err) {
			backoffDuration := time.Duration(math.Pow(2, float64(retry+1))) * s.retryDelay
			global.GVA_LOG.Warn("Rate limit hit, backing off",
				zap.String("symbol", symbol),
				zap.String("interval", interval),
				zap.Int("retry", retry+1),
				zap.Duration("backoff", backoffDuration))

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoffDuration):
				continue
			}
		}

		// For other errors, retry with exponential backoff
		retryDelay := time.Duration(math.Pow(2, float64(retry))) * s.retryDelay
		global.GVA_LOG.Warn("Request failed, retrying with backoff",
			zap.String("symbol", symbol),
			zap.String("interval", interval),
			zap.Int("retry", retry+1),
			zap.Duration("retry_delay", retryDelay),
			zap.Error(err))

		// Wait before retrying (unless it's the last retry)
		if retry < s.maxRetries-1 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryDelay):
				// Continue to next retry
			}
		}
	}

	return nil, fmt.Errorf("max retries exceeded, last error: %w", lastErr)
}

// FetchCandlesWithProxy retrieves OHLCV candles using proxy rotation with fallback to direct connection
func (s *OHLCVBackfillService) FetchCandlesWithProxy(ctx context.Context, symbol string, interval string, startTime, endTime uint64) ([]HyperliquidCandleResponse, error) {
	// If proxy manager is not available or disabled, fall back to regular fetch
	if s.proxyManager == nil || !global.GVA_CONFIG.Backfill.Proxies.Enabled {
		return s.FetchCandles(ctx, symbol, interval, startTime, endTime)
	}

	var lastErr error
	var proxyInfo *ProxyInfo

	// Try to get a proxy for this job
	if s.jobID != "" {
		proxyInfo, lastErr = s.proxyManager.GetProxyForJob(s.jobID)
	} else {
		proxyInfo, lastErr = s.proxyManager.GetNextProxy()
	}

	// If no proxy available, fall back to direct connection
	if lastErr != nil || proxyInfo == nil {
		global.GVA_LOG.Warn("No proxy available, falling back to direct connection",
			zap.String("symbol", symbol),
			zap.Error(lastErr))
		return s.FetchCandles(ctx, symbol, interval, startTime, endTime)
	}

	// Try with proxy first
	for retry := 0; retry < s.maxRetries; retry++ {
		client := s.createHTTPClientWithProxy(retry, proxyInfo)

		candles, err := s.fetchCandlesWithClient(ctx, client, symbol, interval, startTime, endTime)
		if err == nil {
			// Mark proxy as successful
			s.proxyManager.MarkProxySuccess(proxyInfo.URL)
			return candles, nil
		}

		lastErr = err

		// Check if it's a rate limit error (429)
		if isRateLimitError(err) {
			// Mark proxy as failed for rate limiting
			s.proxyManager.MarkProxyFailed(proxyInfo.URL, err)

			// Try to get a different proxy
			newProxyInfo, proxyErr := s.proxyManager.GetNextProxy()
			if proxyErr == nil && newProxyInfo != nil && newProxyInfo.URL != proxyInfo.URL {
				proxyInfo = newProxyInfo
				global.GVA_LOG.Info("Switching to different proxy due to rate limit",
					zap.String("symbol", symbol),
					zap.String("new_proxy", proxyInfo.URL))
				continue
			}

			// If no other proxy available, apply backoff and continue with same proxy
			backoffDuration := time.Duration(math.Pow(2, float64(retry+1))) * s.retryDelay
			global.GVA_LOG.Warn("Rate limit hit with proxy, backing off",
				zap.String("symbol", symbol),
				zap.String("proxy_url", proxyInfo.URL),
				zap.Int("retry", retry+1),
				zap.Duration("backoff", backoffDuration))

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoffDuration):
				continue
			}
		}

		// For other errors, mark proxy as failed and retry
		s.proxyManager.MarkProxyFailed(proxyInfo.URL, err)

		global.GVA_LOG.Warn("Proxy request failed, retrying",
			zap.String("symbol", symbol),
			zap.String("proxy_url", proxyInfo.URL),
			zap.Int("retry", retry+1),
			zap.Error(err))

		// Apply retry delay
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(s.retryDelay):
			continue
		}
	}

	// If all proxy attempts failed, fall back to direct connection
	global.GVA_LOG.Warn("All proxy attempts failed, falling back to direct connection",
		zap.String("symbol", symbol),
		zap.String("proxy_url", proxyInfo.URL),
		zap.Error(lastErr))

	return s.FetchCandles(ctx, symbol, interval, startTime, endTime)
}

// fetchCandlesWithClient performs the actual HTTP request to fetch candles
func (s *OHLCVBackfillService) fetchCandlesWithClient(ctx context.Context, client *http.Client, symbol string, interval string, startTime, endTime uint64) ([]HyperliquidCandleResponse, error) {
	// Add rate limiting delay for individual API calls (full delay for retries too)
	time.Sleep(s.rateLimitDelay)

	requestBody := HyperliquidCandleRequest{
		Type: "candleSnapshot",
		Req: HyperliquidCandleRequestReq{
			Coin:      symbol,
			Interval:  interval,
			StartTime: startTime,
			EndTime:   endTime,
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusTooManyRequests {
		return nil, &RateLimitError{StatusCode: resp.StatusCode}
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var candles []HyperliquidCandleResponse
	if err := json.Unmarshal(body, &candles); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	global.GVA_LOG.Debug("Fetched candles from Hyperliquid API",
		zap.String("symbol", symbol),
		zap.String("interval", interval),
		zap.Int("candle_count", len(candles)))

	return candles, nil
}

// convertToKlines converts Hyperliquid API response to internal Kline model
func (s *OHLCVBackfillService) convertToKlines(candles []HyperliquidCandleResponse) ([]*model.Kline, error) {
	klines := make([]*model.Kline, len(candles))

	for i, candle := range candles {
		// Parse price values
		open, err := strconv.ParseFloat(candle.Open, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse open price: %w", err)
		}

		high, err := strconv.ParseFloat(candle.High, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse high price: %w", err)
		}

		low, err := strconv.ParseFloat(candle.Low, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse low price: %w", err)
		}

		close, err := strconv.ParseFloat(candle.Close, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse close price: %w", err)
		}

		volume, err := strconv.ParseFloat(candle.Volume, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse volume: %w", err)
		}

		// Convert timestamp from milliseconds to time.Time
		timestamp := time.Unix(0, int64(candle.TimeOpen)*int64(time.Millisecond))

		klines[i] = &model.Kline{
			Timestamp:   timestamp,
			Symbol:      candle.Coin,
			Open:        open,
			High:        high,
			Low:         low,
			Close:       close,
			Volume:      volume,
			TradesCount: int64(candle.NumTrades),
		}
	}

	return klines, nil
}

// BatchInsertKlines performs batch insertion of klines to ClickHouse
func (s *OHLCVBackfillService) BatchInsertKlines(ctx context.Context, klines []*model.Kline, timeframe model.KlineTimeframe) error {
	if len(klines) == 0 {
		return nil
	}

	tableName := timeframe.GetTableName()

	// Create a separate context with configurable timeout for database operations
	dbCtx, cancel := context.WithTimeout(context.Background(), s.databaseTimeout)
	defer cancel()

	// Use UPSERT logic with ON DUPLICATE KEY UPDATE equivalent for ClickHouse
	// ClickHouse uses ReplacingMergeTree or we can use INSERT with deduplication
	query := fmt.Sprintf(`
		INSERT INTO %s (timestamp, symbol, open, high, low, close, volume, trades_count)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, tableName)

	// Begin transaction for atomic batch insert with extended timeout
	tx, err := s.clickhouse.BeginTx(dbCtx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Prepare statement
	stmt, err := tx.PrepareContext(dbCtx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	// Execute batch insert
	for _, kline := range klines {
		_, err := stmt.ExecContext(dbCtx,
			kline.Timestamp,
			kline.Symbol,
			kline.Open,
			kline.High,
			kline.Low,
			kline.Close,
			kline.Volume,
			kline.TradesCount,
		)
		if err != nil {
			return fmt.Errorf("failed to execute batch insert: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit batch insert transaction: %w", err)
	}

	global.GVA_LOG.Info("Successfully inserted klines to ClickHouse",
		zap.String("table", tableName),
		zap.Int("kline_count", len(klines)))

	return nil
}

// BackfillError represents different types of backfill errors
type BackfillError struct {
	Type    string
	Message string
	Cause   error
}

func (e *BackfillError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

func (e *BackfillError) Unwrap() error {
	return e.Cause
}

// Error types
const (
	ErrorTypeRateLimit   = "RATE_LIMIT"
	ErrorTypeNetwork     = "NETWORK"
	ErrorTypeAPI         = "API"
	ErrorTypeDatabase    = "DATABASE"
	ErrorTypeValidation  = "VALIDATION"
	ErrorTypeTimeout     = "TIMEOUT"
	ErrorTypeSymbolFetch = "SYMBOL_FETCH"
	ErrorTypeCandleFetch = "CANDLE_FETCH"
	ErrorTypeDataConvert = "DATA_CONVERT"
)

// RateLimitError represents a rate limit error from the API
type RateLimitError struct {
	StatusCode int
}

func (e *RateLimitError) Error() string {
	return fmt.Sprintf("rate limit error: status code %d", e.StatusCode)
}

// isRateLimitError checks if an error is a rate limit error
func isRateLimitError(err error) bool {
	_, ok := err.(*RateLimitError)
	return ok
}

// NewBackfillError creates a new backfill error
func NewBackfillError(errorType, message string, cause error) *BackfillError {
	return &BackfillError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// BackfillTimeframe performs backfill for a specific timeframe
func (s *OHLCVBackfillService) BackfillTimeframe(ctx context.Context, timeframe model.KlineTimeframe, config BackfillConfig) error {
	global.GVA_LOG.Info("Starting backfill for timeframe",
		zap.String("timeframe", string(timeframe)),
		zap.Int("candle_count", config.CandleCount))

	// Fetch all available symbols (with caching)
	symbols, err := s.GetSymbols(ctx)
	if err != nil {
		return fmt.Errorf("failed to fetch symbols: %w", err)
	}

	// Calculate time range for fetching candles
	// Use current time minus one timeframe unit to avoid fetching incomplete candles
	// The most recent candle is handled by the persist kline job
	now := time.Now()
	timeframeMinutes := timeframe.GetMinutes()
	timeframeDuration := time.Duration(timeframeMinutes) * time.Minute
	endTime := uint64(now.Add(-timeframeDuration).UnixMilli())

	// Calculate start time based on timeframe and candle count
	startTime := endTime - uint64(config.CandleCount*timeframeMinutes*60*1000) // Convert to milliseconds

	global.GVA_LOG.Info("Calculated time range for backfill",
		zap.String("timeframe", string(timeframe)),
		zap.Time("start_time", time.Unix(0, int64(startTime)*int64(time.Millisecond))),
		zap.Time("end_time", time.Unix(0, int64(endTime)*int64(time.Millisecond))),
		zap.Int("symbol_count", len(symbols)))

	// Determine processing strategy based on timeframe
	// Enable concurrent processing only for high-frequency timeframes (1m, 3m)
	// Use sequential processing for all other timeframes
	isHighFrequency := timeframe == model.Timeframe1m || timeframe == model.Timeframe3m

	var totalKlines int

	if isHighFrequency {
		// Concurrent processing for high-frequency timeframes
		totalKlines = s.processConcurrently(ctx, symbols, timeframe, startTime, endTime)
		// Note: processConcurrently logs errors internally and returns 0 on failure
		// We continue with the result (0 or actual count) for consistency
	} else {
		// Sequential processing for low-frequency timeframes
		var err error
		totalKlines, err = s.processSequentially(ctx, symbols, timeframe, startTime, endTime)
		if err != nil {
			return err
		}
	}

	global.GVA_LOG.Info("Backfill completed successfully",
		zap.String("timeframe", string(timeframe)),
		zap.Int("total_klines", totalKlines),
		zap.Int("symbol_count", len(symbols)))

	return nil
}

// processSymbolBackfill processes backfill for a single symbol
func (s *OHLCVBackfillService) processSymbolBackfill(ctx context.Context, symbol, interval string, startTime, endTime uint64, timeframe model.KlineTimeframe) (int, error) {
	// Note: Rate limiting delay is applied in fetchCandlesWithClient, not here
	// This avoids double-applying the rate limit delay

	// Fetch candles from Hyperliquid API
	candles, err := s.FetchCandles(ctx, symbol, interval, startTime, endTime)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch candles: %w", err)
	}

	if len(candles) == 0 {
		global.GVA_LOG.Debug("No candles found for symbol",
			zap.String("symbol", symbol),
			zap.String("interval", interval))
		return 0, nil
	}

	// Convert to internal kline format
	klines, err := s.convertToKlines(candles)
	if err != nil {
		return 0, fmt.Errorf("failed to convert candles: %w", err)
	}

	// Batch insert to ClickHouse
	if err := s.BatchInsertKlines(ctx, klines, timeframe); err != nil {
		return 0, fmt.Errorf("failed to insert klines: %w", err)
	}

	global.GVA_LOG.Debug("Successfully processed symbol backfill",
		zap.String("symbol", symbol),
		zap.String("interval", interval),
		zap.Int("kline_count", len(klines)))

	return len(klines), nil
}

// processSymbolBackfillWithProxy processes backfill for a single symbol using proxy rotation
func (s *OHLCVBackfillService) processSymbolBackfillWithProxy(ctx context.Context, symbol, interval string, startTime, endTime uint64, timeframe model.KlineTimeframe) (int, error) {
	// Fetch candles from Hyperliquid API with proxy support
	candles, err := s.FetchCandlesWithProxy(ctx, symbol, interval, startTime, endTime)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch candles: %w", err)
	}

	if len(candles) == 0 {
		global.GVA_LOG.Debug("No candles found for symbol",
			zap.String("symbol", symbol),
			zap.String("interval", interval))
		return 0, nil
	}

	// Convert to internal kline format
	klines, err := s.convertToKlines(candles)
	if err != nil {
		return 0, fmt.Errorf("failed to convert candles: %w", err)
	}

	// Batch insert to ClickHouse
	if err := s.BatchInsertKlines(ctx, klines, timeframe); err != nil {
		return 0, fmt.Errorf("failed to insert klines: %w", err)
	}

	global.GVA_LOG.Debug("Successfully processed symbol backfill with proxy",
		zap.String("symbol", symbol),
		zap.String("interval", interval),
		zap.Int("kline_count", len(klines)))

	return len(klines), nil
}

// processConcurrently processes symbols concurrently for high-frequency timeframes (1m, 3m)
func (s *OHLCVBackfillService) processConcurrently(ctx context.Context, symbols []string, timeframe model.KlineTimeframe, startTime, endTime uint64) int {
	var totalKlines int
	var totalKlinesMutex sync.Mutex

	// Determine concurrency level based on proxy configuration
	concurrency := 1 // Default to sequential processing
	if s.proxyManager != nil && global.GVA_CONFIG.Backfill.Proxies.Enabled {
		healthyProxyCount := s.proxyManager.GetHealthyProxyCount()
		if healthyProxyCount > 0 {
			concurrency = healthyProxyCount
		}
		// Use configured concurrency if available
		if global.GVA_CONFIG.Backfill.Proxies.Concurrency > 0 {
			concurrency = global.GVA_CONFIG.Backfill.Proxies.Concurrency
		}
	}

	global.GVA_LOG.Info("Starting concurrent symbol processing",
		zap.Int("concurrency", concurrency),
		zap.Int("symbol_count", len(symbols)),
		zap.String("timeframe", string(timeframe)))

	// Create error group with concurrency limit (semaphore pattern)
	eg, egCtx := errgroup.WithContext(ctx)
	eg.SetLimit(concurrency)

	// Process symbols concurrently
	for i, symbol := range symbols {
		symbol := symbol // Capture loop variable
		symbolIndex := i

		eg.Go(func() error {
			global.GVA_LOG.Debug("Processing symbol concurrently",
				zap.String("symbol", symbol),
				zap.Int("progress", symbolIndex+1),
				zap.Int("total", len(symbols)),
				zap.String("timeframe", string(timeframe)))

			klineCount, err := s.processSymbolBackfillWithProxy(egCtx, symbol, string(timeframe), startTime, endTime, timeframe)

			if err != nil {
				global.GVA_LOG.Error("Failed to process symbol",
					zap.String("symbol", symbol),
					zap.Error(err))
				return fmt.Errorf("symbol %s: %w", symbol, err)
			}

			// Thread-safe update of total klines
			totalKlinesMutex.Lock()
			totalKlines += klineCount
			totalKlinesMutex.Unlock()

			global.GVA_LOG.Debug("Successfully processed symbol concurrently",
				zap.String("symbol", symbol),
				zap.Int("kline_count", klineCount))

			return nil
		})
	}

	// Wait for all goroutines to complete
	if err := eg.Wait(); err != nil {
		global.GVA_LOG.Error("Concurrent processing failed",
			zap.String("timeframe", string(timeframe)),
			zap.Error(err))
		// Return 0 on error, but don't panic - let the caller handle it
		return 0
	}

	global.GVA_LOG.Info("Concurrent processing completed successfully",
		zap.String("timeframe", string(timeframe)),
		zap.Int("total_klines", totalKlines),
		zap.Int("symbol_count", len(symbols)))

	return totalKlines
}

// processSequentially processes symbols sequentially for low-frequency timeframes
func (s *OHLCVBackfillService) processSequentially(ctx context.Context, symbols []string, timeframe model.KlineTimeframe, startTime, endTime uint64) (int, error) {
	var totalKlines int
	var errors []error

	global.GVA_LOG.Info("Starting sequential symbol processing",
		zap.Int("symbol_count", len(symbols)),
		zap.String("timeframe", string(timeframe)))

	for i, symbol := range symbols {
		global.GVA_LOG.Debug("Processing symbol sequentially",
			zap.String("symbol", symbol),
			zap.Int("progress", i+1),
			zap.Int("total", len(symbols)),
			zap.String("timeframe", string(timeframe)))

		klineCount, err := s.processSymbolBackfillWithProxy(ctx, symbol, string(timeframe), startTime, endTime, timeframe)

		if err != nil {
			errors = append(errors, fmt.Errorf("symbol %s: %w", symbol, err))
			global.GVA_LOG.Error("Failed to process symbol",
				zap.String("symbol", symbol),
				zap.Error(err))
		} else {
			totalKlines += klineCount
			global.GVA_LOG.Debug("Successfully processed symbol sequentially",
				zap.String("symbol", symbol),
				zap.Int("kline_count", klineCount))
		}

		// Add configurable delay between symbols to spread out processing
		// This provides spacing between starting to process different symbols
		// Note: API rate limiting is handled separately in fetchCandlesWithClient
		if i < len(symbols)-1 { // Don't sleep after the last symbol
			time.Sleep(s.interSymbolDelay)
		}
	}

	if len(errors) > 0 {
		global.GVA_LOG.Error("Sequential processing completed with errors",
			zap.String("timeframe", string(timeframe)),
			zap.Int("error_count", len(errors)),
			zap.Int("total_klines", totalKlines))

		// Log first few errors for debugging
		for i, err := range errors {
			if i >= 5 { // Limit to first 5 errors
				break
			}
			global.GVA_LOG.Error("Sequential processing error", zap.Error(err))
		}

		return totalKlines, fmt.Errorf("sequential processing completed with %d errors", len(errors))
	}

	return totalKlines, nil
}

// ClearSymbolsCache clears the cached symbols
func (s *OHLCVBackfillService) ClearSymbolsCache() {
	s.symbolsMutex.Lock()
	defer s.symbolsMutex.Unlock()

	s.cachedSymbols = nil
	s.symbolsCacheTime = time.Time{}

	global.GVA_LOG.Info("Symbols cache cleared")
}

// GetSymbolsCacheInfo returns information about the symbols cache
func (s *OHLCVBackfillService) GetSymbolsCacheInfo() map[string]interface{} {
	s.symbolsMutex.RLock()
	defer s.symbolsMutex.RUnlock()

	return map[string]interface{}{
		"cached_symbols_count": len(s.cachedSymbols),
		"cache_time":           s.symbolsCacheTime,
		"cache_ttl":            s.symbolsCacheTTL,
		"cache_age":            time.Since(s.symbolsCacheTime),
		"is_cache_valid":       len(s.cachedSymbols) > 0 && time.Since(s.symbolsCacheTime) < s.symbolsCacheTTL,
	}
}

// RefreshSymbols forces a refresh of the symbols cache
func (s *OHLCVBackfillService) RefreshSymbols(ctx context.Context) ([]string, error) {
	global.GVA_LOG.Info("Forcing symbols cache refresh")
	return s.FetchSymbols(ctx)
}

// CleanupHTTPClients closes all HTTP clients in the pool to prevent resource leaks
func (s *OHLCVBackfillService) CleanupHTTPClients() {
	s.clientPoolMutex.Lock()
	defer s.clientPoolMutex.Unlock()

	for key, client := range s.clientPool {
		if client.Transport != nil {
			if transport, ok := client.Transport.(*http.Transport); ok {
				transport.CloseIdleConnections()
			}
		}
		delete(s.clientPool, key)
	}

	global.GVA_LOG.Info("HTTP client pool cleaned up",
		zap.Int("clients_cleaned", len(s.clientPool)))
}
