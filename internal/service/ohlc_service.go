package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// OHLCRepositoryInterface defines the contract for OHLC data access
type OHLCRepositoryInterface interface {
	GetHistoricalKlines(ctx context.Context, req *OHLCQueryRequest) ([]*model.Kline, error)
	GetActiveKline(ctx context.Context, symbol string, timeframe model.KlineTimeframe) (*model.Kline, error)
	GetLastCompletedKline(ctx context.Context, symbol string, timeframe model.KlineTimeframe, beforeTimestamp time.Time) (*model.Kline, error)
}

// OHLCQueryRequest represents a query request for OHLC data
type OHLCQueryRequest struct {
	Symbol    string               `json:"symbol"`
	Timeframe model.KlineTimeframe `json:"timeframe"`
	Timestamp time.Time            `json:"timestamp"`
	IsF<PERSON><PERSON> bool                 `json:"is_forward"`
	Limit     int                  `json:"limit"`
}

// OHLCServiceInterface defines the contract for OHLC business logic
type OHLCServiceInterface interface {
	GetOHLC(ctx context.Context, req *OHLCRequest) (*OHLCResponse, error)
}

// OHLCRequest represents a request for OHLC data
type OHLCRequest struct {
	Symbol    string           `json:"symbol"`
	Interval  OHLCIntervalEnum `json:"interval"`
	Timestamp int64            `json:"timestamp"` // Unix timestamp in milliseconds
	IsForward bool             `json:"is_forward"`
	Limit     int              `json:"limit"`
}

// OHLCResponse represents the response containing OHLC data
type OHLCResponse struct {
	Symbol string      `json:"symbol"`
	Data   []*OHLCData `json:"data"`
}

// OHLCData represents a single OHLC candle
type OHLCData struct {
	Timestamp int64   `json:"timestamp"` // Unix timestamp in milliseconds
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
}

// OHLCIntervalEnum represents the supported timeframe intervals
type OHLCIntervalEnum string

const (
	OHLCIntervalOneMinute      OHLCIntervalEnum = "ONE_MINUTE"
	OHLCIntervalThreeMinutes   OHLCIntervalEnum = "THREE_MINUTES"
	OHLCIntervalFiveMinutes    OHLCIntervalEnum = "FIVE_MINUTES"
	OHLCIntervalFifteenMinutes OHLCIntervalEnum = "FIFTEEN_MINUTES"
	OHLCIntervalThirtyMinutes  OHLCIntervalEnum = "THIRTY_MINUTES"
	OHLCIntervalOneHour        OHLCIntervalEnum = "ONE_HOUR"
	OHLCIntervalTwoHours       OHLCIntervalEnum = "TWO_HOURS"
	OHLCIntervalFourHours      OHLCIntervalEnum = "FOUR_HOURS"
	OHLCIntervalEightHours     OHLCIntervalEnum = "EIGHT_HOURS"
	OHLCIntervalTwelveHours    OHLCIntervalEnum = "TWELVE_HOURS"
	OHLCIntervalOneDay         OHLCIntervalEnum = "ONE_DAY"
	OHLCIntervalThreeDays      OHLCIntervalEnum = "THREE_DAYS"
	OHLCIntervalOneWeek        OHLCIntervalEnum = "ONE_WEEK"
	OHLCIntervalOneMonth       OHLCIntervalEnum = "ONE_MONTH"
)

// OHLCService implements the OHLCServiceInterface
type OHLCService struct {
	repo OHLCRepositoryInterface
}

// NewOHLCService creates a new OHLC service
func NewOHLCService() OHLCServiceInterface {
	// We'll inject the repository later to avoid import cycles
	return &OHLCService{}
}

// SetRepository sets the repository for dependency injection
func (s *OHLCService) SetRepository(repo OHLCRepositoryInterface) {
	s.repo = repo
}

// GetOHLC retrieves OHLC data combining ClickHouse historical data and Redis active data
func (s *OHLCService) GetOHLC(ctx context.Context, req *OHLCRequest) (*OHLCResponse, error) {
	// Validate request
	if err := s.validateRequest(req); err != nil {
		return nil, err
	}

	// Convert GraphQL enum to model timeframe
	timeframe, err := s.convertIntervalToTimeframe(req.Interval)
	if err != nil {
		return nil, err
	}

	// Convert timestamp from milliseconds to time.Time
	requestTime := time.Unix(0, req.Timestamp*int64(time.Millisecond))

	// Get historical data from ClickHouse
	historicalReq := &OHLCQueryRequest{
		Symbol:    req.Symbol,
		Timeframe: timeframe,
		Timestamp: requestTime,
		IsForward: req.IsForward,
		Limit:     req.Limit,
	}

	historicalKlines, err := s.repo.GetHistoricalKlines(ctx, historicalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical klines: %w", err)
	}

	// Get current active kline from Redis
	activeKline, err := s.repo.GetActiveKline(ctx, req.Symbol, timeframe)
	if err != nil {
		global.GVA_LOG.Error("Failed to get active kline from Redis",
			zap.String("symbol", req.Symbol),
			zap.String("timeframe", string(timeframe)),
			zap.Error(err))
		// Continue without active kline if Redis fails
	}

	// Combine historical and active data
	combinedKlines := s.combineKlines(historicalKlines, activeKline, timeframe, req.IsForward)

	// Handle Redis delay fallback if needed
	combinedKlines = s.handleRedisDelayFallback(ctx, combinedKlines, req.Symbol, timeframe)

	// Convert to response format
	response := &OHLCResponse{
		Symbol: req.Symbol,
		Data:   s.convertKlinesToOHLCData(combinedKlines),
	}

	// Apply limit to final result
	if len(response.Data) > req.Limit {
		response.Data = response.Data[:req.Limit]
	}

	return response, nil
}

// validateRequest validates the OHLC request parameters
func (s *OHLCService) validateRequest(req *OHLCRequest) error {
	if req.Symbol == "" {
		return fmt.Errorf("symbol is required")
	}
	if req.Limit <= 0 {
		return fmt.Errorf("limit must be greater than 0")
	}
	if req.Limit > 10000 {
		return fmt.Errorf("limit cannot exceed 10000")
	}
	if req.Timestamp <= 0 {
		return fmt.Errorf("timestamp must be greater than 0")
	}
	return nil
}

// convertIntervalToTimeframe converts GraphQL enum to model timeframe
func (s *OHLCService) convertIntervalToTimeframe(interval OHLCIntervalEnum) (model.KlineTimeframe, error) {
	switch interval {
	case OHLCIntervalOneMinute:
		return model.Timeframe1m, nil
	case OHLCIntervalThreeMinutes:
		return model.Timeframe3m, nil
	case OHLCIntervalFiveMinutes:
		return model.Timeframe5m, nil
	case OHLCIntervalFifteenMinutes:
		return model.Timeframe15m, nil
	case OHLCIntervalThirtyMinutes:
		return model.Timeframe30m, nil
	case OHLCIntervalOneHour:
		return model.Timeframe1h, nil
	case OHLCIntervalTwoHours:
		return model.Timeframe2h, nil
	case OHLCIntervalFourHours:
		return model.Timeframe4h, nil
	case OHLCIntervalEightHours:
		return model.Timeframe8h, nil
	case OHLCIntervalTwelveHours:
		return model.Timeframe12h, nil
	case OHLCIntervalOneDay:
		return model.Timeframe1d, nil
	case OHLCIntervalThreeDays:
		return model.Timeframe3d, nil
	case OHLCIntervalOneWeek:
		return model.Timeframe1w, nil
	case OHLCIntervalOneMonth:
		return model.Timeframe1mo, nil
	default:
		return "", fmt.Errorf("unsupported interval: %s", interval)
	}
}

// combineKlines combines historical klines with active kline data
func (s *OHLCService) combineKlines(historicalKlines []*model.Kline, activeKline *model.Kline, _ model.KlineTimeframe, isForward bool) []*model.Kline {
	var combinedKlines []*model.Kline

	// Add historical klines
	combinedKlines = append(combinedKlines, historicalKlines...)

	// Add active kline if it exists and doesn't overlap with historical data
	if activeKline != nil {
		shouldAddActive := true

		// Check if active kline overlaps with historical data
		for _, historical := range historicalKlines {
			if activeKline.Timestamp.Equal(historical.Timestamp) {
				shouldAddActive = false
				break
			}
		}

		if shouldAddActive {
			combinedKlines = append(combinedKlines, activeKline)
		}
	}

	// Sort by timestamp
	sort.Slice(combinedKlines, func(i, j int) bool {
		if isForward {
			return combinedKlines[i].Timestamp.Before(combinedKlines[j].Timestamp)
		}
		return combinedKlines[i].Timestamp.After(combinedKlines[j].Timestamp)
	})

	return combinedKlines
}

// handleRedisDelayFallback handles the case when Redis is delayed and creates fallback data
func (s *OHLCService) handleRedisDelayFallback(ctx context.Context, klines []*model.Kline, symbol string, timeframe model.KlineTimeframe) []*model.Kline {
	if len(klines) == 0 || s.repo == nil {
		return klines
	}

	// Get the current time and calculate the expected current candle timestamp
	now := time.Now()
	currentCandleTime := timeframe.TruncateTime(now)

	// Check if we have data for the current candle period
	hasCurrentCandle := false
	for _, kline := range klines {
		if kline.Timestamp.Equal(currentCandleTime) {
			hasCurrentCandle = true
			break
		}
	}

	// If we don't have current candle data and it's been more than 2 seconds into the candle period
	if !hasCurrentCandle && now.Sub(currentCandleTime) > 2*time.Second {
		// Get the last completed kline for fallback
		lastKline, err := s.repo.GetLastCompletedKline(ctx, symbol, timeframe, currentCandleTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get last completed kline for fallback",
				zap.String("symbol", symbol),
				zap.String("timeframe", string(timeframe)),
				zap.Error(err))
			return klines
		}

		if lastKline != nil {
			// Create fallback kline using the close price of the previous candle
			fallbackKline := &model.Kline{
				Timestamp:   currentCandleTime,
				Symbol:      symbol,
				Open:        lastKline.Close,
				High:        lastKline.Close,
				Low:         lastKline.Close,
				Close:       lastKline.Close,
				Volume:      0,
				TradesCount: 0,
			}

			klines = append(klines, fallbackKline)

			// Re-sort the klines
			sort.Slice(klines, func(i, j int) bool {
				return klines[i].Timestamp.Before(klines[j].Timestamp)
			})
		}
	}

	return klines
}

// convertKlinesToOHLCData converts model.Kline to OHLCData for GraphQL response
func (s *OHLCService) convertKlinesToOHLCData(klines []*model.Kline) []*OHLCData {
	var ohlcData []*OHLCData

	for _, kline := range klines {
		ohlc := &OHLCData{
			Timestamp: kline.Timestamp.UnixMilli(),
			Open:      kline.Open,
			High:      kline.High,
			Low:       kline.Low,
			Close:     kline.Close,
			Volume:    kline.Volume,
		}
		ohlcData = append(ohlcData, ohlc)
	}

	return ohlcData
}
