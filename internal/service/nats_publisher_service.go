package service

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// NATSPublisherService handles publishing messages to NATS
type NATSPublisherService struct {
	natsClient *nats.Conn
	js         nats.JetStreamContext
}

// NewNATSPublisherService creates a new NATS publisher service
func NewNATSPublisherService() *NATSPublisherService {
	natsClient := initializer.GetNATS()
	return &NATSPublisherService{
		natsClient: natsClient.GetConn(),
		js:         natsClient.GetJetStream(),
	}
}

// PublishRawUserFill publishes a raw WebSocket message to NATS immediately for minimal latency
func (n *NATSPublisherService) PublishRawUserFill(ctx context.Context, rawData []byte) error {
	// Optimized: Create raw NATS message with minimal allocations
	rawMessage := model.CreateRawWebSocketMessage(rawData)

	// Optimized: Convert to JSON with pre-allocated buffer estimation
	jsonData, err := rawMessage.ToJSON()
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal raw user fill message", zap.Error(err))
		return fmt.Errorf("failed to marshal raw message: %w", err)
	}

	// Optimized: Publish to NATS JetStream with async publish for better throughput
	pubAck, err := n.js.PublishAsync(model.SubjectRawUserFills, jsonData)
	if err != nil {
		global.GVA_LOG.Error("Failed to publish raw user fill to NATS",
			zap.String("subject", model.SubjectRawUserFills),
			zap.Error(err))
		return fmt.Errorf("failed to publish raw message to NATS: %w", err)
	}

	// Optional: Check publish acknowledgment in background (non-blocking)
	go func() {
		select {
		case <-pubAck.Ok():
			global.GVA_LOG.Debug("Published raw user fill to NATS",
				zap.String("message_id", rawMessage.MessageID),
				zap.Time("timestamp", rawMessage.Timestamp),
				zap.Int("raw_data_size", len(rawData)))
		case err := <-pubAck.Err():
			global.GVA_LOG.Error("Async publish failed",
				zap.String("message_id", rawMessage.MessageID),
				zap.Error(err))
		case <-time.After(5 * time.Second):
			global.GVA_LOG.Warn("Async publish acknowledgment timeout",
				zap.String("message_id", rawMessage.MessageID))
		}
	}()

	return nil
}

// PublishOptimizedUserFill publishes raw user_fills messages directly to NATS for ultra-low latency
// This method prioritizes speed over message size optimization by avoiding any JSON parsing
func (n *NATSPublisherService) PublishOptimizedUserFill(ctx context.Context, rawData []byte) error {
	// ULTRA-FAST PATH: Publish raw data directly without any parsing or processing
	// This achieves the lowest possible latency by eliminating all JSON operations

	// Quick validation: Check if this looks like a user_fills message without full parsing
	if !bytes.Contains(rawData, []byte(`"type":"user_fills"`)) &&
		!bytes.Contains(rawData, []byte(`"type": "user_fills"`)) {
		return fmt.Errorf("invalid message: does not appear to be user_fills format")
	}

	// Publish the entire raw WebSocket message directly to NATS
	pubAck, err := n.js.PublishAsync(model.SubjectRawUserFills, rawData)
	if err != nil {
		global.GVA_LOG.Error("Failed to publish raw user_fills to NATS",
			zap.String("subject", model.SubjectRawUserFills),
			zap.Int("message_size", len(rawData)),
			zap.Error(err))
		return fmt.Errorf("failed to publish raw message to NATS: %w", err)
	}

	// Single goroutine for acknowledgment handling (minimal overhead)
	go func() {
		select {
		case <-pubAck.Ok():
			global.GVA_LOG.Debug("Published raw user_fills to NATS",
				zap.Int("message_size", len(rawData)),
				zap.String("optimization", "zero-copy-raw-publish"))
		case err := <-pubAck.Err():
			global.GVA_LOG.Error("Async raw publish failed",
				zap.Int("message_size", len(rawData)),
				zap.Error(err))
		case <-time.After(5 * time.Second):
			global.GVA_LOG.Warn("Async raw publish acknowledgment timeout",
				zap.Int("message_size", len(rawData)))
		}
	}()

	return nil
}

// EnsureStream creates the NATS stream if it doesn't exist
func (n *NATSPublisherService) EnsureStream() error {
	// Check if stream exists
	_, err := n.js.StreamInfo(model.StreamKline)
	if err != nil {
		// Stream doesn't exist, create it
		streamConfig := &nats.StreamConfig{
			Name:        model.StreamKline,
			Description: "Stream for kline/OHLCV processing",
			Subjects:    []string{model.SubjectRawUserFills},
			MaxAge:      24 * time.Hour,     // 24 hours
			MaxBytes:    1024 * 1024 * 1024, // 1GB
			Storage:     nats.FileStorage,   // Explicit file storage for persistence
		}

		_, err = n.js.AddStream(streamConfig)
		if err != nil {
			global.GVA_LOG.Error("Failed to create NATS stream",
				zap.String("stream", model.StreamKline),
				zap.Error(err))
			return fmt.Errorf("failed to create stream: %w", err)
		}

		global.GVA_LOG.Info("Created NATS stream successfully",
			zap.String("stream", model.StreamKline),
			zap.String("retention", "LimitsPolicy"),
			zap.String("storage", "FileStorage"))
	}

	return nil
}

// GetStreamInfo returns information about the stream
func (n *NATSPublisherService) GetStreamInfo() (*nats.StreamInfo, error) {
	return n.js.StreamInfo(model.StreamKline)
}

// GetPublishStats returns publishing statistics
func (n *NATSPublisherService) GetPublishStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// Get stream info if available
	if streamInfo, err := n.GetStreamInfo(); err == nil {
		stats["stream_name"] = streamInfo.Config.Name
		stats["total_messages"] = streamInfo.State.Msgs
		stats["total_bytes"] = streamInfo.State.Bytes
		stats["first_seq"] = streamInfo.State.FirstSeq
		stats["last_seq"] = streamInfo.State.LastSeq
	}

	return stats
}
