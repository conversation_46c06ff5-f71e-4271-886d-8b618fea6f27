package service

import (
	"context"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// RealtimeKlineService processes real-time trading data and updates klines
type RealtimeKlineService struct {
	redisKlineService *RedisKlineService
}

// NewRealtimeKlineService creates a new real-time kline service
func NewRealtimeKlineService() *RealtimeKlineService {
	return &RealtimeKlineService{
		redisKlineService: NewRedisKlineService(),
	}
}

// ProcessUserFillData processes optimized user fill data and updates klines for all timeframes
func (r *RealtimeKlineService) ProcessUserFillData(ctx context.Context, userFillData *UserFillData) error {
	// Validate the message
	if !r.isValidUserFillData(userFillData) {
		global.GVA_LOG.Warn("Invalid user fill data",
			zap.String("coin", userFillData.Coin),
			zap.String("price", userFillData.Px),
			zap.String("size", userFillData.Sz))
		return nil
	}

	// Parse price and size
	price, err := strconv.ParseFloat(userFillData.Px, 64)
	if err != nil {
		global.GVA_LOG.Error("Failed to parse price", zap.String("price", userFillData.Px), zap.Error(err))
		return err
	}

	size, err := strconv.ParseFloat(userFillData.Sz, 64)
	if err != nil {
		global.GVA_LOG.Error("Failed to parse size", zap.String("size", userFillData.Sz), zap.Error(err))
		return err
	}

	// Convert timestamp to time.Time
	tradeTime := time.UnixMilli(userFillData.Time)

	// Calculate volume (price * size)
	volume := price * size

	global.GVA_LOG.Debug("Processing optimized user fill data",
		zap.String("coin", userFillData.Coin),
		zap.Float64("price", price),
		zap.Float64("size", size),
		zap.Float64("volume", volume),
		zap.Time("time", tradeTime))

	// Use batch update for all timeframes (OPTIMIZED)
	return r.redisKlineService.BatchUpdateKlines(ctx, userFillData.Coin, tradeTime, price, volume)
}

// isValidUserFillData validates optimized user fill data
func (r *RealtimeKlineService) isValidUserFillData(userFillData *UserFillData) bool {
	// Check required fields
	if userFillData.Coin == "" {
		return false
	}

	if userFillData.Px == "" || userFillData.Px == "0" {
		return false
	}

	if userFillData.Sz == "" || userFillData.Sz == "0" {
		return false
	}

	if userFillData.Time <= 0 {
		return false
	}

	// Try to parse price and size to ensure they're valid numbers
	if _, err := strconv.ParseFloat(userFillData.Px, 64); err != nil {
		return false
	}

	if _, err := strconv.ParseFloat(userFillData.Sz, 64); err != nil {
		return false
	}

	return true
}

// GetRealtimeKlines retrieves real-time klines from Redis
func (r *RealtimeKlineService) GetRealtimeKlines(ctx context.Context, symbol string, timeframe model.KlineTimeframe, limit int) ([]RedisKline, error) {
	return r.redisKlineService.GetKlines(ctx, symbol, timeframe, limit)
}

// GetCompletedKlinesForPersistence retrieves completed klines for ClickHouse persistence
func (r *RealtimeKlineService) GetCompletedKlinesForPersistence(ctx context.Context, symbol string, timeframe model.KlineTimeframe) ([]RedisKline, error) {
	return r.redisKlineService.GetCompletedKlines(ctx, symbol, timeframe)
}

// RemovePersistedKlines removes klines from Redis after they've been persisted to ClickHouse
func (r *RealtimeKlineService) RemovePersistedKlines(ctx context.Context, symbol string, timeframe model.KlineTimeframe, timestamps []int64) error {
	return r.redisKlineService.RemoveCompletedKlines(ctx, symbol, timeframe, timestamps)
}

// GetAllSymbolsWithKlines retrieves all symbols that have kline data
func (r *RealtimeKlineService) GetAllSymbolsWithKlines(ctx context.Context) ([]string, error) {
	return r.redisKlineService.GetAllSymbols(ctx)
}

// ConvertRedisKlineToModel converts RedisKline to model.Kline for ClickHouse persistence
func (r *RealtimeKlineService) ConvertRedisKlineToModel(redisKline RedisKline) *model.Kline {
	timestamp := time.Unix(redisKline.Timestamp/1000, (redisKline.Timestamp%1000)*1000000)

	return &model.Kline{
		Timestamp:   timestamp,
		Symbol:      redisKline.Symbol,
		Open:        redisKline.Open,
		High:        redisKline.High,
		Low:         redisKline.Low,
		Close:       redisKline.Close,
		Volume:      redisKline.Volume,
		TradesCount: redisKline.TradesCount,
	}
}

// GetKlineStatistics returns statistics about klines in Redis
func (r *RealtimeKlineService) GetKlineStatistics(ctx context.Context) (map[string]interface{}, error) {
	symbols, err := r.GetAllSymbolsWithKlines(ctx)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_symbols": len(symbols),
		"symbols":       symbols,
		"timeframes":    len(model.GetAllTimeframes()),
	}

	// Count klines per timeframe
	timeframeStats := make(map[string]int)
	for _, symbol := range symbols {
		for _, timeframe := range model.GetAllTimeframes() {
			klines, err := r.GetRealtimeKlines(ctx, symbol, timeframe, 0)
			if err == nil {
				timeframeStats[string(timeframe)] += len(klines)
			}
		}
	}
	stats["klines_per_timeframe"] = timeframeStats

	return stats, nil
}
