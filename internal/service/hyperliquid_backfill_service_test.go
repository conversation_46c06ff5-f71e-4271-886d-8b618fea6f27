package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

func TestParseDurationWithDefault(t *testing.T) {
	tests := []struct {
		name            string
		durationStr     string
		defaultDuration time.Duration
		expected        time.Duration
	}{
		{
			name:            "valid duration string",
			durationStr:     "5s",
			defaultDuration: 3 * time.Second,
			expected:        5 * time.Second,
		},
		{
			name:            "empty duration string",
			durationStr:     "",
			defaultDuration: 3 * time.Second,
			expected:        3 * time.Second,
		},
		{
			name:            "invalid duration string",
			durationStr:     "invalid",
			defaultDuration: 3 * time.Second,
			expected:        3 * time.Second,
		},
		{
			name:            "microsecond duration",
			durationStr:     "200us",
			defaultDuration: 100 * time.Microsecond,
			expected:        200 * time.Microsecond,
		},
		{
			name:            "millisecond duration",
			durationStr:     "50ms",
			defaultDuration: 100 * time.Millisecond,
			expected:        50 * time.Millisecond,
		},
		{
			name:            "minute duration",
			durationStr:     "2m",
			defaultDuration: 1 * time.Minute,
			expected:        2 * time.Minute,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseDurationWithDefault(tt.durationStr, tt.defaultDuration)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewOHLCVBackfillServiceWithConfiguration(t *testing.T) {
	// Setup test configuration
	global.GVA_CONFIG = config.Server{
		Backfill: config.Backfill{
			Timing: config.BackfillTiming{
				RetryDelay:                  "5s",
				RateLimitDelay:              "3s",
				InterSymbolDelay:            "75ms",
				BaseTimeout:                 "180s",
				ProgressiveTimeoutIncrement: "90s",
				MaxTimeout:                  "600s",
				TLSHandshakeTimeout:         "45s",
				ResponseHeaderTimeout:       "90s",
				IdleConnTimeout:             "180s",
				DatabaseTimeout:             "3m",
				SymbolsCacheTTL:             "10m",
			},
		},
	}

	service := NewOHLCVBackfillService()

	// Verify all timing values are correctly set from configuration
	assert.Equal(t, 5*time.Second, service.retryDelay)
	assert.Equal(t, 3*time.Second, service.rateLimitDelay)
	assert.Equal(t, 75*time.Millisecond, service.interSymbolDelay)
	assert.Equal(t, 180*time.Second, service.baseTimeout)
	assert.Equal(t, 90*time.Second, service.progressiveTimeoutIncrement)
	assert.Equal(t, 600*time.Second, service.maxTimeout)
	assert.Equal(t, 45*time.Second, service.tlsHandshakeTimeout)
	assert.Equal(t, 90*time.Second, service.responseHeaderTimeout)
	assert.Equal(t, 180*time.Second, service.idleConnTimeout)
	assert.Equal(t, 3*time.Minute, service.databaseTimeout)
	assert.Equal(t, 10*time.Minute, service.symbolsCacheTTL)
}

func TestNewOHLCVBackfillServiceWithDefaults(t *testing.T) {
	// Setup test configuration with empty timing values to test defaults
	global.GVA_CONFIG = config.Server{
		Backfill: config.Backfill{
			Timing: config.BackfillTiming{
				// All empty strings to test default fallbacks
			},
		},
	}

	service := NewOHLCVBackfillService()

	// Verify all timing values fall back to defaults
	assert.Equal(t, 3*time.Second, service.retryDelay)
	assert.Equal(t, 2*time.Second, service.rateLimitDelay)
	assert.Equal(t, 50*time.Millisecond, service.interSymbolDelay)
	assert.Equal(t, 120*time.Second, service.baseTimeout)
	assert.Equal(t, 60*time.Second, service.progressiveTimeoutIncrement)
	assert.Equal(t, 300*time.Second, service.maxTimeout)
	assert.Equal(t, 30*time.Second, service.tlsHandshakeTimeout)
	assert.Equal(t, 60*time.Second, service.responseHeaderTimeout)
	assert.Equal(t, 120*time.Second, service.idleConnTimeout)
	assert.Equal(t, 2*time.Minute, service.databaseTimeout)
	assert.Equal(t, 5*time.Minute, service.symbolsCacheTTL)
}

func TestCreateHTTPClientWithProgressiveTimeout(t *testing.T) {
	// Setup test configuration
	global.GVA_CONFIG = config.Server{
		Backfill: config.Backfill{
			Timing: config.BackfillTiming{
				BaseTimeout:                 "60s",
				ProgressiveTimeoutIncrement: "30s",
				MaxTimeout:                  "180s",
				TLSHandshakeTimeout:         "15s",
				ResponseHeaderTimeout:       "30s",
				IdleConnTimeout:             "60s",
			},
		},
	}

	service := NewOHLCVBackfillService()

	// Test progressive timeout calculation
	tests := []struct {
		retryAttempt    int
		expectedTimeout time.Duration
	}{
		{0, 60 * time.Second},  // base timeout
		{1, 90 * time.Second},  // base + 1 * increment
		{2, 120 * time.Second}, // base + 2 * increment
		{3, 150 * time.Second}, // base + 3 * increment
		{4, 180 * time.Second}, // base + 4 * increment, but capped at max
		{5, 180 * time.Second}, // still capped at max
	}

	for _, tt := range tests {
		t.Run("retry_attempt_"+string(rune(tt.retryAttempt+'0')), func(t *testing.T) {
			client := service.createHTTPClient(tt.retryAttempt)
			assert.Equal(t, tt.expectedTimeout, client.Timeout)
		})
	}
}

func TestTimingBehaviorFix(t *testing.T) {
	// This test demonstrates that the timing fix eliminates the double rate-limit delay
	// Setup test configuration with known timing values
	global.GVA_CONFIG = config.Server{
		Backfill: config.Backfill{
			Timing: config.BackfillTiming{
				RateLimitDelay:   "100ms", // Short delay for testing
				InterSymbolDelay: "10ms",  // Short delay for testing
			},
		},
	}

	service := NewOHLCVBackfillService()

	// Verify the timing values are set correctly
	assert.Equal(t, 100*time.Millisecond, service.rateLimitDelay)
	assert.Equal(t, 10*time.Millisecond, service.interSymbolDelay)

	// The key insight: processSymbolBackfill no longer applies rate limit delay
	// Only fetchCandlesWithClient applies it, eliminating the double delay issue
	t.Log("Rate limit delay is now only applied once per API call in fetchCandlesWithClient")
	t.Log("Inter-symbol delay provides spacing between symbol processing starts")
	t.Log("Total expected delay between symbols: inter-symbol-delay (10ms) + rate-limit-delay (100ms) = ~110ms")
	t.Log("Previous behavior: inter-symbol-delay + 2x rate-limit-delay = 10ms + 200ms = ~210ms")
}
