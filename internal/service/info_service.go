package service

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service/data"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service/hyperliquid"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service/node"
)

// InfoRequest represents the general request structure for /info endpoint
type InfoRequest struct {
	Type string `json:"type"` // Required for meta type

	// For meta type
	Dex string `json:"dex,omitempty"` // Optional for meta type

	// For candleSnapshot type
	Req *KlineRequest `json:"req,omitempty"`

	// For activeAssetData type
	User string `json:"user,omitempty"`
	Coin string `json:"coin,omitempty"`

	// For maxBuilderFee type
	Builder string `json:"builder,omitempty"`

	// For fundingHistory type
	StartTime int64 `json:"startTime,omitempty"`
	EndTime   int64 `json:"endTime,omitempty"`

	// For userFillsByTime and userFills types
	AggregateByTime bool `json:"aggregateByTime,omitempty"`
}

// KlineRequest represents the kline data request parameters
type KlineRequest struct {
	Coin      string `json:"coin"`
	Interval  string `json:"interval"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

// InfoResponse represents the response structure for /info endpoint
type InfoResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}

type GenericResponse map[string]interface{}

type CandleData struct {
	StartTime  int64  `json:"t"` // Start timestamp (milliseconds)
	EndTime    int64  `json:"T"` // End timestamp (milliseconds)
	Symbol     string `json:"s"` // Symbol/coin name
	Interval   string `json:"i"` // Time interval
	OpenPrice  string `json:"o"` // Open price (string format)
	ClosePrice string `json:"c"` // Close price (string format)
	HighPrice  string `json:"h"` // High price (string format)
	LowPrice   string `json:"l"` // Low price (string format)
	Volume     string `json:"v"` // Volume (string format)
	TradeCount int    `json:"n"` // Number of trades
}

// InfoService handles information requests
type InfoService struct {
	klineService *KlineService
	ohlcRepo     OHLCRepositoryInterface
	httpClient   *http.Client
	infoAPIURL   string
	nodeAPIURL   string
	proxyManager *ProxyManager
	rateLimiter  *hyperliquid.RestRateLimiter
	redisClient  *redis.Client
}

// NewInfoService creates a new info service instance
func NewInfoService() *InfoService {
	// Parse timeout from config
	timeout, err := time.ParseDuration(global.GVA_CONFIG.InfoAPI.Timeout)
	if err != nil {
		global.GVA_LOG.Warn("Failed to parse InfoAPI timeout, using default 30s", zap.Error(err))
		timeout = 30 * time.Second
	}

	proxyManager := GetSharedProxyManager()

	rateLimiter := hyperliquid.NewRestRateLimiter()

	return &InfoService{
		klineService: NewKlineService(),
		httpClient: &http.Client{
			Timeout: timeout,
		},
		infoAPIURL:   global.GVA_CONFIG.InfoAPI.URL,
		nodeAPIURL:   global.GVA_CONFIG.NodeAPI.URL,
		proxyManager: proxyManager,
		rateLimiter:  rateLimiter,
		redisClient:  global.GVA_REDIS,
		// ohlcRepo will be injected later to avoid import cycles
	}
}

// SetOHLCRepository sets the OHLC repository for dependency injection
func (s *InfoService) SetOHLCRepository(repo OHLCRepositoryInterface) {
	s.ohlcRepo = repo
}

// convertKlinesToCandleData converts []model.Kline to []CandleData
func (s *InfoService) convertKlinesToCandleData(klines []model.Kline, interval string, timeframe model.KlineTimeframe) []CandleData {
	candleData := make([]CandleData, len(klines))

	for i, kline := range klines {
		// Calculate end time (start time + interval duration - 1 millisecond)
		// This matches Hyperliquid's format where T is the last millisecond of the candle period
		endTime := kline.Timestamp.Add(time.Duration(timeframe.GetMinutes()) * time.Minute).Add(-time.Millisecond)

		candleData[i] = CandleData{
			StartTime:  kline.Timestamp.UnixMilli(),
			EndTime:    endTime.UnixMilli(),
			Symbol:     kline.Symbol,
			Interval:   interval,
			OpenPrice:  strconv.FormatFloat(kline.Open, 'f', -1, 64),
			ClosePrice: strconv.FormatFloat(kline.Close, 'f', -1, 64),
			HighPrice:  strconv.FormatFloat(kline.High, 'f', -1, 64),
			LowPrice:   strconv.FormatFloat(kline.Low, 'f', -1, 64),
			Volume:     strconv.FormatFloat(kline.Volume, 'f', -1, 64),
			TradeCount: int(kline.TradesCount),
		}
	}

	return candleData
}

// HandleInfoRequest handles the /info endpoint for different request types
func (s *InfoService) HandleInfoRequest(c *gin.Context) {
	// Debug: Try to read raw body first
	body, err := c.GetRawData()
	if err != nil {
		global.GVA_LOG.Error("Failed to read raw body", zap.Error(err))
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read request body: %v", err),
		})
		return
	}

	// Restore the request body
	c.Request.Body = io.NopCloser(bytes.NewReader(body))

	var req InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		global.GVA_LOG.Error("Failed to bind JSON", zap.Error(err))
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Invalid request format: %v", err),
		})
		return
	}

	switch req.Type {
	case "candleSnapshot":
		s.handleCandleSnapshotRequest(c, req)
	case "meta":
		// type is required (already validated by switch case matching)
		s.HandleMetaRequest(c, req.Dex)
	case "spotMeta":
		s.HandleSpotMetaRequest(c, req.User)
	case "clearinghouseState":
		// type is required (already validated by switch case matching)
		// user is required
		s.HandleClearinghouseStateRequest(c, req.User, req.Dex)
	case "spotClearinghouseState":
		s.handleSpotClearinghouseStateRequest(c, req.User)
	case "openOrders":
		s.handleOpenOrdersRequest(c, req.User, req.Dex)
	case "frontendOpenOrders":
		s.handleFrontendOpenOrdersRequest(c, req.User, req.Dex)
	// case "liquidatable":
	// 	s.handleLiquidatableRequest(c, req.User, req.Coin)
	case "activeAssetData":
		s.handleActiveAssetDataRequest(c, req.User, req.Coin)
	// case "maxMarketOrderNtls":
	// 	s.handleMaxMarketOrderNtlsRequest(c, req.User, req.Coin)
	// case "vaultSummaries":
	// 	s.handleVaultSummariesRequest(c, req.User, req.Coin)
	case "userVaultEquities":
		s.handleUserVaultEquitiesRequest(c, req.User)
	// case "leadingVaults":
	// 	s.handleLeadingVaultsRequest(c, req.User)
	// case "extraAgents":
	// 	s.handleExtraAgentsRequest(c, req.User)
	case "subAccounts":
		s.handleSubAccountsRequest(c, req.User)
	case "userFees":
		s.handleUserFeesRequest(c, req.User)
	case "userRateLimit":
		s.handleUserRateLimitRequest(c, req.User)
	case "spotDeployState":
		s.handleSpotDeployStateRequest(c, req.User)
	case "perpDeployAuctionStatus":
		s.handlePerpDeployAuctionStatusRequest(c)
	case "delegations":
		s.handleDelegationsRequest(c, req.User)
	case "delegatorSummary":
		s.handleDelegatorSummaryRequest(c, req.User)
	case "maxBuilderFee":
		s.handleMaxBuilderFeeRequest(c, req.User, req.Builder)
	// 	case "userToMultiSigSigners":
	// s.handleUserToMultiSigSignersRequest(c, req.User, req.Builder)
	case "userRole":
		s.handleUserRoleRequest(c, req.User)
	case "perpsAtOpenInterestCap":
		s.handlePerpsAtOpenInterestCapRequest(c)
	// case "validatorL1Votes":
	// 	s.handleValidatorL1VotesRequest(c)
	// case "marginTable":
	// 	s.handleMarginTableRequest(c)
	case "perpDexs":
		s.handlePerpDexsRequest(c)
	case "webData2":
		s.handleWebData2Request(c)
	case "allMids":
		s.handleAllMidsRequest(c, req)
	case "metaAndAssetCtxs":
		s.handleMetaAndAssetCtxsRequest(c)
	case "fundingHistory":
		s.handleFundingHistoryRequest(c, req.Coin, req.StartTime, req.EndTime)
	case "portfolio":
		s.handlePortfolioRequest(c, req.User)
	// tidb data
	case "userFills":
		s.handleUserFillsRequest(c, req.User, req.AggregateByTime)
	case "userFillsByTime":
		s.handleUserFillsByTimeRequest(c, req.User, req.StartTime, req.EndTime, req.AggregateByTime)
	case "historicalOrders":
		s.handleHistoricalOrdersRequest(c, req.User)
	case "userNonFundingLedgerUpdates":
		s.handleUserNonFundingLedgerUpdatesRequest(c, req.User, req.StartTime, req.EndTime)
	default:
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Unsupported request type: %s", req.Type),
		})
	}
}

// SetCachedResponse sets cached response data (for middleware use)
func (s *InfoService) SetCachedResponse(c *gin.Context, data interface{}) {
	// Serialize response data to JSON string and set it in response header
	// This allows middleware to get response data for caching
	if jsonData, err := json.Marshal(data); err == nil {
		c.Header("X-Cached-Response", string(jsonData))
	}
}

func (s *InfoService) generateCacheKey(requestType string, params map[string]interface{}) string {
	key := fmt.Sprintf("info:%s", requestType)

	switch requestType {
	case "fundingHistory":
		if coin, ok := params["coin"].(string); ok {
			key += fmt.Sprintf(":%s", coin)
		}
		if startTime, ok := params["startTime"].(int64); ok {
			key += fmt.Sprintf(":%d", startTime)
		}
		if endTime, ok := params["endTime"].(int64); ok {
			key += fmt.Sprintf(":%d", endTime)
		}
	case "portfolio":
		if user, ok := params["user"].(string); ok {
			key += fmt.Sprintf(":%s", user)
		}
	case "candleSnapshot":
		if coin, ok := params["coin"].(string); ok {
			key += fmt.Sprintf(":%s", coin)
		}
		if interval, ok := params["interval"].(string); ok {
			key += fmt.Sprintf(":%s", interval)
		}
		if startTime, ok := params["startTime"].(int64); ok {
			key += fmt.Sprintf(":%d", startTime)
		}
		if endTime, ok := params["endTime"].(int64); ok {
			key += fmt.Sprintf(":%d", endTime)
		}
	}

	return key
}

func (s *InfoService) getCachedResponse(ctx context.Context, cacheKey string) ([]byte, error) {
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}

	result, err := s.redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get cache: %w", err)
	}

	return []byte(result), nil
}

func (s *InfoService) setCachedResponse(ctx context.Context, cacheKey string, data []byte, ttl time.Duration) error {
	if s.redisClient == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	err := s.redisClient.Set(ctx, cacheKey, string(data), ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to set cache: %w", err)
	}

	return nil
}

func (s *InfoService) getCacheTTL(requestType string) time.Duration {
	switch requestType {
	case "allMids":
		return 10 * time.Minute
	case "metaAndAssetCtxs":
		return 5 * time.Minute
	case "fundingHistory":
		return 1 * time.Minute
	case "portfolio":
		return 30 * time.Second
	case "candleSnapshot":
		return 1 * time.Second // Short TTL since it includes active candle data
	default:
		return 1 * time.Minute
	}
}

func (s *InfoService) HandleMetaRequest(c *gin.Context, dex string) error {
	req := InfoRequest{
		Type: "meta",
		Dex:  dex,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) HandleSpotMetaRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "spotMeta",
		User: user,
	}
	return s.HandleRequest(c, req)
}
func (s *InfoService) HandleClearinghouseStateRequest(c *gin.Context, user string, dex string) error {
	req := InfoRequest{
		Type: "clearinghouseState",
		User: user,
		Dex:  dex,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleSpotClearinghouseStateRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "spotClearinghouseState",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleOpenOrdersRequest(c *gin.Context, user string, dex string) error {
	req := InfoRequest{
		Type: "openOrders",
		User: user,
		Dex:  dex,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleFrontendOpenOrdersRequest(c *gin.Context, user string, dex string) error {
	req := InfoRequest{
		Type: "frontendOpenOrders",
		User: user,
		Dex:  dex,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleActiveAssetDataRequest(c *gin.Context, user, coin string) error {
	req := InfoRequest{
		Type: "activeAssetData",
		User: user,
		Coin: coin,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleUserVaultEquitiesRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "userVaultEquities",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleSubAccountsRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "subAccounts",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleUserFeesRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "userFees",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleUserRateLimitRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "userRateLimit",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleSpotDeployStateRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "spotDeployState",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handlePerpDeployAuctionStatusRequest(c *gin.Context) error {
	req := InfoRequest{
		Type: "perpDeployAuctionStatus",
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleDelegationsRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "delegations",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleDelegatorSummaryRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "delegatorSummary",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleMaxBuilderFeeRequest(c *gin.Context, user, builder string) error {
	req := InfoRequest{
		Type:    "maxBuilderFee",
		User:    user,
		Builder: builder,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleUserRoleRequest(c *gin.Context, user string) error {
	req := InfoRequest{
		Type: "userRole",
		User: user,
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handlePerpsAtOpenInterestCapRequest(c *gin.Context) error {
	req := InfoRequest{
		Type: "perpsAtOpenInterestCap",
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handlePerpDexsRequest(c *gin.Context) error {
	req := InfoRequest{
		Type: "perpDexs",
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) handleWebData2Request(c *gin.Context) error {
	req := InfoRequest{
		Type: "webData2",
	}
	return s.HandleRequest(c, req)
}

func (s *InfoService) HandleRequest(c *gin.Context, req InfoRequest) error {
	requestBody := s.buildRequestBody(req)

	responseBody, err := s.sendNodeAPIRequest(c, requestBody)
	if err != nil {
		return err
	}

	return s.parseAndReturnResponse(c, req.Type, responseBody)
}

func (s *InfoService) buildRequestBody(req InfoRequest) map[string]interface{} {
	requestBody := map[string]interface{}{
		"type": req.Type,
	}

	switch req.Type {
	case "meta":
		// type is required (already added above)
		if req.Dex != "" {
			requestBody["dex"] = req.Dex
		}
	case "allMids":
		// type is required (already added above)
		if req.Dex != "" {
			requestBody["dex"] = req.Dex
		}
	case "metaAndAssetCtxs":
		// These request types only require the type field
		// No additional parameters are needed
	case "clearinghouseState":
		// type is required (already added above)
		// user is required
		requestBody["user"] = req.User
		if req.Dex != "" {
			requestBody["dex"] = req.Dex
		}
	case "openOrders":
		if req.User != "" {
			requestBody["user"] = req.User
		}
		if req.Dex != "" {
			requestBody["dex"] = req.Dex
		}
	case "frontendOpenOrders":
		if req.User != "" {
			requestBody["user"] = req.User
		}
		if req.Dex != "" {
			requestBody["dex"] = req.Dex
		}
	case "spotMeta", "spotClearinghouseState",
		"userVaultEquities",
		"subAccounts", "userFees", "userRateLimit", "spotDeployState",
		"delegations", "delegatorSummary", "userRole", "portfolio":
		if req.User != "" {
			requestBody["user"] = req.User
		}
	case "activeAssetData":
		if req.User != "" {
			requestBody["user"] = req.User
		}
		if req.Coin != "" {
			requestBody["coin"] = req.Coin
		}
	case "maxBuilderFee":
		if req.User != "" {
			requestBody["user"] = req.User
		}
		if req.Builder != "" {
			requestBody["builder"] = req.Builder
		}
	case "fundingHistory":
		if req.Coin != "" {
			requestBody["coin"] = req.Coin
		}
		if req.StartTime > 0 {
			requestBody["startTime"] = req.StartTime
		}
		if req.EndTime > 0 {
			requestBody["endTime"] = req.EndTime
		}
	case "userFills":
		if req.User != "" {
			requestBody["user"] = req.User
		}
		if req.AggregateByTime {
			requestBody["aggregateByTime"] = req.AggregateByTime
		}
	case "historicalOrders":
		if req.User != "" {
			requestBody["user"] = req.User
		}
	}

	return requestBody
}

func (s *InfoService) sendHTTPRequest(c *gin.Context, requestBody map[string]interface{}) ([]byte, error) {
	return s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "", 0, 0)
}

func (s *InfoService) sendNodeAPIRequest(c *gin.Context, requestBody map[string]interface{}) ([]byte, error) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal request body", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to prepare request: %v", err),
		})
		return nil, err
	}

	httpReq, err := http.NewRequestWithContext(c.Request.Context(), "POST", s.nodeAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		global.GVA_LOG.Error("Failed to create HTTP request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create request: %v", err),
		})
		return nil, err
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		global.GVA_LOG.Error("Failed to make HTTP request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to reach external API: %v", err),
		})
		return nil, err
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("Failed to read response body", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read response: %v", err),
		})
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("External API returned error",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(responseBody)))
		c.JSON(resp.StatusCode, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("External API error: %s", string(responseBody)),
		})
		return nil, fmt.Errorf("external API error: %s", string(responseBody))
	}

	return responseBody, nil
}

func (s *InfoService) sendHTTPRequestWithProxyAndRateLimit(c *gin.Context, requestBody map[string]interface{}, requestType string, batchLength int, resultItemCount int) ([]byte, error) {
	if s.rateLimiter != nil && requestType != "" {
		var infoRequestType hyperliquid.InfoRequestType
		switch requestType {
		case "allMids":
			infoRequestType = hyperliquid.RequestTypeAllMids
		case "metaAndAssetCtxs":
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot // Use the same weight
		case "fundingHistory":
			infoRequestType = hyperliquid.RequestTypeFundingHistory
		case "portfolio":
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot // Use the same weight
		default:
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot
		}

		if !s.rateLimiter.CanMakeRequest(infoRequestType, batchLength, resultItemCount) {
			global.GVA_LOG.Warn("Rate limit exceeded for request",
				zap.String("request_type", requestType),
				zap.Int("current_weight", int(s.rateLimiter.GetCurrentWeight())),
				zap.Int("max_weight", 1200))
			c.JSON(http.StatusTooManyRequests, InfoResponse{
				Success: false,
				Message: "Rate limit exceeded, please try again later",
			})
			return nil, fmt.Errorf("rate limit exceeded")
		}
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal request body", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to prepare request: %v", err),
		})
		return nil, err
	}

	client := s.httpClient
	if s.proxyManager != nil {
		proxy, err := s.proxyManager.GetNextProxy()
		if err != nil {
			global.GVA_LOG.Warn("Failed to get proxy, using direct connection", zap.Error(err))
		} else {
			proxyURL, err := url.Parse(proxy.URL)
			if err != nil {
				global.GVA_LOG.Warn("Invalid proxy URL, using direct connection", zap.Error(err))
			} else {
				client = &http.Client{
					Timeout: s.httpClient.Timeout,
					Transport: &http.Transport{
						Proxy: http.ProxyURL(proxyURL),
					},
				}
				global.GVA_LOG.Debug("Using proxy for request", zap.String("proxy_url", proxy.URL))
			}
		}
	}

	httpReq, err := http.NewRequestWithContext(c.Request.Context(), "POST", s.infoAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		global.GVA_LOG.Error("Failed to create HTTP request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create request: %v", err),
		})
		return nil, err
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		if s.proxyManager != nil {
			proxy, _ := s.proxyManager.GetNextProxy()
			if proxy != nil {
				s.proxyManager.MarkProxyFailed(proxy.URL, err)
			}
		}

		global.GVA_LOG.Error("Failed to make HTTP request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to reach external API: %v", err),
		})
		return nil, err
	}
	defer resp.Body.Close()

	if s.proxyManager != nil {
		proxy, _ := s.proxyManager.GetNextProxy()
		if proxy != nil {
			s.proxyManager.MarkProxySuccess(proxy.URL)
		}
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("Failed to read response body", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read response: %v", err),
		})
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("External API returned error",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(responseBody)))
		c.JSON(resp.StatusCode, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("External API error: %s", string(responseBody)),
		})
		return nil, fmt.Errorf("external API error: %s", string(responseBody))
	}

	if s.rateLimiter != nil && requestType != "" {
		var infoRequestType hyperliquid.InfoRequestType
		switch requestType {
		case "allMids":
			infoRequestType = hyperliquid.RequestTypeAllMids
		case "metaAndAssetCtxs":
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot
		case "fundingHistory":
			infoRequestType = hyperliquid.RequestTypeFundingHistory
		case "portfolio":
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot
		default:
			infoRequestType = hyperliquid.RequestTypeCandleSnapshot
		}
		s.rateLimiter.RecordRequest(infoRequestType, batchLength, resultItemCount)
	}

	return responseBody, nil
}

func (s *InfoService) parseAndReturnResponse(c *gin.Context, requestType string, responseBody []byte) error {
	responseParsers := map[string]func([]byte) (interface{}, error){
		"meta":                    s.parseMetaResponse,
		"spotMeta":                s.parseSpotMetaResponse,
		"clearinghouseState":      s.parseClearinghouseStateResponse,
		"spotClearinghouseState":  s.parseSpotClearinghouseStateResponse,
		"openOrders":              s.parseOpenOrdersResponse,
		"frontendOpenOrders":      s.parseFrontendOpenOrdersResponse,
		"activeAssetData":         s.parseActiveAssetDataResponse,
		"userVaultEquities":       s.parseUserVaultEquitiesResponse,
		"subAccounts":             s.parseSubAccountsResponse,
		"userFees":                s.parseUserFeesResponse,
		"userRateLimit":           s.parseUserRateLimitResponse,
		"spotDeployState":         s.parseSpotDeployStateResponse,
		"perpDeployAuctionStatus": s.parsePerpDeployAuctionStatusResponse,
		"delegations":             s.parseDelegationsResponse,
		"delegatorSummary":        s.parseDelegatorSummaryResponse,
		"maxBuilderFee":           s.parseMaxBuilderFeeResponse,
		"userRole":                s.parseUserRoleResponse,
		"perpsAtOpenInterestCap":  s.parsePerpsAtOpenInterestCapResponse,
		"perpDexs":                s.parsePerpDexsResponse,
		"webData2":                s.parseWebData2Response,
	}

	parser, exists := responseParsers[requestType]
	if !exists {
		return s.parseGenericResponse(c, responseBody)
	}

	result, err := parser(responseBody)
	if err != nil {
		global.GVA_LOG.Error("Failed to parse response",
			zap.String("type", requestType),
			zap.Error(err))
		c.Data(http.StatusOK, "application/json", responseBody)
		return err
	}

	c.JSON(http.StatusOK, result)
	return nil
}

func (s *InfoService) parseMetaResponse(responseBody []byte) (interface{}, error) {
	var result node.MateData
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseSpotMetaResponse(responseBody []byte) (interface{}, error) {
	var result node.SpotMeta
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseClearinghouseStateResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("ClearinghouseState raw response",
		zap.String("response", string(responseBody)))

	var result node.ClearinghouseState
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseSpotClearinghouseStateResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("SpotClearinghouseState raw response",
		zap.String("response", string(responseBody)))

	var result node.SpotClearinghouseState
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseOpenOrdersResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("OpenOrders raw response",
		zap.String("response", string(responseBody)))

	var result node.OpenOrdersResponse
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseFrontendOpenOrdersResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("FrontendOpenOrders raw response",
		zap.String("response", string(responseBody)))

	var result node.FrontendOpenOrdersResponse
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseActiveAssetDataResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("ActiveAssetData raw response",
		zap.String("response", string(responseBody)))

	var result node.ActiveAssetData
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseUserVaultEquitiesResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("UserVaultEquities raw response",
		zap.String("response", string(responseBody)))

	var result node.UserVaultEquities
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseSubAccountsResponse(responseBody []byte) (interface{}, error) {
	global.GVA_LOG.Info("SubAccounts raw response",
		zap.String("response", string(responseBody)))

	var result node.SubAccountResponse
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseUserFeesResponse(responseBody []byte) (interface{}, error) {
	var result node.UserFeesData
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseUserRateLimitResponse(responseBody []byte) (interface{}, error) {
	var result node.UserRateLimit
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseSpotDeployStateResponse(responseBody []byte) (interface{}, error) {
	var result node.SpotDeployState
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parsePerpDeployAuctionStatusResponse(responseBody []byte) (interface{}, error) {
	var result node.PerpDeployAuctionStatus
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseDelegationsResponse(responseBody []byte) (interface{}, error) {
	var result node.DelegationList
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseDelegatorSummaryResponse(responseBody []byte) (interface{}, error) {
	var result node.DelegatorSummary
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseMaxBuilderFeeResponse(responseBody []byte) (interface{}, error) {
	var result int
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseUserRoleResponse(responseBody []byte) (interface{}, error) {
	var result node.UserRole
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parsePerpsAtOpenInterestCapResponse(responseBody []byte) (interface{}, error) {
	var result []string
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parsePerpDexsResponse(responseBody []byte) (interface{}, error) {
	var result node.PerpDexResponse
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseWebData2Response(responseBody []byte) (interface{}, error) {
	var result node.WebData2Response
	err := json.Unmarshal(responseBody, &result)
	return result, err
}

func (s *InfoService) parseGenericResponse(c *gin.Context, responseBody []byte) error {
	var result GenericResponse
	if err := json.Unmarshal(responseBody, &result); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal generic response", zap.Error(err))
		c.Data(http.StatusOK, "application/json", responseBody)
		return err
	}

	c.JSON(http.StatusOK, InfoResponse{
		Success: true,
		Data:    result,
	})
	return nil
}

// handleCandleSnapshotRequest handles candleSnapshot type requests
func (s *InfoService) handleCandleSnapshotRequest(c *gin.Context, req InfoRequest) {
	if req.Req == nil {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'req' field for candleSnapshot request",
		})
		return
	}

	// Generate cache key for candleSnapshot
	cacheKey := s.generateCacheKey("candleSnapshot", map[string]interface{}{
		"coin":      req.Req.Coin,
		"interval":  req.Req.Interval,
		"startTime": req.Req.StartTime,
		"endTime":   req.Req.EndTime,
	})

	ctx := c.Request.Context()
	cachedData, err := s.getCachedResponse(ctx, cacheKey)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get cached response", zap.Error(err))
	} else if cachedData != nil {
		global.GVA_LOG.Debug("Cache hit for candleSnapshot request")
		c.Data(http.StatusOK, "application/json", cachedData)
		return
	}

	global.GVA_LOG.Debug("Cache miss for candleSnapshot request, fetching data")

	var timeframe model.KlineTimeframe
	switch req.Req.Interval {
	case "1m":
		timeframe = model.Timeframe1m
	case "3m":
		timeframe = model.Timeframe3m
	case "5m":
		timeframe = model.Timeframe5m
	case "15m":
		timeframe = model.Timeframe15m
	case "30m":
		timeframe = model.Timeframe30m
	case "1h":
		timeframe = model.Timeframe1h
	case "2h":
		timeframe = model.Timeframe2h
	case "4h":
		timeframe = model.Timeframe4h
	case "8h":
		timeframe = model.Timeframe8h
	case "12h":
		timeframe = model.Timeframe12h
	case "1d":
		timeframe = model.Timeframe1d
	case "3d":
		timeframe = model.Timeframe3d
	case "1w":
		timeframe = model.Timeframe1w
	case "1M":
		timeframe = model.Timeframe1mo
	default:
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Unsupported interval: %s", req.Req.Interval),
		})
		return
	}

	startTime := time.Unix(req.Req.StartTime/1000, (req.Req.StartTime%1000)*1000000)
	endTime := time.Unix(req.Req.EndTime/1000, (req.Req.EndTime%1000)*1000000)

	// Get historical data from ClickHouse
	historicalKlines, err := s.klineService.GetKlineData(ctx, req.Req.Coin, timeframe, startTime, endTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to get historical kline data",
			zap.String("coin", req.Req.Coin),
			zap.String("interval", req.Req.Interval),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to retrieve historical kline data: %v", err),
		})
		return
	}

	// Convert []model.Kline to []*model.Kline for consistency with OHLC service
	var historicalKlinePointers []*model.Kline
	for i := range historicalKlines {
		historicalKlinePointers = append(historicalKlinePointers, &historicalKlines[i])
	}

	// Get current active kline from Redis if OHLC repository is available
	var activeKline *model.Kline
	if s.ohlcRepo != nil {
		activeKline, err = s.ohlcRepo.GetActiveKline(ctx, req.Req.Coin, timeframe)
		if err != nil {
			global.GVA_LOG.Error("Failed to get active kline from Redis",
				zap.String("coin", req.Req.Coin),
				zap.String("interval", req.Req.Interval),
				zap.Error(err))
			// Continue without active kline if Redis fails
		}
	}

	// Combine historical and active data
	combinedKlines := s.combineKlinesForCandleSnapshot(historicalKlinePointers, activeKline, timeframe)

	// Handle Redis delay fallback if needed
	combinedKlines = s.handleRedisDelayFallbackForCandleSnapshot(ctx, combinedKlines, req.Req.Coin, timeframe)

	// Filter klines to only include those within the requested time range
	filteredKlines := s.filterKlinesByTimeRange(combinedKlines, startTime, endTime)

	// Convert filtered klines to CandleData format
	candleData := s.convertKlinesToCandleData(s.convertPointersToValues(filteredKlines), req.Req.Interval, timeframe)

	// Serialize response data for caching
	responseBody, err := json.Marshal(candleData)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal candleSnapshot response", zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: "Failed to process response data",
		})
		return
	}

	// Cache the response
	ttl := s.getCacheTTL("candleSnapshot")
	if err := s.setCachedResponse(ctx, cacheKey, responseBody, ttl); err != nil {
		global.GVA_LOG.Warn("Failed to cache candleSnapshot response", zap.Error(err))
	} else {
		global.GVA_LOG.Debug("Cached candleSnapshot response", zap.String("key", cacheKey), zap.Duration("ttl", ttl))
	}

	// Return response
	c.Data(http.StatusOK, "application/json", responseBody)
}

// filterKlinesByTimeRange filters klines to only include those within the specified time range
// Only includes klines where startTime <= kline.Timestamp < endTime
func (s *InfoService) filterKlinesByTimeRange(klines []*model.Kline, startTime, endTime time.Time) []*model.Kline {
	var filteredKlines []*model.Kline

	for _, kline := range klines {
		// Include kline if its timestamp is within the range: startTime <= timestamp < endTime
		if (kline.Timestamp.Equal(startTime) || kline.Timestamp.After(startTime)) && kline.Timestamp.Before(endTime) {
			filteredKlines = append(filteredKlines, kline)
		}
	}

	return filteredKlines
}

// combineKlinesForCandleSnapshot combines historical klines with active kline data
func (s *InfoService) combineKlinesForCandleSnapshot(historicalKlines []*model.Kline, activeKline *model.Kline, _ model.KlineTimeframe) []*model.Kline {
	var combinedKlines []*model.Kline

	// Add historical klines
	combinedKlines = append(combinedKlines, historicalKlines...)

	// Add active kline if it exists and doesn't overlap with historical data
	if activeKline != nil {
		shouldAddActive := true

		// Check if active kline overlaps with historical data
		for _, historical := range historicalKlines {
			if activeKline.Timestamp.Equal(historical.Timestamp) {
				shouldAddActive = false
				break
			}
		}

		if shouldAddActive {
			combinedKlines = append(combinedKlines, activeKline)
		}
	}

	// Sort by timestamp
	sort.Slice(combinedKlines, func(i, j int) bool {
		return combinedKlines[i].Timestamp.Before(combinedKlines[j].Timestamp)
	})

	return combinedKlines
}

// handleRedisDelayFallbackForCandleSnapshot handles the case when Redis is delayed and creates fallback data
func (s *InfoService) handleRedisDelayFallbackForCandleSnapshot(ctx context.Context, klines []*model.Kline, symbol string, timeframe model.KlineTimeframe) []*model.Kline {
	if len(klines) == 0 || s.ohlcRepo == nil {
		return klines
	}

	// Get the current time and calculate the expected current candle timestamp
	now := time.Now()
	currentCandleTime := timeframe.TruncateTime(now)

	// Check if we have data for the current candle period
	hasCurrentCandle := false
	for _, kline := range klines {
		if kline.Timestamp.Equal(currentCandleTime) {
			hasCurrentCandle = true
			break
		}
	}

	// If we don't have current candle data and it's been more than 2 seconds into the candle period
	if !hasCurrentCandle && now.Sub(currentCandleTime) > 2*time.Second {
		// Get the last completed kline for fallback
		lastKline, err := s.ohlcRepo.GetLastCompletedKline(ctx, symbol, timeframe, currentCandleTime)
		if err != nil {
			global.GVA_LOG.Error("Failed to get last completed kline for fallback",
				zap.String("symbol", symbol),
				zap.String("timeframe", string(timeframe)),
				zap.Error(err))
			return klines
		}

		if lastKline != nil {
			// Create fallback kline using the close price of the previous candle
			fallbackKline := &model.Kline{
				Timestamp:   currentCandleTime,
				Symbol:      symbol,
				Open:        lastKline.Close,
				High:        lastKline.Close,
				Low:         lastKline.Close,
				Close:       lastKline.Close,
				Volume:      0,
				TradesCount: 0,
			}

			klines = append(klines, fallbackKline)

			// Re-sort the klines
			sort.Slice(klines, func(i, j int) bool {
				return klines[i].Timestamp.Before(klines[j].Timestamp)
			})
		}
	}

	return klines
}

// convertPointersToValues converts []*model.Kline to []model.Kline
func (s *InfoService) convertPointersToValues(klinePointers []*model.Kline) []model.Kline {
	var klines []model.Kline
	for _, klinePtr := range klinePointers {
		if klinePtr != nil {
			klines = append(klines, *klinePtr)
		}
	}
	return klines
}

// handleAllMidsRequest handles allMids type requests
func (s *InfoService) handleAllMidsRequest(c *gin.Context, req InfoRequest) error {
	param := InfoRequest{
		Type: req.Type,
		Dex:  req.Dex,
	}

	cacheKey := s.generateCacheKey("allMids", map[string]interface{}{})

	ctx := c.Request.Context()
	cachedData, err := s.getCachedResponse(ctx, cacheKey)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get cached response", zap.Error(err))
	} else if cachedData != nil {
		global.GVA_LOG.Debug("Cache hit for allMids request")
		c.Data(http.StatusOK, "application/json", cachedData)
		return nil
	}

	global.GVA_LOG.Debug("Cache miss for allMids request, fetching from API")

	requestBody := s.buildRequestBody(param)

	responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "allMids", 0, 0)
	if err != nil {
		return err
	}

	ttl := s.getCacheTTL("allMids")
	if err := s.setCachedResponse(ctx, cacheKey, responseBody, ttl); err != nil {
		global.GVA_LOG.Warn("Failed to cache response", zap.Error(err))
	} else {
		global.GVA_LOG.Debug("Cached allMids response", zap.String("key", cacheKey), zap.Duration("ttl", ttl))
	}

	// Return raw response as it's a simple map
	c.Data(http.StatusOK, "application/json", responseBody)
	return nil
}

// handleMetaAndAssetCtxsRequest handles metaAndAssetCtxs type requests
func (s *InfoService) handleMetaAndAssetCtxsRequest(c *gin.Context) error {
	req := InfoRequest{
		Type: "metaAndAssetCtxs",
	}

	cacheKey := s.generateCacheKey("metaAndAssetCtxs", map[string]interface{}{})

	ctx := c.Request.Context()
	cachedData, err := s.getCachedResponse(ctx, cacheKey)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get cached response", zap.Error(err))
	} else if cachedData != nil {
		global.GVA_LOG.Debug("Cache hit for metaAndAssetCtxs request")
		c.Data(http.StatusOK, "application/json", cachedData)
		return nil
	}

	global.GVA_LOG.Debug("Cache miss for metaAndAssetCtxs request, fetching from API")

	requestBody := s.buildRequestBody(req)

	responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "metaAndAssetCtxs", 0, 0)
	if err != nil {
		return err
	}

	ttl := s.getCacheTTL("metaAndAssetCtxs")
	if err := s.setCachedResponse(ctx, cacheKey, responseBody, ttl); err != nil {
		global.GVA_LOG.Warn("Failed to cache response", zap.Error(err))
	} else {
		global.GVA_LOG.Debug("Cached metaAndAssetCtxs response", zap.String("key", cacheKey), zap.Duration("ttl", ttl))
	}

	// Return raw response
	c.Data(http.StatusOK, "application/json", responseBody)
	return nil
}

// handleFundingHistoryRequest handles fundingHistory type requests
func (s *InfoService) handleFundingHistoryRequest(c *gin.Context, coin string, startTime, endTime int64) error {
	if coin == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'coin' field for fundingHistory request",
		})
		return fmt.Errorf("missing coin field")
	}

	cacheKey := s.generateCacheKey("fundingHistory", map[string]interface{}{
		"coin":      coin,
		"startTime": startTime,
		"endTime":   endTime,
	})

	ctx := c.Request.Context()
	cachedData, err := s.getCachedResponse(ctx, cacheKey)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get cached response", zap.Error(err))
	} else if cachedData != nil {
		global.GVA_LOG.Debug("Cache hit for fundingHistory request", zap.String("coin", coin))
		c.Data(http.StatusOK, "application/json", cachedData)
		return nil
	}

	global.GVA_LOG.Debug("Cache miss for fundingHistory request, fetching from API", zap.String("coin", coin))

	req := InfoRequest{
		Type:      "fundingHistory",
		Coin:      coin,
		StartTime: startTime,
		EndTime:   endTime,
	}

	requestBody := s.buildRequestBody(req)

	// Use the request method with proxy and rate limiting
	// For `fundingHistory`, we cannot know the number of returned items in advance, so we use 0
	responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "fundingHistory", 0, 0)
	if err != nil {
		return err
	}

	ttl := s.getCacheTTL("fundingHistory")
	if err := s.setCachedResponse(ctx, cacheKey, responseBody, ttl); err != nil {
		global.GVA_LOG.Warn("Failed to cache response", zap.Error(err))
	} else {
		global.GVA_LOG.Debug("Cached fundingHistory response", zap.String("key", cacheKey), zap.Duration("ttl", ttl))
	}

	// Return raw response
	c.Data(http.StatusOK, "application/json", responseBody)
	return nil
}

// handlePortfolioRequest handles portfolio type requests
func (s *InfoService) handlePortfolioRequest(c *gin.Context, user string) error {
	if user == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'user' field for portfolio request",
		})
		return fmt.Errorf("missing user field")
	}

	cacheKey := s.generateCacheKey("portfolio", map[string]interface{}{
		"user": user,
	})

	ctx := c.Request.Context()
	cachedData, err := s.getCachedResponse(ctx, cacheKey)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get cached response", zap.Error(err))
	} else if cachedData != nil {
		global.GVA_LOG.Debug("Cache hit for portfolio request", zap.String("user", user))
		c.Data(http.StatusOK, "application/json", cachedData)
		return nil
	}

	global.GVA_LOG.Debug("Cache miss for portfolio request, fetching from API", zap.String("user", user))

	req := InfoRequest{
		Type: "portfolio",
		User: user,
	}

	requestBody := s.buildRequestBody(req)

	responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "portfolio", 0, 0)
	if err != nil {
		return err
	}

	ttl := s.getCacheTTL("portfolio")
	if err := s.setCachedResponse(ctx, cacheKey, responseBody, ttl); err != nil {
		global.GVA_LOG.Warn("Failed to cache response", zap.Error(err))
	} else {
		global.GVA_LOG.Debug("Cached portfolio response", zap.String("key", cacheKey), zap.Duration("ttl", ttl))
	}

	// Return raw response
	c.Data(http.StatusOK, "application/json", responseBody)
	return nil
}

// handleUserNonFundingLedgerUpdatesRequest handles userNonFundingLedgerUpdates type requests
func (s *InfoService) handleUserNonFundingLedgerUpdatesRequest(c *gin.Context, user string, startTime, endTime int64) error {
	if user == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{Success: false, Message: "Missing 'user' field"})
		return fmt.Errorf("missing user field")
	}
	if startTime <= 0 {
		c.JSON(http.StatusBadRequest, InfoResponse{Success: false, Message: "startTime is required and must be > 0 (milliseconds)"})
		return fmt.Errorf("invalid startTime")
	}
	// Default endTime to now if not provided
	if endTime <= 0 {
		endTime = time.Now().UTC().UnixMilli()
	}
	if !(endTime > startTime) {
		c.JSON(http.StatusBadRequest, InfoResponse{Success: false, Message: "endTime must be greater than startTime"})
		return fmt.Errorf("invalid time range")
	}

	ch := global.GVA_DB_CLICKHOUSE
	if ch == nil {
		c.JSON(http.StatusInternalServerError, InfoResponse{Success: false, Message: "clickhouse not initialized"})
		return fmt.Errorf("clickhouse not initialized")
	}

	const q = `
        SELECT type, toString(usd), toString(fee), toUnixTimestamp64Milli(time), arbi_hash
        FROM user_non_funding_ledger_updates
        WHERE addr = lower(?)
          AND time >= toDateTime64(? / 1000, 3, 'UTC')
          AND time <  toDateTime64(? / 1000, 3, 'UTC')
        ORDER BY time ASC
    `

	rows, err := ch.QueryContext(c.Request.Context(), q, user, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, InfoResponse{Success: false, Message: err.Error()})
		return err
	}
	defer rows.Close()

	type delta struct {
		Type  string  `json:"type"`
		Usdc  string  `json:"usdc"`
		Fee   *string `json:"fee,omitempty"`
		Nonce *int64  `json:"nonce,omitempty"`
	}
	type item struct {
		Time  uint64 `json:"time"`
		Hash  string `json:"hash"`
		Delta delta  `json:"delta"`
	}
	var list []item
	for rows.Next() {
		var typeStr, usdStr, feeStr string
		var timeMs uint64
		var hash string
		if err := rows.Scan(&typeStr, &usdStr, &feeStr, &timeMs, &hash); err != nil {
			c.JSON(http.StatusInternalServerError, InfoResponse{Success: false, Message: err.Error()})
			return err
		}

		// Convert type: "withdrawal" -> "withdraw", "deposit" -> "deposit"
		deltaType := typeStr
		if deltaType == "withdrawal" {
			deltaType = "withdraw"
		}

		it := item{
			Time: timeMs,
			Hash: hash,
			Delta: delta{
				Type: deltaType,
				Usdc: usdStr,
			},
		}

		// Add fee if present and not zero
		if feeStr != "" && feeStr != "0" && feeStr != "0.000000" {
			it.Delta.Fee = &feeStr
		}

		// Note: nonce is not available in the current table structure
		// If needed, it should be added to the table or fetched from another source

		list = append(list, it)
	}
	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, InfoResponse{Success: false, Message: err.Error()})
		return err
	}

	c.JSON(http.StatusOK, list)
	return nil
}

// handleUserFillsRequest handles userFills type requests
func (s *InfoService) handleUserFillsRequest(c *gin.Context, user string, aggregateByTime bool) error {
	if user == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'user' field for userFills request",
		})
		return fmt.Errorf("missing user field")
	}

	ctx := c.Request.Context()

	if aggregateByTime {
		// aggregateByTime is true: 直接请求 Hyperliquid API，存入 Redis 5 秒
		req := InfoRequest{
			Type:            "userFills",
			User:            user,
			AggregateByTime: true,
		}

		requestBody := s.buildRequestBody(req)
		responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "userFills", 0, 0)
		if err != nil {
			return err
		}

		// 存入 Redis，TTL 为 5 秒
		// 注意：这里使用 user 作为 key（key 是 userID）
		redisKey := data.GetUserFillsRedisKey(user)
		if s.redisClient != nil {
			if err := s.redisClient.Set(ctx, redisKey, string(responseBody), data.UserFillsCacheTTL).Err(); err != nil {
				global.GVA_LOG.Warn("Failed to cache userFills response to Redis",
					zap.String("user", user),
					zap.Error(err))
			} else {
				global.GVA_LOG.Debug("Cached userFills response to Redis",
					zap.String("user", user),
					zap.Duration("ttl", data.UserFillsCacheTTL))
			}
		}

		// 返回响应
		c.Data(http.StatusOK, "application/json", responseBody)
		return nil
	} else {
		// aggregateByTime is false: 先从 Redis 获取最新一条数据
		redisKey := data.GetUserFillsRedisKey(user)
		var fills interface{}

		// 1. 先从 Redis 获取该用户的最新一条数据
		if s.redisClient != nil {
			val, err := s.redisClient.Get(ctx, redisKey).Result()
			if err == nil {
				// Redis 中有数据，解析并返回最新一条
				var cachedFills []data.UserLatestFill
				if err := json.Unmarshal([]byte(val), &cachedFills); err == nil && len(cachedFills) > 0 {
					// 返回最新一条数据（数组格式）
					fills = []data.UserLatestFill{cachedFills[0]}
					c.JSON(http.StatusOK, fills)
					return nil
				}
			} else if err != redis.Nil {
				global.GVA_LOG.Warn("Failed to get data from Redis",
					zap.String("user", user),
					zap.Error(err))
			}
		}

		// 2. Redis 没有，查询 ClickHouse
		latestFill, err := s.getUserLatestFillFromClickHouse(ctx, user)
		if err != nil {
			global.GVA_LOG.Warn("Failed to query user latest fill from ClickHouse",
				zap.String("user", user),
				zap.Error(err))
		} else if latestFill != nil {
			// ClickHouse 有数据，存入 Redis 5 秒并返回
			fills = []data.UserLatestFill{*latestFill}
			if s.redisClient != nil {
				dataBytes, err := json.Marshal(fills)
				if err == nil {
					if err := s.redisClient.Set(ctx, redisKey, string(dataBytes), data.UserFillsCacheTTL).Err(); err != nil {
						global.GVA_LOG.Warn("Failed to cache userFills to Redis",
							zap.String("user", user),
							zap.Error(err))
					}
				}
			}
			c.JSON(http.StatusOK, fills)
			return nil
		}

		// 3. ClickHouse 没有，查询 Hyperliquid API
		req := InfoRequest{
			Type:            "userFills",
			User:            user,
			AggregateByTime: false,
		}

		requestBody := s.buildRequestBody(req)
		responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "userFills", 0, 0)
		if err != nil {
			return err
		}

		// 解析响应，检查是否有数据
		var apiFills []interface{}
		if err := json.Unmarshal(responseBody, &apiFills); err == nil && len(apiFills) > 0 {
			// Hyperliquid API 有数据，存入 Redis 5 秒
			if s.redisClient != nil {
				if err := s.redisClient.Set(ctx, redisKey, string(responseBody), data.UserFillsCacheTTL).Err(); err != nil {
					global.GVA_LOG.Warn("Failed to cache userFills response to Redis",
						zap.String("user", user),
						zap.Error(err))
				}
			}
			// 返回最新一条数据
			c.JSON(http.StatusOK, []interface{}{apiFills[0]})
			return nil
		}

		// 4. 没有数据，返回空数组
		c.JSON(http.StatusOK, []interface{}{})
		return nil
	}
}

// getUserLatestFillFromClickHouse 从 ClickHouse 查询用户最新的一条数据
func (s *InfoService) getUserLatestFillFromClickHouse(ctx context.Context, userAddress string) (*data.UserLatestFill, error) {
	if global.GVA_DB_CLICKHOUSE == nil {
		return nil, fmt.Errorf("ClickHouse client not initialized")
	}

	query := `
		SELECT 
			id, user_address, hash, tid, coin, 
			toString(px) as px, toString(sz) as sz, side, time,
			toString(start_position) as start_position, direction, 
			toString(closed_pnl) as closed_pnl, oid, crossed,
			toString(fee) as fee, fee_token, trade_date, trade_type, processed_at
		FROM node_fills
		WHERE user_address = lower(?)
		ORDER BY id DESC
		LIMIT 1
	`

	var fill data.UserLatestFill
	var processedAtNullable sql.NullTime
	var userAddr, hash, coin, px, sz, side, startPos, direction, closedPnl, feeToken, tradeDate, tradeType sql.NullString
	var tid, oid sql.NullInt64
	var crossed sql.NullInt32
	var fee sql.NullString

	err := global.GVA_DB_CLICKHOUSE.QueryRowContext(ctx, query, userAddress).Scan(
		&fill.ID,
		&userAddr,
		&hash,
		&tid,
		&coin,
		&px,
		&sz,
		&side,
		&fill.Time,
		&startPos,
		&direction,
		&closedPnl,
		&oid,
		&crossed,
		&fee,
		&feeToken,
		&tradeDate,
		&tradeType,
		&processedAtNullable,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to query user latest fill from ClickHouse: %w", err)
	}

	if userAddr.Valid {
		fill.UserAddress = userAddr.String
	}
	if hash.Valid {
		fill.Hash = hash.String
	}
	if tid.Valid {
		fill.Tid = tid.Int64
	}
	if coin.Valid {
		fill.Coin = coin.String
	}
	if px.Valid {
		fill.Px = px.String
	}
	if sz.Valid {
		fill.Sz = sz.String
	}
	if side.Valid {
		fill.Side = side.String
	}
	if startPos.Valid {
		fill.StartPosition = startPos.String
	}
	if direction.Valid {
		fill.Direction = direction.String
	}
	if closedPnl.Valid {
		fill.ClosedPnl = closedPnl.String
	}
	if oid.Valid {
		fill.Oid = oid.Int64
	}
	if crossed.Valid {
		fill.Crossed = int8(crossed.Int32)
	}
	if fee.Valid {
		fill.Fee = fee.String
	}
	if feeToken.Valid {
		fill.FeeToken = feeToken.String
	}
	if tradeDate.Valid {
		fill.TradeDate = tradeDate.String
	}
	if tradeType.Valid {
		fill.TradeType = tradeType.String
	}
	if processedAtNullable.Valid {
		fill.ProcessedAt = &processedAtNullable.Time
	}

	return &fill, nil
}

// handleUserFillsByTimeRequest handles userFillsByTime type requests
// Queries user fills data from ClickHouse node_fills table based on time range
func (s *InfoService) handleUserFillsByTimeRequest(c *gin.Context, user string, startTime, endTime int64, aggregateByTime bool) error {
	if user == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'user' field for userFillsByTime request",
		})
		return fmt.Errorf("missing user field")
	}

	if startTime <= 0 {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing or invalid 'startTime' field for userFillsByTime request",
		})
		return fmt.Errorf("missing or invalid startTime field")
	}

	ctx := c.Request.Context()

	// Query user fills from ClickHouse
	fills, err := s.getUserFillsByTimeFromClickHouse(ctx, user, startTime, endTime, aggregateByTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to query user fills from ClickHouse",
			zap.String("user", user),
			zap.Int64("startTime", startTime),
			zap.Int64("endTime", endTime),
			zap.Bool("aggregateByTime", aggregateByTime),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, InfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to query user fills: %v", err),
		})
		return err
	}

	// Return the fills data
	c.JSON(http.StatusOK, fills)
	return nil
}

// getUserFillsByTimeFromClickHouse queries user fills from ClickHouse node_fills table
// based on user, startTime, endTime, and optionally aggregates by time
// Returns at most 2000 fills per response and only the 10000 most recent fills are available
func (s *InfoService) getUserFillsByTimeFromClickHouse(ctx context.Context, user string, startTime, endTime int64, aggregateByTime bool) (interface{}, error) {
	if global.GVA_DB_CLICKHOUSE == nil {
		return []interface{}{}, fmt.Errorf("ClickHouse client not initialized")
	}

	const (
		maxAvailableFills = 10000 // Only the 10000 most recent fills are available
		maxResponseFills  = 2000  // Returns at most 2000 fills per response
	)

	var query string
	var args []interface{}

	if aggregateByTime {
		// Aggregate by time (group by time bucket)
		// First, get the 10000 most recent fills, then aggregate
		query = `
			SELECT 
				toStartOfMinute(toDateTime(time / 1000)) as time_bucket,
				coin,
				sum(CAST(sz AS Float64)) as total_sz,
				avg(CAST(px AS Float64)) as avg_px,
				min(CAST(px AS Float64)) as min_px,
				max(CAST(px AS Float64)) as max_px,
				count() as trade_count
			FROM (
				SELECT time, coin, sz, px
				FROM node_fills
				WHERE user_address = lower(?)
		`
		args = []interface{}{user}

		if startTime > 0 {
			query += " AND time >= ?"
			args = append(args, startTime)
		}

		if endTime > 0 {
			query += " AND time <= ?"
			args = append(args, endTime)
		}

		query += `
				ORDER BY time DESC
				LIMIT ?
			)
			GROUP BY time_bucket, coin
			ORDER BY time_bucket ASC, coin ASC
			LIMIT ?
		`
		args = append(args, maxAvailableFills, maxResponseFills)

		rows, err := global.GVA_DB_CLICKHOUSE.QueryContext(ctx, query, args...)
		if err != nil {
			return []map[string]interface{}{}, fmt.Errorf("failed to query aggregated fills: %w", err)
		}
		defer rows.Close()

		results := make([]map[string]interface{}, 0)
		for rows.Next() {
			var timeBucket time.Time
			var coin string
			var totalSz, avgPx, minPx, maxPx float64
			var tradeCount uint64

			if err := rows.Scan(&timeBucket, &coin, &totalSz, &avgPx, &minPx, &maxPx, &tradeCount); err != nil {
				return []map[string]interface{}{}, fmt.Errorf("failed to scan aggregated fill row: %w", err)
			}

			results = append(results, map[string]interface{}{
				"time":        timeBucket.UnixMilli(),
				"coin":        coin,
				"total_sz":    totalSz,
				"avg_px":      avgPx,
				"min_px":      minPx,
				"max_px":      maxPx,
				"trade_count": tradeCount,
			})
		}

		if err := rows.Err(); err != nil {
			return []map[string]interface{}{}, fmt.Errorf("failed to iterate aggregated fills: %w", err)
		}

		// 如果没有数据，results 将是空切片 []，JSON 序列化为 []
		return results, nil
	} else {
		// Return individual fills
		// First, get the 10000 most recent fills, then limit to 2000
		query = `
			SELECT 
				id, user_address, hash, tid, coin, px, sz, side, time,
				start_position, direction, closed_pnl, oid, crossed, fee,
				fee_token, trade_date, trade_type, processed_at
			FROM (
				SELECT 
					id, user_address, hash, tid, coin, px, sz, side, time,
					start_position, direction, closed_pnl, oid, crossed, fee,
					fee_token, trade_date, trade_type, processed_at
				FROM node_fills
				WHERE user_address = lower(?)
		`
		args = []interface{}{user}

		if startTime > 0 {
			query += " AND time >= ?"
			args = append(args, startTime)
		}

		if endTime > 0 {
			query += " AND time <= ?"
			args = append(args, endTime)
		}

		query += `
				ORDER BY time DESC
				LIMIT ?
			)
			ORDER BY time ASC
			LIMIT ?
		`
		args = append(args, maxAvailableFills, maxResponseFills)

		rows, err := global.GVA_DB_CLICKHOUSE.QueryContext(ctx, query, args...)
		if err != nil {
			return []map[string]interface{}{}, fmt.Errorf("failed to query fills: %w", err)
		}
		defer rows.Close()

		results := make([]map[string]interface{}, 0)
		for rows.Next() {
			var fill model.NodeFill
			var processedAt sql.NullTime

			if err := rows.Scan(
				&fill.ID,
				&fill.UserAddress,
				&fill.Hash,
				&fill.TID,
				&fill.Coin,
				&fill.Px,
				&fill.Sz,
				&fill.Side,
				&fill.Time,
				&fill.StartPosition,
				&fill.Direction,
				&fill.ClosedPnl,
				&fill.OID,
				&fill.Crossed,
				&fill.Fee,
				&fill.FeeToken,
				&fill.TradeDate,
				&fill.TradeType,
				&processedAt,
			); err != nil {
				return []map[string]interface{}{}, fmt.Errorf("failed to scan fill row: %w", err)
			}

			if processedAt.Valid {
				fill.ProcessedAt = processedAt.Time
			}

			results = append(results, map[string]interface{}{
				"id":             fill.ID,
				"user_address":   fill.UserAddress,
				"hash":           fill.Hash,
				"tid":            fill.TID,
				"coin":           fill.Coin,
				"px":             fill.Px.String(),
				"sz":             fill.Sz.String(),
				"side":           fill.Side,
				"time":           fill.Time,
				"start_position": fill.StartPosition.String(),
				"direction":      fill.Direction,
				"closed_pnl":     fill.ClosedPnl.String(),
				"oid":            fill.OID,
				"crossed":        fill.Crossed,
				"fee":            fill.Fee.String(),
				"fee_token":      fill.FeeToken,
				"trade_date":     fill.TradeDate,
				"trade_type":     fill.TradeType,
				"processed_at":   fill.ProcessedAt.Unix(),
			})
		}

		if err := rows.Err(); err != nil {
			return []map[string]interface{}{}, fmt.Errorf("failed to iterate fills: %w", err)
		}

		return results, nil
	}
}

// handleHistoricalOrdersRequest handles historicalOrders type requests
func (s *InfoService) handleHistoricalOrdersRequest(c *gin.Context, user string) error {
	if user == "" {
		c.JSON(http.StatusBadRequest, InfoResponse{
			Success: false,
			Message: "Missing 'user' field for historicalOrders request",
		})
		return fmt.Errorf("missing user field")
	}

	ctx := c.Request.Context()

	redisKey := data.GetUserLatestHistoricalOrderRedisKey(user)
	if s.redisClient != nil {
		val, err := s.redisClient.Get(ctx, redisKey).Result()
		if err == nil {
			// Data found in Redis, parse and return
			var orders []data.UserLatestHistoricalOrder
			if err := json.Unmarshal([]byte(val), &orders); err == nil {
				global.GVA_LOG.Debug("Cache hit for historicalOrders request", zap.String("user", user))
				c.JSON(http.StatusOK, orders)
				return nil
			} else {
				global.GVA_LOG.Warn("Failed to unmarshal Redis data for historicalOrders",
					zap.String("user", user),
					zap.Error(err))
			}
		} else if err != redis.Nil {
			global.GVA_LOG.Warn("Failed to get data from Redis for historicalOrders",
				zap.String("user", user),
				zap.Error(err))
		}
	}

	// Cache miss, query Hyperliquid API
	global.GVA_LOG.Debug("Cache miss for historicalOrders request, fetching from Hyperliquid API", zap.String("user", user))

	req := InfoRequest{
		Type: "historicalOrders",
		User: user,
	}

	requestBody := s.buildRequestBody(req)
	responseBody, err := s.sendHTTPRequestWithProxyAndRateLimit(c, requestBody, "historicalOrders", 0, 0)
	if err != nil {
		return err
	}

	// Parse response and check if data exists
	var apiOrders []interface{}
	if err := json.Unmarshal(responseBody, &apiOrders); err == nil && len(apiOrders) > 0 {
		// Hyperliquid API has data, cache to Redis for 5 seconds
		if s.redisClient != nil {
			if err := s.redisClient.Set(ctx, redisKey, string(responseBody), data.HistoricalOrdersCacheTTL).Err(); err != nil {
				global.GVA_LOG.Warn("Failed to cache historicalOrders response to Redis",
					zap.String("user", user),
					zap.Error(err))
			} else {
				global.GVA_LOG.Debug("Cached historicalOrders response to Redis",
					zap.String("user", user),
					zap.Duration("ttl", data.HistoricalOrdersCacheTTL))
			}
		}
		// Return response
		c.Data(http.StatusOK, "application/json", responseBody)
		return nil
	}

	// No data, return empty array
	c.JSON(http.StatusOK, []interface{}{})
	return nil
}
