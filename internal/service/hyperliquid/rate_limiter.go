package hyperliquid

import (
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// Rate limiting weight enumeration
const (
	// Info API request weights
	WeightStandardInfo    = 1  // Standard info request weight
	WeightCommonInfo      = 20 // Most info request weight
	WeightSpecialInfo     = 2  // Special info request weight (l2Book, allMids, clearinghouseState, orderStatus, spotClearinghouseState, exchangeStatus)
	WeightUserRoleInfo    = 60 // userRole request weight
	WeightExtraPer20Items = 1  // Extra weight per 20 returned items
	WeightExtraPer60Items = 1  // Extra weight per 60 returned items for candleSnapshot
	WeightExplorerAPI     = 40 // Explorer API weight
	WeightPerBlock        = 1  // Extra weight per block for blockList

	// Overall rate limiting
	MaxRESTWeightPerMinute = 1200 // Maximum aggregated weight limit per minute for REST requests

	// WebSocket limits
	MaxWebSocketConnections      = 100  // Maximum 100 websocket connections
	MaxWebSocketSubscriptions    = 1000 // Maximum 1000 websocket subscriptions
	MaxUniqueUsersPerSub         = 10   // User-specific websocket subscriptions can accommodate up to 10 unique users
	MaxWebSocketMessagesPerMin   = 2000 // Maximum 2000 messages sent to Hyperliquid per minute
	MaxInFlightWebSocketMessages = 100  // Maximum 100 in-flight messages across all websocket connections

	// EVM JSON-RPC limits
	MaxEVMRequestsPerMinute = 100 // Maximum 100 EVM JSON-RPC requests per minute

	// Address-based limits
	InitialRequestBuffer = 10000    // Initial buffer capacity of 10,000 requests per address
	RequestPerSecond     = 1.0 / 10 // One request allowed per address every 10 seconds
	VolumeRatio          = 0.01     // 0.01 requests per 1 USDC traded (1%)
	CancelBonus          = 100000   // Additional capacity for cancel operations
	CancelMultiplier     = 2        // Multiplier limit for cancel operations

	// Order limits
	DefaultOpenOrdersLimit  = 1000              // Default open order limit of 1000 orders
	VolumePerOrderIncrement = 5000000 * 1000000 // Additional order per 5 million USDC traded (USDC has 6 decimal precision)
	MaxOpenOrdersLimit      = 5000              // Total open order limit of 5000 orders
	ReductionOnlyThreshold  = 1000              // When this threshold is reached, only reduction orders or scheduled orders will be rejected
	StressedOrderLimitRatio = 2                 // During congestion, address usage is limited to twice its order share
)

// InfoRequestType defines info request types
type InfoRequestType string

const (
	RequestTypeL2Book                 InfoRequestType = "l2Book"
	RequestTypeAllMids                InfoRequestType = "allMids"
	RequestTypeClearinghouseState     InfoRequestType = "clearinghouseState"
	RequestTypeOrderStatus            InfoRequestType = "orderStatus"
	RequestTypeSpotClearinghouseState InfoRequestType = "spotClearinghouseState"
	RequestTypeExchangeStatus         InfoRequestType = "exchangeStatus"
	RequestTypeUserRole               InfoRequestType = "userRole"
	RequestTypeCandleSnapshot         InfoRequestType = "candleSnapshot"

	// Requests that need to be weighted by number of returned items
	RequestTypeRecentTrades          InfoRequestType = "recentTrades"
	RequestTypeHistoricalOrders      InfoRequestType = "historicalOrders"
	RequestTypeUserFills             InfoRequestType = "userFills"
	RequestTypeUserFillsByTime       InfoRequestType = "userFillsByTime"
	RequestTypeFundingHistory        InfoRequestType = "fundingHistory"
	RequestTypeUserFunding           InfoRequestType = "userFunding"
	RequestTypeNonUserFundingUpdates InfoRequestType = "nonUserFundingUpdates"
	RequestTypeTwapHistory           InfoRequestType = "twapHistory"
	RequestTypeUserTwapSlice         InfoRequestType = "userTwapSlice"
	RequestTypeFills                 InfoRequestType = "fills"
	RequestTypeFillsByTime           InfoRequestType = "fillsByTime"
	RequestTypeDelegatorHistory      InfoRequestType = "delegatorHistory"
	RequestTypeDelegatorRewards      InfoRequestType = "delegatorRewards"
	RequestTypeValidatorStats        InfoRequestType = "validatorStats"
)

// RestRateLimiter REST API rate limiter
type RestRateLimiter struct {
	currentWeight int64
	windowStart   int64
	mu            sync.Mutex
}

// NewRestRateLimiter creates a new REST rate limiter
func NewRestRateLimiter() *RestRateLimiter {
	return &RestRateLimiter{
		currentWeight: 0,
		windowStart:   time.Now().Unix(),
	}
}

// CalculateWeight calculates the weight of a request
func (rl *RestRateLimiter) CalculateWeight(requestType InfoRequestType, batchLength int, resultItemCount int) int {
	// Adjust weight based on request type
	switch requestType {
	case RequestTypeL2Book, RequestTypeAllMids, RequestTypeClearinghouseState,
		RequestTypeOrderStatus, RequestTypeSpotClearinghouseState, RequestTypeExchangeStatus:
		return WeightSpecialInfo

	case RequestTypeUserRole:
		return WeightUserRoleInfo

	case RequestTypeCandleSnapshot:
		// Add 1 weight for every 60 returned items
		return WeightCommonInfo + (resultItemCount/60)*WeightExtraPer60Items

	// Requests that add 1 weight for every 20 returned items
	case RequestTypeRecentTrades, RequestTypeHistoricalOrders, RequestTypeUserFills,
		RequestTypeUserFillsByTime, RequestTypeFundingHistory, RequestTypeUserFunding,
		RequestTypeNonUserFundingUpdates, RequestTypeTwapHistory, RequestTypeUserTwapSlice,
		RequestTypeFills, RequestTypeFillsByTime, RequestTypeDelegatorHistory,
		RequestTypeDelegatorRewards, RequestTypeValidatorStats:
		return WeightCommonInfo + (resultItemCount/20)*WeightExtraPer20Items

	default:
		// Other recorded info requests
		return WeightCommonInfo
	}
}

// CanMakeRequest checks if a request can be made
func (rl *RestRateLimiter) CanMakeRequest(requestType InfoRequestType, batchLength int, resultItemCount int) bool {
	weight := rl.CalculateWeight(requestType, batchLength, resultItemCount)
	return rl.CanMakeRequestWithWeight(weight)
}

// CanMakeRequestWithWeight checks if a request with specified weight can be made
func (rl *RestRateLimiter) CanMakeRequestWithWeight(weight int) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Check if entering a new time window (resets every minute)
	if now-rl.windowStart >= 60 {
		rl.currentWeight = 0
		rl.windowStart = now
	}

	// Check if current weight plus new request weight exceeds the limit
	return atomic.LoadInt64(&rl.currentWeight)+int64(weight) <= MaxRESTWeightPerMinute
}

// RecordRequest records a request that has been made
func (rl *RestRateLimiter) RecordRequest(requestType InfoRequestType, batchLength int, resultItemCount int) {
	weight := rl.CalculateWeight(requestType, batchLength, resultItemCount)
	rl.RecordRequestWithWeight(weight)
}

// RecordRequestWithWeight records a request that has been made (with weight)
func (rl *RestRateLimiter) RecordRequestWithWeight(weight int) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Check if entering a new time window
	if now-rl.windowStart >= 60 {
		rl.currentWeight = 0
		rl.windowStart = now
	}

	atomic.AddInt64(&rl.currentWeight, int64(weight))

	// Log the request
	if global.GVA_LOG != nil {
		global.GVA_LOG.Debug("Recorded REST API request",
			zap.Int("weight", weight),
			zap.Int64("current_weight", atomic.LoadInt64(&rl.currentWeight)),
			zap.Int("max_weight_per_min", MaxRESTWeightPerMinute))
	}
}

// GetCurrentWeight gets the weight used in the current window
func (rl *RestRateLimiter) GetCurrentWeight() int64 {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()
	if now-rl.windowStart >= 60 {
		return 0
	}

	return atomic.LoadInt64(&rl.currentWeight)
}

// GetRemainingWeight gets the remaining available weight
func (rl *RestRateLimiter) GetRemainingWeight() int64 {
	return MaxRESTWeightPerMinute - rl.GetCurrentWeight()
}

// AddressRateLimiter address-based rate limiter
type AddressRateLimiter struct {
	// Address trading history
	tradingVolume     int64 // Cumulative trading volume (USDC, 6 decimal precision)
	availableRequests int64 // Available request count
	lastRequestTime   int64 // Last request time

	// Open order information
	openOrdersCount int64 // Current open order count

	mu sync.Mutex
}

// NewAddressRateLimiter creates a new address rate limiter
func NewAddressRateLimiter() *AddressRateLimiter {
	return &AddressRateLimiter{
		tradingVolume:     0,
		availableRequests: InitialRequestBuffer,
		lastRequestTime:   0,
		openOrdersCount:   0,
	}
}

// RecordTrade records a trade
func (rl *AddressRateLimiter) RecordTrade(tradeValueUsdc float64) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// Calculate new request quota (trade value * 1% / 1000000 converted to unitless quota)
	// Example: 100 USDC * 0.01 = 1 quota
	requestQuota := int64(tradeValueUsdc * VolumeRatio * 1000000)

	rl.tradingVolume += int64(tradeValueUsdc * 1000000)
	rl.availableRequests += requestQuota

	if global.GVA_LOG != nil {
		global.GVA_LOG.Debug("Recorded trade for address rate limiter",
			zap.Float64("trade_value_usdc", tradeValueUsdc),
			zap.Int64("request_quota_added", requestQuota),
			zap.Int64("available_requests", rl.availableRequests))
	}
}

// CanMakeRequest checks if an operation request can be made (non-info request)
func (rl *AddressRateLimiter) CanMakeRequest(isCancel bool) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Check per-second request limit
	if rl.lastRequestTime > 0 && now-rl.lastRequestTime < 10 {
		return false
	}

	// Calculate current limit
	limit := rl.availableRequests
	if isCancel {
		// Additional capacity for cancel operations
		limit = min(limit+CancelBonus, limit*CancelMultiplier)
	}

	return limit > 0
}

// RecordRequest records an operation request that has been made
func (rl *AddressRateLimiter) RecordRequest(isCancel bool) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Update time window
	rl.lastRequestTime = now

	// Decrease available request count
	limit := rl.availableRequests
	if isCancel {
		// Cancel operations have additional capacity
		effectiveLimit := min(limit+CancelBonus, limit*CancelMultiplier)
		if effectiveLimit > limit {
			// Don't reduce quota, use additional capacity
			if global.GVA_LOG != nil {
				global.GVA_LOG.Debug("Used cancel bonus capacity",
					zap.Int64("original_limit", limit),
					zap.Int64("effective_limit", effectiveLimit))
			}
			return
		}
	}

	if rl.availableRequests > 0 {
		atomic.AddInt64(&rl.availableRequests, -1)
	}

	if global.GVA_LOG != nil {
		global.GVA_LOG.Debug("Recorded address operation request",
			zap.Bool("is_cancel", isCancel),
			zap.Int64("remaining_requests", rl.availableRequests),
			zap.Int64("trading_volume_usdc", rl.tradingVolume/1000000))
	}
}

// GetAvailableRequests gets the available request count
func (rl *AddressRateLimiter) GetAvailableRequests() int64 {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	return rl.availableRequests
}

// SetOpenOrdersCount sets the open order count
func (rl *AddressRateLimiter) SetOpenOrdersCount(count int64) {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	rl.openOrdersCount = count
}

// GetOpenOrdersLimit gets the open order limit
func (rl *AddressRateLimiter) GetOpenOrdersLimit() int64 {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// Default 1000 + 1 additional order per 5 million USDC trading volume
	volumeIncrement := rl.tradingVolume / VolumePerOrderIncrement
	limit := DefaultOpenOrdersLimit + volumeIncrement

	// Not exceeding 5000
	if limit > MaxOpenOrdersLimit {
		limit = MaxOpenOrdersLimit
	}

	return limit
}

// CanPlaceOrder checks if a new order can be placed
func (rl *AddressRateLimiter) CanPlaceOrder(orderValueUsdc float64, isReductionOnly bool, isScheduledOrder bool) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	limit := rl.GetOpenOrdersLimit()

	// Check open order count
	if rl.openOrdersCount >= limit {
		return false
	}

	// Special restriction: when there are at least 1000 open orders
	if rl.openOrdersCount >= ReductionOnlyThreshold {
		if isReductionOnly || isScheduledOrder {
			return false
		}
	}

	// Peak congestion period: calculate current available order share
	// During congestion, limit to twice the order share
	// Simplified handling here, returns true, should be dynamically adjusted based on network congestion
	return true
}

// RecordOrderPlaced records a placed order
func (rl *AddressRateLimiter) RecordOrderPlaced(orderValueUsdc float64) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	atomic.AddInt64(&rl.openOrdersCount, 1)

	// Also record the trade (this will increase available request quota)
	rl.RecordTrade(orderValueUsdc)
}

// WebSocketLimiter WebSocket connection limiter
type WebSocketLimiter struct {
	activeConnections   int64
	activeSubscriptions int64
	uniqueUsers         map[string]bool
	messagesLastMin     int64
	inFlightMessages    int64

	mu                sync.Mutex
	messageTimestamps []int64 // Used to track messages from the last minute
}

// NewWebSocketLimiter creates a new WebSocket limiter
func NewWebSocketLimiter() *WebSocketLimiter {
	return &WebSocketLimiter{
		uniqueUsers:       make(map[string]bool),
		messageTimestamps: make([]int64, 0, MaxWebSocketMessagesPerMin),
	}
}

// AddConnection adds a WebSocket connection
func (wl *WebSocketLimiter) AddConnection() bool {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	if atomic.LoadInt64(&wl.activeConnections) >= MaxWebSocketConnections {
		return false
	}

	atomic.AddInt64(&wl.activeConnections, 1)
	return true
}

// RemoveConnection removes a WebSocket connection
func (wl *WebSocketLimiter) RemoveConnection() {
	atomic.AddInt64(&wl.activeConnections, -1)
}

// AddSubscription adds a subscription
func (wl *WebSocketLimiter) AddSubscription(user string) bool {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	// Check total subscription count
	if len(wl.uniqueUsers)+int(atomic.LoadInt64(&wl.activeSubscriptions)) >= MaxWebSocketSubscriptions {
		return false
	}

	// If it's a user-specific subscription
	if user != "" {
		if len(wl.uniqueUsers) >= MaxUniqueUsersPerSub && !wl.uniqueUsers[user] {
			return false
		}
		wl.uniqueUsers[user] = true
	}

	atomic.AddInt64(&wl.activeSubscriptions, 1)
	return true
}

// RemoveSubscription removes a subscription
func (wl *WebSocketLimiter) RemoveSubscription(user string) {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	if user != "" {
		delete(wl.uniqueUsers, user)
	}

	if atomic.LoadInt64(&wl.activeSubscriptions) > 0 {
		atomic.AddInt64(&wl.activeSubscriptions, -1)
	}
}

// CanSendMessage checks if a message can be sent
func (wl *WebSocketLimiter) CanSendMessage() bool {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	now := time.Now().Unix()

	// Clean up message records older than one minute
	wl.messageTimestamps = wl.filterRecentMessages(wl.messageTimestamps, now)

	// Check per-minute message limit
	if int64(len(wl.messageTimestamps)) >= MaxWebSocketMessagesPerMin {
		return false
	}

	// Check in-flight message limit
	if atomic.LoadInt64(&wl.inFlightMessages) >= MaxInFlightWebSocketMessages {
		return false
	}

	return true
}

// RecordMessage records a sent message
func (wl *WebSocketLimiter) RecordMessage() {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	now := time.Now().Unix()
	wl.messageTimestamps = append(wl.messageTimestamps, now)

	// Keep array size within reasonable range
	if len(wl.messageTimestamps) > MaxWebSocketMessagesPerMin*2 {
		wl.messageTimestamps = wl.messageTimestamps[len(wl.messageTimestamps)-MaxWebSocketMessagesPerMin:]
	}

	atomic.AddInt64(&wl.inFlightMessages, 1)
}

// RecordMessageResponse records a message that received a response
func (wl *WebSocketLimiter) RecordMessageResponse() {
	if atomic.LoadInt64(&wl.inFlightMessages) > 0 {
		atomic.AddInt64(&wl.inFlightMessages, -1)
	}
}

// filterRecentMessages filters messages from the last minute
func (wl *WebSocketLimiter) filterRecentMessages(timestamps []int64, now int64) []int64 {
	result := make([]int64, 0, len(timestamps))
	for _, ts := range timestamps {
		if now-ts < 60 {
			result = append(result, ts)
		}
	}
	return result
}

// GetCurrentMetrics gets current metrics
func (wl *WebSocketLimiter) GetCurrentMetrics() map[string]interface{} {
	wl.mu.Lock()
	defer wl.mu.Unlock()

	now := time.Now().Unix()
	wl.messageTimestamps = wl.filterRecentMessages(wl.messageTimestamps, now)

	return map[string]interface{}{
		"active_connections":   atomic.LoadInt64(&wl.activeConnections),
		"max_connections":      MaxWebSocketConnections,
		"active_subscriptions": atomic.LoadInt64(&wl.activeSubscriptions),
		"max_subscriptions":    MaxWebSocketSubscriptions,
		"unique_users":         len(wl.uniqueUsers),
		"max_unique_users":     MaxUniqueUsersPerSub,
		"messages_last_min":    len(wl.messageTimestamps),
		"max_messages_per_min": MaxWebSocketMessagesPerMin,
		"in_flight_messages":   atomic.LoadInt64(&wl.inFlightMessages),
		"max_in_flight":        MaxInFlightWebSocketMessages,
	}
}

// EVMRateLimiter EVM JSON-RPC rate limiter
type EVMRateLimiter struct {
	requestCount int64
	windowStart  int64
	mu           sync.Mutex
}

// NewEVMRateLimiter creates a new EVM rate limiter
func NewEVMRateLimiter() *EVMRateLimiter {
	return &EVMRateLimiter{
		requestCount: 0,
		windowStart:  time.Now().Unix(),
	}
}

// CanMakeRequest checks if a request can be made
func (rl *EVMRateLimiter) CanMakeRequest() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Check if entering a new time window (resets every minute)
	if now-rl.windowStart >= 60 {
		rl.requestCount = 0
		rl.windowStart = now
	}

	return rl.requestCount < MaxEVMRequestsPerMinute
}

// RecordRequest records a request that has been made
func (rl *EVMRateLimiter) RecordRequest() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now().Unix()

	// Check if entering a new time window
	if now-rl.windowStart >= 60 {
		rl.requestCount = 0
		rl.windowStart = now
	}

	rl.requestCount++

	if global.GVA_LOG != nil {
		global.GVA_LOG.Debug("Recorded EVM JSON-RPC request",
			zap.Int64("request_count", rl.requestCount),
			zap.Int("max_requests_per_min", MaxEVMRequestsPerMinute))
	}
}

// CalculateOptimalRequestRate calculates how many requests should be made per minute
// This is a helper method to help users understand best practices
func CalculateOptimalRequestRate(requestType InfoRequestType, batchLength int, estimatedResultCount int) (requestsPerMinute int, description string) {
	limiter := NewRestRateLimiter()
	weight := limiter.CalculateWeight(requestType, batchLength, estimatedResultCount)

	// Calculate how many such requests can be made per minute without exceeding the limit
	requestsPerMinute = MaxRESTWeightPerMinute / weight
	if requestsPerMinute > MaxRESTWeightPerMinute {
		requestsPerMinute = MaxRESTWeightPerMinute
	}

	description = buildRequestRateDescription(requestType, batchLength, estimatedResultCount, weight, requestsPerMinute)

	return requestsPerMinute, description
}

// buildRequestRateDescription builds request rate description
func buildRequestRateDescription(requestType InfoRequestType, batchLength int, resultCount int, weight int, requestsPerMinute int) string {
	var desc string

	switch requestType {
	case RequestTypeL2Book, RequestTypeAllMids, RequestTypeClearinghouseState,
		RequestTypeOrderStatus, RequestTypeSpotClearinghouseState, RequestTypeExchangeStatus:
		desc = "Special Info request (weight 2)"
	case RequestTypeUserRole:
		desc = "UserRole request (weight 60)"
	case RequestTypeCandleSnapshot:
		desc = "CandleSnapshot request (weight 20 + 1 extra weight per 60 items)"
	case RequestTypeRecentTrades, RequestTypeHistoricalOrders, RequestTypeUserFills,
		RequestTypeUserFillsByTime, RequestTypeFundingHistory, RequestTypeUserFunding,
		RequestTypeNonUserFundingUpdates, RequestTypeTwapHistory, RequestTypeUserTwapSlice,
		RequestTypeFills, RequestTypeFillsByTime, RequestTypeDelegatorHistory,
		RequestTypeDelegatorRewards, RequestTypeValidatorStats:
		desc = "Item count weighted request (weight 20 + 1 extra weight per 20 items)"
	default:
		desc = "Standard Info request"
	}

	if resultCount > 0 {
		desc += ", estimated result count: " + strconv.Itoa(resultCount)
	}

	desc += ", weight: " + strconv.Itoa(weight)
	desc += ", recommended requests per minute: " + strconv.Itoa(requestsPerMinute)

	return desc
}

// helper function
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}
