package hyperliquid

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// ExampleUsage demonstrates how to use the rate limiter
func ExampleUsage() {
	// 1. REST API rate limiting example
	restLimiter := NewRestRateLimiter()

	// Check if we can make an allMids request (weight 2)
	requestType := RequestTypeAllMids
	batchLength := 0
	resultItemCount := 0

	weight := restLimiter.CalculateWeight(requestType, batchLength, resultItemCount)
	fmt.Printf("AllMids request weight: %d\n", weight)
	fmt.Printf("Remaining available weight: %d\n", restLimiter.GetRemainingWeight())

	if restLimiter.CanMakeRequest(requestType, batchLength, resultItemCount) {
		fmt.Println("Can make request")
		restLimiter.RecordRequest(requestType, batchLength, resultItemCount)
		fmt.Printf("Remaining weight after request: %d\n", restLimiter.GetRemainingWeight())
	}

	// 2. Calculate optimal request rate
	optimalRate, description := CalculateOptimalRequestRate(RequestTypeAllMids, 0, 0)
	fmt.Printf("Optimal request rate: %s\n", description)
	fmt.Printf("Can request AllMids %d times per minute\n", optimalRate)

	optimalRate2, description2 := CalculateOptimalRequestRate(RequestTypeUserRole, 0, 0)
	fmt.Printf("Optimal request rate: %s\n", description2)
	fmt.Printf("Can request UserRole %d times per minute\n", optimalRate2)

	// 3. Address-based rate limiting example
	addressLimiter := NewAddressRateLimiter()

	// Record some trades
	addressLimiter.RecordTrade(100.0) // 100 USDC trade, gets 1 request quota
	addressLimiter.RecordTrade(500.0) // 500 USDC trade, gets 5 request quotas

	fmt.Printf("Available requests (including initial buffer): %d\n", addressLimiter.GetAvailableRequests())

	// Check if we can submit a regular order
	if addressLimiter.CanMakeRequest(false) {
		fmt.Println("Can submit regular order")
		addressLimiter.RecordRequest(false)
		fmt.Printf("Remaining requests after order submission: %d\n", addressLimiter.GetAvailableRequests())
	}

	// Check if we can cancel an order (has additional capacity)
	if addressLimiter.CanMakeRequest(true) {
		fmt.Println("Can cancel order")
		addressLimiter.RecordRequest(true)
		fmt.Printf("Remaining requests after order cancellation: %d\n", addressLimiter.GetAvailableRequests())
	}

	// 4. Check open orders limit
	limit := addressLimiter.GetOpenOrdersLimit()
	fmt.Printf("Current open orders limit: %d\n", limit)

	// Set current open orders count
	addressLimiter.SetOpenOrdersCount(950)

	// Check if we can place a new order
	if addressLimiter.CanPlaceOrder(100.0, false, false) {
		fmt.Println("Can place new order")
		addressLimiter.RecordOrderPlaced(100.0)
	}

	// 5. WebSocket limiting example
	wsLimiter := NewWebSocketLimiter()

	// Add connections
	for i := 0; i < 5; i++ {
		if wsLimiter.AddConnection() {
			fmt.Printf("Added WebSocket connection %d\n", i+1)
		}
	}

	// Add subscriptions
	for i := 0; i < 3; i++ {
		user := fmt.Sprintf("user%d", i)
		if wsLimiter.AddSubscription(user) {
			fmt.Printf("Added subscription for user %s\n", user)
		}
	}

	// Send messages
	messageCount := 0
	for i := 0; i < 10; i++ {
		if wsLimiter.CanSendMessage() {
			wsLimiter.RecordMessage()
			messageCount++
			// Simulate receiving response
			wsLimiter.RecordMessageResponse()
		}
	}
	fmt.Printf("Sent %d messages\n", messageCount)

	// Get current metrics
	metrics := wsLimiter.GetCurrentMetrics()
	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("WebSocket metrics", zap.Any("metrics", metrics))
	}

	// 6. EVM JSON-RPC limiting example
	evmLimiter := NewEVMRateLimiter()

	rpcCount := 0
	for i := 0; i < 5; i++ {
		if evmLimiter.CanMakeRequest() {
			evmLimiter.RecordRequest()
			rpcCount++
		}
	}
	fmt.Printf("Made %d EVM RPC requests\n", rpcCount)
}

// ExampleConcurrentUsage demonstrates usage in concurrent environment
func ExampleConcurrentUsage(ctx context.Context) {
	restLimiter := NewRestRateLimiter()

	// Simulate concurrent requests
	done := make(chan bool)

	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 10; j++ {
				select {
				case <-ctx.Done():
					return
				default:
					if restLimiter.CanMakeRequest(RequestTypeAllMids, 0, 0) {
						restLimiter.RecordRequest(RequestTypeAllMids, 0, 0)
						if global.GVA_LOG != nil {
							global.GVA_LOG.Info("Concurrent request made",
								zap.Int("goroutine", id),
								zap.Int("request", j))
						}
					}
					time.Sleep(100 * time.Millisecond)
				}
			}
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	fmt.Printf("Final remaining weight: %d\n", restLimiter.GetRemainingWeight())
}
