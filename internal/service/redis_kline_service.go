package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// RedisKline represents a candlestick stored in Redis
type RedisKline struct {
	Timestamp       int64   `json:"timestamp"` // Unix timestamp in milliseconds (candle start time)
	Symbol          string  `json:"symbol"`
	Open            float64 `json:"open"`
	High            float64 `json:"high"`
	Low             float64 `json:"low"`
	Close           float64 `json:"close"`
	Volume          float64 `json:"volume"`
	TradesCount     int64   `json:"trades_count"`
	Timeframe       string  `json:"timeframe"`
	OpenIndex       int     `json:"open_index"`        // Index of the trade that set the Open price (for backward compatibility)
	CloseIndex      int     `json:"close_index"`       // Index of the trade that set the Close price (for backward compatibility)
	OpenBatchIndex  int     `json:"open_batch_index"`  // Batch index of the trade that set the Open price
	CloseBatchIndex int     `json:"close_batch_index"` // Batch index of the trade that set the Close price
	OpenTimestamp   int64   `json:"open_timestamp"`    // Timestamp of the trade that set the Open price
	CloseTimestamp  int64   `json:"close_timestamp"`   // Timestamp of the trade that set the Close price
}

// TimeframeTTLConfig defines TTL and max candles for each timeframe
type TimeframeTTLConfig struct {
	TTL        time.Duration
	MaxCandles int
}

// RedisKlineService handles Redis operations for real-time kline data
type RedisKlineService struct {
	client     *redis.Client
	ttlConfigs map[model.KlineTimeframe]TimeframeTTLConfig
}

// GetTTLConfig returns the TTL configuration for a given timeframe
func (r *RedisKlineService) GetTTLConfig(timeframe model.KlineTimeframe) (TimeframeTTLConfig, bool) {
	config, exists := r.ttlConfigs[timeframe]
	return config, exists
}

// GetAllTTLConfigs returns all TTL configurations
func (r *RedisKlineService) GetAllTTLConfigs() map[model.KlineTimeframe]TimeframeTTLConfig {
	// Return a copy to prevent external modification
	configs := make(map[model.KlineTimeframe]TimeframeTTLConfig)
	for k, v := range r.ttlConfigs {
		configs[k] = v
	}
	return configs
}

// VerifyTTL checks the actual TTL of a Redis key for debugging purposes
func (r *RedisKlineService) VerifyTTL(ctx context.Context, symbol string, timeframe model.KlineTimeframe) (time.Duration, error) {
	key := r.getRedisKey(symbol, timeframe)
	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get TTL for key %s: %w", key, err)
	}
	return ttl, nil
}

// NewRedisKlineService creates a new Redis kline service
func NewRedisKlineService() *RedisKlineService {
	// Define TTL configurations as per requirements
	ttlConfigs := map[model.KlineTimeframe]TimeframeTTLConfig{
		model.Timeframe1m:  {TTL: 5 * time.Minute, MaxCandles: 5},
		model.Timeframe3m:  {TTL: 9 * time.Minute, MaxCandles: 3},
		model.Timeframe5m:  {TTL: 15 * time.Minute, MaxCandles: 3},
		model.Timeframe15m: {TTL: 30 * time.Minute, MaxCandles: 2},
		model.Timeframe30m: {TTL: 1 * time.Hour, MaxCandles: 2},
		model.Timeframe1h:  {TTL: 2 * time.Hour, MaxCandles: 2},
		model.Timeframe2h:  {TTL: 4 * time.Hour, MaxCandles: 2},
		model.Timeframe4h:  {TTL: 8 * time.Hour, MaxCandles: 2},
		model.Timeframe8h:  {TTL: 16 * time.Hour, MaxCandles: 2},
		model.Timeframe12h: {TTL: 24 * time.Hour, MaxCandles: 2},
		model.Timeframe1d:  {TTL: 48 * time.Hour, MaxCandles: 2},
		model.Timeframe3d:  {TTL: 144 * time.Hour, MaxCandles: 2},  // 6 days
		model.Timeframe1w:  {TTL: 336 * time.Hour, MaxCandles: 2},  // 14 days
		model.Timeframe1mo: {TTL: 1440 * time.Hour, MaxCandles: 2}, // 60 days
	}

	return &RedisKlineService{
		client:     global.GVA_REDIS,
		ttlConfigs: ttlConfigs,
	}
}

// getRedisKey generates Redis key for kline data
func (r *RedisKlineService) getRedisKey(symbol string, timeframe model.KlineTimeframe) string {
	return fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))
}

// UpdateKline updates or creates a kline in Redis using sorted sets
func (r *RedisKlineService) UpdateKline(ctx context.Context, symbol string, timeframe model.KlineTimeframe, timestamp time.Time, price, volume float64) error {
	key := r.getRedisKey(symbol, timeframe)
	bucketTime := timeframe.TruncateTime(timestamp)
	score := float64(bucketTime.UnixMilli()) // Use milliseconds as score for sorting

	// Get existing kline data
	existingData, err := r.client.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: fmt.Sprintf("%f", score),
		Max: fmt.Sprintf("%f", score),
	}).Result()

	var kline RedisKline
	tradeTimestamp := timestamp.UnixMilli()

	if len(existingData) > 0 {
		// Update existing kline
		if err := json.Unmarshal([]byte(existingData[0]), &kline); err != nil {
			return fmt.Errorf("failed to unmarshal existing kline: %w", err)
		}

		// Update High
		if price > kline.High {
			kline.High = price
		}

		// Update Low
		if price < kline.Low {
			kline.Low = price
		}

		// Update Open if this trade is earlier than current open
		// Use timestamp for comparison, with index as tiebreaker (default to 0 for single trades)
		if tradeTimestamp < kline.OpenTimestamp ||
			(tradeTimestamp == kline.OpenTimestamp && 0 < kline.OpenIndex) {
			kline.Open = price
			kline.OpenIndex = 0
			kline.OpenTimestamp = tradeTimestamp
		}

		// Update Close if this trade is later than current close
		// Use timestamp for comparison, with index as tiebreaker (default to 0 for single trades)
		if tradeTimestamp > kline.CloseTimestamp ||
			(tradeTimestamp == kline.CloseTimestamp && 0 >= kline.CloseIndex) {
			kline.Close = price
			kline.CloseIndex = 0
			kline.CloseTimestamp = tradeTimestamp
		}

		// Update Volume and Count
		kline.Volume += volume
		kline.TradesCount++
	} else {
		// Create new kline
		timestampMilli := bucketTime.UnixMilli()
		kline = RedisKline{
			Timestamp:       timestampMilli,
			Symbol:          symbol,
			Open:            price,
			High:            price,
			Low:             price,
			Close:           price,
			Volume:          volume,
			TradesCount:     1,
			Timeframe:       string(timeframe),
			OpenIndex:       0, // Default index for backward compatibility
			CloseIndex:      0, // Default index for backward compatibility
			OpenBatchIndex:  0, // Default batch index for backward compatibility
			CloseBatchIndex: 0, // Default batch index for backward compatibility
			OpenTimestamp:   tradeTimestamp,
			CloseTimestamp:  tradeTimestamp,
		}
	}

	// Serialize kline data
	klineData, err := json.Marshal(kline)
	if err != nil {
		return fmt.Errorf("failed to marshal kline data: %w", err)
	}

	// Use pipeline for atomic operations
	pipe := r.client.Pipeline()

	// If updating existing kline, remove the old entry first to prevent duplicates
	if len(existingData) > 0 {
		pipe.ZRemRangeByScore(ctx, key, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score))
	}

	// Add/update the kline in sorted set
	pipe.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: string(klineData),
	})

	// Set TTL for the key
	ttlConfig := r.ttlConfigs[timeframe]
	pipe.Expire(ctx, key, ttlConfig.TTL)

	// Trim to keep only the specified number of candles (keep the most recent MaxCandles)
	// ZRemRangeByRank removes elements from start to stop rank
	// To keep last N elements, remove from rank 0 to rank -(N+1)
	pipe.ZRemRangeByRank(ctx, key, 0, int64(-ttlConfig.MaxCandles-1))

	// Execute pipeline
	results, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute Redis pipeline: %w", err)
	}

	// Verify TTL was set correctly (for debugging)
	for i, result := range results {
		if result.Err() != nil {
			global.GVA_LOG.Warn("Pipeline command failed",
				zap.Int("command_index", i),
				zap.String("key", key),
				zap.Error(result.Err()))
		}
	}

	global.GVA_LOG.Debug("Updated kline in Redis",
		zap.String("symbol", symbol),
		// time
		zap.Int64("timestamp", kline.Timestamp),
		zap.String("timeframe", string(timeframe)),
		zap.Time("timestamp", bucketTime),
		zap.Float64("price", price),
		zap.Float64("volume", volume))

	return nil
}

// GetKlines retrieves klines from Redis for a specific symbol and timeframe
func (r *RedisKlineService) GetKlines(ctx context.Context, symbol string, timeframe model.KlineTimeframe, limit int) ([]RedisKline, error) {
	key := r.getRedisKey(symbol, timeframe)

	// Get klines in descending order (most recent first)
	var results []string
	var err error

	if limit > 0 {
		results, err = r.client.ZRevRange(ctx, key, 0, int64(limit-1)).Result()
	} else {
		results, err = r.client.ZRevRange(ctx, key, 0, -1).Result()
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get klines from Redis: %w", err)
	}

	klines := make([]RedisKline, 0, len(results))
	for _, result := range results {
		var kline RedisKline
		if err := json.Unmarshal([]byte(result), &kline); err != nil {
			global.GVA_LOG.Error("Failed to unmarshal kline from Redis", zap.Error(err))
			continue
		}
		klines = append(klines, kline)
	}

	return klines, nil
}

// GetCompletedKlines retrieves completed klines (not the currently forming one) for ClickHouse persistence
func (r *RedisKlineService) GetCompletedKlines(ctx context.Context, symbol string, timeframe model.KlineTimeframe) ([]RedisKline, error) {
	now := time.Now()
	currentBucket := timeframe.TruncateTime(now)

	key := r.getRedisKey(symbol, timeframe)

	// Get all klines except the current one
	maxScore := float64(currentBucket.UnixMilli()) - 1
	results, err := r.client.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: "-inf",
		Max: fmt.Sprintf("%f", maxScore),
	}).Result()

	if err != nil {
		return nil, fmt.Errorf("failed to get completed klines from Redis: %w", err)
	}

	klines := make([]RedisKline, 0, len(results))
	for _, result := range results {
		var kline RedisKline
		if err := json.Unmarshal([]byte(result), &kline); err != nil {
			global.GVA_LOG.Error("Failed to unmarshal completed kline from Redis", zap.Error(err))
			continue
		}
		klines = append(klines, kline)
	}

	return klines, nil
}

// RemoveCompletedKlines removes completed klines from Redis after they've been persisted to ClickHouse
func (r *RedisKlineService) RemoveCompletedKlines(ctx context.Context, symbol string, timeframe model.KlineTimeframe, timestamps []int64) error {
	if len(timestamps) == 0 {
		return nil
	}

	key := r.getRedisKey(symbol, timeframe)

	// Remove klines by score (timestamp)
	for _, timestamp := range timestamps {
		score := float64(timestamp)
		if err := r.client.ZRemRangeByScore(ctx, key, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score)).Err(); err != nil {
			return fmt.Errorf("failed to remove completed kline from Redis: %w", err)
		}
	}

	global.GVA_LOG.Debug("Removed completed klines from Redis",
		zap.String("symbol", symbol),
		zap.String("timeframe", string(timeframe)),
		zap.Int("count", len(timestamps)))

	return nil
}

// GetAllSymbols retrieves all symbols that have kline data in Redis
func (r *RedisKlineService) GetAllSymbols(ctx context.Context) ([]string, error) {
	pattern := "kline:*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get kline keys from Redis: %w", err)
	}

	symbolMap := make(map[string]bool)
	for _, key := range keys {
		// Extract symbol from key format: kline:SYMBOL:TIMEFRAME
		parts := splitKey(key)
		if len(parts) == 3 {
			symbolMap[parts[1]] = true
		}
	}

	symbols := make([]string, 0, len(symbolMap))
	for symbol := range symbolMap {
		symbols = append(symbols, symbol)
	}

	return symbols, nil
}

// Helper function to split Redis key
func splitKey(key string) []string {
	result := make([]string, 0, 3)
	start := 0
	for i, char := range key {
		if char == ':' {
			result = append(result, key[start:i])
			start = i + 1
		}
	}
	result = append(result, key[start:])
	return result
}

// BatchUpdateKlines updates multiple klines for a symbol across all timeframes in a single operation
// This is the OPTIMIZED version that replaces individual UpdateKline calls
func (r *RedisKlineService) BatchUpdateKlines(ctx context.Context, symbol string, timestamp time.Time, price, volume float64) error {
	startTime := time.Now()

	// Get all timeframes to process
	timeframes := model.GetAllTimeframes()

	// Step 1: Batch read existing klines for all timeframes
	keys := make([]string, len(timeframes))
	keyToTimeframe := make(map[string]model.KlineTimeframe)

	for i, timeframe := range timeframes {
		key := r.getRedisKey(symbol, timeframe)
		keys[i] = key
		keyToTimeframe[key] = timeframe
	}

	// Batch read all existing klines using MGET-like operation
	existingKlines, err := r.batchGetKlines(ctx, keys, timestamp)
	if err != nil {
		return fmt.Errorf("failed to batch read existing klines: %w", err)
	}

	// Step 2: Process all timeframes in memory
	updatedKlines := make(map[string]*RedisKline)

	for _, timeframe := range timeframes {
		key := r.getRedisKey(symbol, timeframe)
		bucketTime := timeframe.TruncateTime(timestamp)
		tradeTimestamp := timestamp.UnixMilli()

		// Get existing kline or create new one
		var kline RedisKline
		if existingKline := existingKlines[key]; existingKline != nil {
			kline = *existingKline

			// Update High
			if price > kline.High {
				kline.High = price
			}

			// Update Low
			if price < kline.Low {
				kline.Low = price
			}

			// Update Open if this trade is earlier than current open
			// Use timestamp for comparison, with index as tiebreaker (default to 0 for single trades)
			if tradeTimestamp < kline.OpenTimestamp ||
				(tradeTimestamp == kline.OpenTimestamp && 0 < kline.OpenIndex) {
				kline.Open = price
				kline.OpenIndex = 0
				kline.OpenTimestamp = tradeTimestamp
			}

			// Update Close if this trade is later than current close
			// Use timestamp for comparison, with index as tiebreaker (default to 0 for single trades)
			if tradeTimestamp > kline.CloseTimestamp ||
				(tradeTimestamp == kline.CloseTimestamp && 0 >= kline.CloseIndex) {
				kline.Close = price
				kline.CloseIndex = 0
				kline.CloseTimestamp = tradeTimestamp
			}

			// Update Volume and Count
			kline.Volume += volume
			kline.TradesCount++
		} else {
			// Create new kline
			timestampMilli := bucketTime.UnixMilli()
			kline = RedisKline{
				Timestamp:       timestampMilli,
				Symbol:          symbol,
				Open:            price,
				High:            price,
				Low:             price,
				Close:           price,
				Volume:          volume,
				TradesCount:     1,
				Timeframe:       string(timeframe),
				OpenIndex:       0, // Default index for backward compatibility
				CloseIndex:      0, // Default index for backward compatibility
				OpenBatchIndex:  0, // Default batch index for backward compatibility
				CloseBatchIndex: 0, // Default batch index for backward compatibility
				OpenTimestamp:   tradeTimestamp,
				CloseTimestamp:  tradeTimestamp,
			}
		}

		updatedKlines[key] = &kline
	}

	// Step 3: Batch write all updated klines using pipeline
	if err := r.batchWriteKlines(ctx, updatedKlines, timestamp); err != nil {
		return fmt.Errorf("failed to batch write klines: %w", err)
	}

	duration := time.Since(startTime)
	global.GVA_LOG.Debug("Batch updated klines for all timeframes",
		zap.String("symbol", symbol),
		zap.Int("timeframe_count", len(timeframes)),
		zap.Float64("price", price),
		zap.Float64("volume", volume),
		zap.Duration("duration", duration))

	return nil
}

// batchGetKlines performs batch read of existing klines for multiple keys
func (r *RedisKlineService) batchGetKlines(ctx context.Context, keys []string, timestamp time.Time) (map[string]*RedisKline, error) {
	result := make(map[string]*RedisKline)

	// For each key, we need to get the specific kline data from sorted set
	for _, key := range keys {
		// Extract timeframe from key to calculate bucket time
		parts := splitKey(key)
		if len(parts) != 3 {
			continue
		}

		timeframe := model.KlineTimeframe(parts[2])
		bucketTime := timeframe.TruncateTime(timestamp)
		score := float64(bucketTime.UnixMilli())

		// Get existing kline data for this specific timestamp
		existingData, err := r.client.ZRangeByScore(ctx, key, &redis.ZRangeBy{
			Min: fmt.Sprintf("%f", score),
			Max: fmt.Sprintf("%f", score),
		}).Result()

		if err != nil {
			global.GVA_LOG.Warn("Failed to get kline data for key",
				zap.String("key", key),
				zap.Error(err))
			result[key] = nil
			continue
		}

		if len(existingData) > 0 {
			var kline RedisKline
			if err := json.Unmarshal([]byte(existingData[0]), &kline); err != nil {
				global.GVA_LOG.Warn("Failed to unmarshal kline data",
					zap.String("key", key),
					zap.Error(err))
				result[key] = nil
				continue
			}
			result[key] = &kline
		} else {
			result[key] = nil
		}
	}

	return result, nil
}

// batchWriteKlines performs batch write of klines using a single pipeline
func (r *RedisKlineService) batchWriteKlines(ctx context.Context, klines map[string]*RedisKline, timestamp time.Time) error {
	// Create single pipeline for all operations
	pipe := r.client.Pipeline()

	for key, kline := range klines {
		if kline == nil {
			continue
		}

		// Extract timeframe from key
		parts := splitKey(key)
		if len(parts) != 3 {
			continue
		}

		timeframe := model.KlineTimeframe(parts[2])
		bucketTime := timeframe.TruncateTime(timestamp)
		score := float64(bucketTime.UnixMilli())

		// Serialize kline data
		klineData, err := json.Marshal(kline)
		if err != nil {
			global.GVA_LOG.Error("Failed to marshal kline data",
				zap.String("key", key),
				zap.Error(err))
			continue
		}

		// Remove old entry first (if exists)
		pipe.ZRemRangeByScore(ctx, key, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score))

		// Add/update the kline in sorted set
		pipe.ZAdd(ctx, key, redis.Z{
			Score:  score,
			Member: string(klineData),
		})

		// Set TTL for the key
		ttlConfig := r.ttlConfigs[timeframe]
		pipe.Expire(ctx, key, ttlConfig.TTL)

		// Trim to keep only the specified number of candles
		pipe.ZRemRangeByRank(ctx, key, 0, int64(-ttlConfig.MaxCandles-1))
	}

	// Execute entire pipeline in single network call
	results, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute batch write pipeline: %w", err)
	}

	// Verify pipeline commands executed successfully (for debugging)
	errorCount := 0
	for i, result := range results {
		if result.Err() != nil {
			global.GVA_LOG.Warn("Batch pipeline command failed",
				zap.Int("command_index", i),
				zap.Error(result.Err()))
			errorCount++
		}
	}

	if errorCount > 0 {
		global.GVA_LOG.Warn("Some batch pipeline commands failed",
			zap.Int("failed_commands", errorCount),
			zap.Int("total_commands", len(results)))
	}

	return nil
}
