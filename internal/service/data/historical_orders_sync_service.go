package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"go.uber.org/zap"
)

type HistoricalOrdersSyncService struct {
	dbTidb       *gorm.DB
	dbClickHouse *sql.DB
	redis        *redis.Client
}

func NewHistoricalOrdersSyncService() *HistoricalOrdersSyncService {
	return &HistoricalOrdersSyncService{
		dbTidb:       global.GVA_DB_TIDB,
		dbClickHouse: global.GVA_DB_CLICKHOUSE,
		redis:        global.GVA_REDIS,
	}
}

// GetUserLatestHistoricalOrderRedisKey returns the Redis key for user's latest historical orders
func GetUserLatestHistoricalOrderRedisKey(userID string) string {
	return fmt.Sprintf("historical_orders:latest:%s", userID)
}

type LatestHistoricalOrder struct {
	ID          int64      `json:"id"`
	ProcessedAt *time.Time `json:"processed_at"`
}

// UserLatestHistoricalOrder represents the latest historical order for a user
// This is an alias for UserHistoricalOrder to maintain compatibility
type UserLatestHistoricalOrder = UserHistoricalOrder

// ExecuteSync queries TiDB historical orders data, aggregates by user and syncs to ClickHouse
func (s *HistoricalOrdersSyncService) ExecuteSync() {
	logger := global.GVA_LOG
	if s.dbTidb == nil || s.redis == nil {
		logger.Error("TiDB or Redis not initialized, skipping historical orders sync")
		return
	}

	if s.dbClickHouse == nil {
		logger.Error("ClickHouse not initialized, skipping historical orders sync")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	// 1. Check if TiDB data is from the last 2 days, if not, skip this sync
	var latestTiDBOrder LatestHistoricalOrder
	if err := s.dbTidb.WithContext(ctx).
		Table("order_details").
		Select("id, created_at as processed_at").
		Order("id DESC").
		Limit(1).
		Scan(&latestTiDBOrder).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Info("TiDB has no data, skipping sync")
			return
		}
		logger.Error("Failed to query latest TiDB order_details record", zap.Error(err))
		return
	}

	// Check if TiDB latest data is from the last 2 days
	if latestTiDBOrder.ProcessedAt == nil {
		logger.Info("TiDB latest order has no processed_at, skipping sync")
		return
	}

	twoDaysAgo := time.Now().AddDate(0, 0, -2)
	if latestTiDBOrder.ProcessedAt.Before(twoDaysAgo) {
		logger.Info("TiDB latest data is older than 2 days, skipping sync",
			zap.Time("latest_processed_at", *latestTiDBOrder.ProcessedAt),
			zap.Time("two_days_ago", twoDaysAgo))
		return
	}

	// 2. Check if ClickHouse has data
	clickhouseLatestOrder, err := s.getLatestOrderFromClickHouse(ctx)
	if err != nil {
		logger.Error("Failed to query latest ClickHouse historical_orders record", zap.Error(err))
		return
	}

	// 3. If ClickHouse has no data, query the latest 2000 records from TiDB and save to ClickHouse
	if clickhouseLatestOrder == nil {
		logger.Info("ClickHouse has no data, performing initial sync with latest 2000 records from TiDB")
		if err := s.syncInitialDataFromTiDB(ctx, 2000); err != nil {
			logger.Error("Failed to sync initial data from TiDB", zap.Error(err))
			return
		}
	} else {
		// 4. If ClickHouse has data, perform incremental sync by comparing TiDB and ClickHouse data differences
		logger.Info("ClickHouse has data, performing incremental sync",
			zap.Int64("clickhouse_latest_id", clickhouseLatestOrder.ID),
			zap.Int64("tidb_latest_id", latestTiDBOrder.ID))

		if latestTiDBOrder.ID > clickhouseLatestOrder.ID {
			if err := s.syncIncrementalData(ctx, clickhouseLatestOrder.ID); err != nil {
				logger.Error("Failed to sync incremental data", zap.Error(err))
				return
			}
		} else {
			logger.Info("TiDB and ClickHouse are in sync, no incremental sync needed")
		}
	}

	// 5. Query each user from user table, get their data from ClickHouse (at most 2000 most recent historical orders), and save to Redis. Skip Redis cache if no data.
	if err := s.saveAllUsersOrdersToRedis(ctx); err != nil {
		logger.Error("Failed to save users orders to Redis", zap.Error(err))
		return
	}

	logger.Info("Historical orders data sync completed",
		zap.Int64("latest_tidb_id", latestTiDBOrder.ID))
}

// syncInitialDataFromTiDB queries the latest limit records from TiDB and saves them to ClickHouse
func (s *HistoricalOrdersSyncService) syncInitialDataFromTiDB(ctx context.Context, limit int) error {
	logger := global.GVA_LOG

	// Query the latest limit records from TiDB
	orders, err := s.queryLatestOrdersFromTiDB(ctx, limit)
	if err != nil {
		return fmt.Errorf("failed to query latest orders from TiDB: %w", err)
	}

	if len(orders) == 0 {
		logger.Info("No orders found in TiDB for initial sync")
		return nil
	}

	// Batch insert to ClickHouse
	if err := s.batchInsertToClickHouse(ctx, orders, uuid.Nil); err != nil {
		return fmt.Errorf("failed to batch insert to ClickHouse: %w", err)
	}

	logger.Info("Initial sync completed",
		zap.Int("record_count", len(orders)))
	return nil
}

// syncIncrementalData performs incremental sync by comparing TiDB and ClickHouse data differences
func (s *HistoricalOrdersSyncService) syncIncrementalData(ctx context.Context, clickhouseLatestID int64) error {
	logger := global.GVA_LOG

	// Query records from TiDB where ID is greater than clickhouseLatestID
	orders, err := s.queryOrdersFromTiDBAfterID(ctx, clickhouseLatestID)
	if err != nil {
		return fmt.Errorf("failed to query incremental orders from TiDB: %w", err)
	}

	if len(orders) == 0 {
		logger.Info("No incremental orders found")
		return nil
	}

	// Batch insert to ClickHouse
	if err := s.batchInsertToClickHouse(ctx, orders, uuid.Nil); err != nil {
		return fmt.Errorf("failed to batch insert incremental data to ClickHouse: %w", err)
	}

	logger.Info("Incremental sync completed",
		zap.Int("record_count", len(orders)),
		zap.Int64("from_id", clickhouseLatestID))
	return nil
}

// saveAllUsersOrdersToRedis queries each user from user table, gets their data from ClickHouse (at most 2000 most recent historical orders), and saves to Redis. Skips Redis cache if no data.
func (s *HistoricalOrdersSyncService) saveAllUsersOrdersToRedis(ctx context.Context) error {
	logger := global.GVA_LOG

	// Read all users
	type userRow struct {
		ID          uuid.UUID
		UserAddress *string
	}
	var users []userRow
	query := "SELECT id, user_address FROM users"
	rows, err := s.dbClickHouse.QueryContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to query ClickHouse users: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var u userRow
		var userAddress sql.NullString
		if err := rows.Scan(&u.ID, &userAddress); err != nil {
			logger.Warn("Failed to scan user ID and user_address", zap.Error(err))
			continue
		}
		if userAddress.Valid {
			u.UserAddress = &userAddress.String
		}
		users = append(users, u)
	}
	if err := rows.Err(); err != nil {
		return fmt.Errorf("failed to iterate user list: %w", err)
	}

	// Query each user's data from ClickHouse (at most 2000 records) and save to Redis
	for _, u := range users {
		if u.ID == uuid.Nil {
			logger.Debug("Skipping zero-value user ID")
			continue
		}

		orders, err := s.getUserOrdersFromClickHouse(ctx, u.ID, 2000)
		if err != nil {
			logger.Warn("Failed to query user orders from ClickHouse",
				zap.String("user_id", u.ID.String()),
				zap.Error(err))
			continue
		}

		// Skip Redis cache if no data
		if len(orders) == 0 {
			logger.Debug("No orders found for user, skipping Redis cache",
				zap.String("user_id", u.ID.String()))
			continue
		}

		// Save to Redis
		if err := s.saveUserOrdersToRedis(ctx, u.ID, orders); err != nil {
			logger.Warn("Failed to save user orders to Redis",
				zap.String("user_id", u.ID.String()),
				zap.Error(err))
			continue
		}

		logger.Debug("Saved user orders to Redis",
			zap.String("user_id", u.ID.String()),
			zap.Int("order_count", len(orders)))
	}

	return nil
}

// queryLatestOrdersFromTiDB queries the latest limit records from TiDB
func (s *HistoricalOrdersSyncService) queryLatestOrdersFromTiDB(ctx context.Context, limit int) ([]model.HistoricalOrderTiDB, error) {
	logger := global.GVA_LOG

	// Build query to join order_details with order_status_events
	query := `
		SELECT 
			od.id,
			od.user_address,
			od.coin,
			od.limit_px,
			od.oid,
			od.side,
			od.orig_sz as sz,
			od.order_timestamp as timestamp,
			COALESCE(ose.status, '') as status,
			COALESCE(od.limit_px, 0) as avg_px,
			COALESCE(ose.filled_sz, 0) as filled_sz,
			od.trade_date,
			od.created_at as processed_at
		FROM order_details od
		LEFT JOIN (
			SELECT 
				ose1.oid,
				ose1.user_address,
				ose1.status,
				ose1.filled_sz,
				ose1.event_timestamp
			FROM order_status_events ose1
			INNER JOIN (
				SELECT 
					oid,
					user_address,
					MAX(event_timestamp) as max_timestamp
				FROM order_status_events
				GROUP BY oid, user_address
			) ose2 ON ose1.oid = ose2.oid 
				AND ose1.user_address = ose2.user_address 
				AND ose1.event_timestamp = ose2.max_timestamp
		) ose ON od.oid = ose.oid AND od.user_address = ose.user_address
		ORDER BY od.id DESC
		LIMIT ?
	`

	rows, err := s.dbTidb.WithContext(ctx).Raw(query, limit).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to query latest orders from TiDB: %w", err)
	}
	defer rows.Close()

	return s.scanOrdersFromRows(rows, logger)
}

// queryOrdersFromTiDBAfterID queries records from TiDB where ID is greater than afterID
func (s *HistoricalOrdersSyncService) queryOrdersFromTiDBAfterID(ctx context.Context, afterID int64) ([]model.HistoricalOrderTiDB, error) {
	logger := global.GVA_LOG

	// Build query to join order_details with order_status_events
	query := `
		SELECT 
			od.id,
			od.user_address,
			od.coin,
			od.limit_px,
			od.oid,
			od.side,
			od.orig_sz as sz,
			od.order_timestamp as timestamp,
			COALESCE(ose.status, '') as status,
			COALESCE(od.limit_px, 0) as avg_px,
			COALESCE(ose.filled_sz, 0) as filled_sz,
			od.trade_date,
			od.created_at as processed_at
		FROM order_details od
		LEFT JOIN (
			SELECT 
				ose1.oid,
				ose1.user_address,
				ose1.status,
				ose1.filled_sz,
				ose1.event_timestamp
			FROM order_status_events ose1
			INNER JOIN (
				SELECT 
					oid,
					user_address,
					MAX(event_timestamp) as max_timestamp
				FROM order_status_events
				GROUP BY oid, user_address
			) ose2 ON ose1.oid = ose2.oid 
				AND ose1.user_address = ose2.user_address 
				AND ose1.event_timestamp = ose2.max_timestamp
		) ose ON od.oid = ose.oid AND od.user_address = ose.user_address
		WHERE od.id > ?
		ORDER BY od.id ASC
	`

	rows, err := s.dbTidb.WithContext(ctx).Raw(query, afterID).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to query orders from TiDB after ID: %w", err)
	}
	defer rows.Close()

	return s.scanOrdersFromRows(rows, logger)
}

// scanOrdersFromRows scans order data from rows
func (s *HistoricalOrdersSyncService) scanOrdersFromRows(rows *sql.Rows, logger *zap.Logger) ([]model.HistoricalOrderTiDB, error) {
	var orders []model.HistoricalOrderTiDB
	for rows.Next() {
		var order model.HistoricalOrderTiDB
		var userAddr sql.NullString
		var coin sql.NullString
		var limitPx, sz, avgPx, filledSz sql.NullString
		var oid sql.NullInt64
		var side, status, tradeDate sql.NullString
		var timestamp sql.NullInt64
		var processedAt sql.NullTime

		if err := rows.Scan(
			&order.ID,
			&userAddr,
			&coin,
			&limitPx,
			&oid,
			&side,
			&sz,
			&timestamp,
			&status,
			&avgPx,
			&filledSz,
			&tradeDate,
			&processedAt,
		); err != nil {
			logger.Warn("Failed to scan historical order", zap.Error(err))
			continue
		}

		// Populate order fields
		if userAddr.Valid {
			order.UserAddress = &userAddr.String
		}
		if coin.Valid {
			order.Coin = coin.String
		}
		if limitPx.Valid {
			if err := order.LimitPx.Scan(limitPx.String); err != nil {
				logger.Warn("Failed to parse limit_px", zap.String("limit_px", limitPx.String), zap.Error(err))
			}
		}
		if oid.Valid {
			order.Oid = oid.Int64
		}
		if side.Valid {
			order.Side = side.String
		}
		if sz.Valid {
			if err := order.Sz.Scan(sz.String); err != nil {
				logger.Warn("Failed to parse sz", zap.String("sz", sz.String), zap.Error(err))
			}
		}
		if timestamp.Valid {
			order.Timestamp = timestamp.Int64
		}
		if status.Valid {
			order.Status = status.String
		} else {
			order.Status = ""
		}
		if avgPx.Valid {
			if err := order.AvgPx.Scan(avgPx.String); err != nil {
				logger.Warn("Failed to parse avg_px", zap.String("avg_px", avgPx.String), zap.Error(err))
			}
		}
		if order.AvgPx.IsZero() {
			order.AvgPx = order.LimitPx
		}
		if filledSz.Valid {
			if err := order.FilledSz.Scan(filledSz.String); err != nil {
				logger.Warn("Failed to parse filled_sz", zap.String("filled_sz", filledSz.String), zap.Error(err))
			}
		}
		if tradeDate.Valid {
			order.TradeDate = tradeDate.String
		}
		if processedAt.Valid {
			order.ProcessedAt = &processedAt.Time
		}

		orders = append(orders, order)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate orders: %w", err)
	}

	return orders, nil
}

// batchInsertToClickHouse batch inserts data to ClickHouse
func (s *HistoricalOrdersSyncService) batchInsertToClickHouse(ctx context.Context, orders []model.HistoricalOrderTiDB, userID uuid.UUID) error {
	if len(orders) == 0 {
		return nil
	}

	// Build batch insert SQL
	insertQuery := `
		INSERT INTO historical_orders (
			id, user_address, coin, limit_px, oid, side, sz, timestamp,
			status, avg_px, filled_sz, trade_date, processed_at
		) VALUES `

	values := make([]interface{}, 0, len(orders)*13) // 13 fields per order
	valuePlaceholders := make([]string, 0, len(orders))

	for _, order := range orders {
		valuePlaceholders = append(valuePlaceholders, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

		var userAddr *string = order.UserAddress

		values = append(values,
			order.ID,
			userAddr,
			order.Coin,
			order.LimitPx.String(),
			order.Oid,
			order.Side,
			order.Sz.String(),
			order.Timestamp,
			order.Status,
			order.AvgPx.String(),
			order.FilledSz.String(),
			order.TradeDate,
			order.ProcessedAt,
		)
	}

	finalQuery := insertQuery + strings.Join(valuePlaceholders, ", ")

	_, err := s.dbClickHouse.ExecContext(ctx, finalQuery, values...)
	if err != nil {
		return fmt.Errorf("failed to execute batch insert: %w", err)
	}

	return nil
}

// getLatestOrderFromClickHouse queries the latest historical_orders id and processed_at record in ClickHouse
func (s *HistoricalOrdersSyncService) getLatestOrderFromClickHouse(ctx context.Context) (*LatestHistoricalOrder, error) {
	query := `
		SELECT id, processed_at
		FROM historical_orders
		ORDER BY id DESC
		LIMIT 1
	`

	var latestOrder LatestHistoricalOrder
	var processedAtNullable sql.NullTime

	err := s.dbClickHouse.QueryRowContext(ctx, query).Scan(&latestOrder.ID, &processedAtNullable)
	if err == sql.ErrNoRows {
		// ClickHouse has no data, return nil
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to query latest ClickHouse record: %w", err)
	}

	// Handle processed_at field (may be NULL)
	if processedAtNullable.Valid {
		latestOrder.ProcessedAt = &processedAtNullable.Time
	}

	return &latestOrder, nil
}

type UserHistoricalOrder struct {
	ID          int64      `json:"id"`
	UserAddress string     `json:"user_address"`
	Coin        string     `json:"coin"`
	LimitPx     string     `json:"limit_px"`
	Oid         int64      `json:"oid"`
	Side        string     `json:"side"`
	Sz          string     `json:"sz"`
	Timestamp   int64      `json:"timestamp"`
	Status      string     `json:"status"`
	AvgPx       string     `json:"avg_px"`
	FilledSz    string     `json:"filled_sz"`
	TradeDate   string     `json:"trade_date"`
	ProcessedAt *time.Time `json:"processed_at"`
}

// getUserOrdersFromClickHouse queries user's historical orders from ClickHouse (at most limit records)
func (s *HistoricalOrdersSyncService) getUserOrdersFromClickHouse(ctx context.Context, userID uuid.UUID, limit int) ([]UserHistoricalOrder, error) {
	query := `
		SELECT 
			id, user_address, coin,
			toString(limit_px) as limit_px, oid, side,
			toString(sz) as sz, timestamp, status,
			toString(avg_px) as avg_px, toString(filled_sz) as filled_sz,
			trade_date, processed_at
		FROM historical_orders
		WHERE user_address = ?
		ORDER BY id DESC
		LIMIT ?
	`

	rows, err := s.dbClickHouse.QueryContext(ctx, query, userID.String(), limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query user orders from ClickHouse: %w", err)
	}
	defer rows.Close()

	var orders []UserHistoricalOrder
	for rows.Next() {
		var order UserHistoricalOrder
		var processedAtNullable sql.NullTime
		var userAddr, coin, limitPx, sz, side, status, avgPx, filledSz, tradeDate sql.NullString
		var oid sql.NullInt64

		if err := rows.Scan(
			&order.ID,
			&userAddr,
			&coin,
			&limitPx,
			&oid,
			&side,
			&sz,
			&order.Timestamp,
			&status,
			&avgPx,
			&filledSz,
			&tradeDate,
			&processedAtNullable,
		); err != nil {
			return nil, fmt.Errorf("failed to scan user order: %w", err)
		}

		if userAddr.Valid {
			order.UserAddress = userAddr.String
		}
		if coin.Valid {
			order.Coin = coin.String
		}
		if limitPx.Valid {
			order.LimitPx = limitPx.String
		}
		if oid.Valid {
			order.Oid = oid.Int64
		}
		if side.Valid {
			order.Side = side.String
		}
		if sz.Valid {
			order.Sz = sz.String
		}
		if status.Valid {
			order.Status = status.String
		}
		if avgPx.Valid {
			order.AvgPx = avgPx.String
		}
		if filledSz.Valid {
			order.FilledSz = filledSz.String
		}
		if tradeDate.Valid {
			order.TradeDate = tradeDate.String
		}
		if processedAtNullable.Valid {
			order.ProcessedAt = &processedAtNullable.Time
		}

		orders = append(orders, order)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate user orders: %w", err)
	}

	return orders, nil
}

// saveUserOrdersToRedis saves user's historical orders to Redis
func (s *HistoricalOrdersSyncService) saveUserOrdersToRedis(ctx context.Context, userID uuid.UUID, orders []UserHistoricalOrder) error {
	logger := global.GVA_LOG
	userIDStr := userID.String()

	data, err := json.Marshal(orders)
	if err != nil {
		return fmt.Errorf("failed to serialize user orders: %w", err)
	}

	redisKey := GetUserLatestHistoricalOrderRedisKey(userIDStr)
	if err := s.redis.Set(ctx, redisKey, data, 0).Err(); err != nil {
		return fmt.Errorf("failed to save user orders to Redis: %w", err)
	}

	logger.Debug("Saved user orders to Redis",
		zap.String("user_id", userIDStr),
		zap.String("redis_key", redisKey),
		zap.Int("order_count", len(orders)))
	return nil
}
