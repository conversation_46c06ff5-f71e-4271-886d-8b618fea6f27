package data

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

const (
	// WatermarkRedisKey 存储 watermark 的 Redis key 前缀
	WatermarkRedisKey = "user_non_funding:watermark:"
	// DefaultWatermark 默认 watermark（从很久以前开始）
	DefaultWatermark = "1970-01-01T00:00:00Z"
)

// UserNonFundingSyncTask 管理增量同步任务
type UserNonFundingSyncTask struct {
	syncService *UserNonFundingLedgerSyncService
	addresses   []string
	overlap     time.Duration
	batchSize   int
}

// NewUserNonFundingSyncTask 创建新的同步任务
func NewUserNonFundingSyncTask(addresses []string, overlap time.Duration, batchSize int) *UserNonFundingSyncTask {
	if batchSize <= 0 {
		batchSize = 10000
	}
	if overlap <= 0 {
		overlap = 5 * time.Minute // 默认5分钟重叠窗口
	}

	return &UserNonFundingSyncTask{
		syncService: NewUserNonFundingLedgerSyncService(),
		addresses:   addresses,
		overlap:     overlap,
		batchSize:   batchSize,
	}
}

// ExecuteSync 执行增量同步任务
// 这个方法会被定时任务调用
func (t *UserNonFundingSyncTask) ExecuteSync() {
	logger := global.GVA_LOG
	ctx := context.Background()

	// 若未显式配置地址，则从 ClickHouse users 表动态加载地址
	addrs := t.addresses
	if len(addrs) == 0 {
		var err error
		addrs, err = fetchUserAddressesFromCH(ctx)
		if err != nil {
			logger.Error("Failed to load user addresses from ClickHouse", zap.Error(err))
			return
		}
		if len(addrs) == 0 {
			logger.Info("No user addresses found in ClickHouse; skip this incremental run")
			return
		}
	}

	// 获取当前 watermark
	watermark, err := t.getWatermark(ctx)
	if err != nil {
		logger.Error("Failed to get watermark", zap.Error(err))
		return
	}

	// 执行增量同步
	now := time.Now().UTC()
	newWatermark, err := t.syncService.SyncIncrementalOnce(ctx, addrs, watermark, t.overlap, now, t.batchSize)
	if err != nil {
		logger.Error("Incremental sync failed",
			zap.Time("watermark", watermark),
			zap.Error(err))
		return
	}

	// 更新 watermark（只有当有新数据时才更新）
	if newWatermark.After(watermark) {
		if err := t.setWatermark(ctx, newWatermark); err != nil {
			logger.Error("Failed to update watermark", zap.Error(err))
			return
		}
		logger.Info("Incremental sync completed",
			zap.Time("old_watermark", watermark),
			zap.Time("new_watermark", newWatermark),
			zap.Int("addresses", len(t.addresses)))
	}
}

// ExecuteBackfill 执行全量同步（一次性任务）
// startTime: 开始时间
// endTime: 结束时间（通常设置为当前时间前1小时，避免与增量同步冲突）
func (t *UserNonFundingSyncTask) ExecuteBackfill(startTime, endTime time.Time) error {
	logger := global.GVA_LOG
	ctx := context.Background()

	// 若未显式配置地址，则从 ClickHouse users 表动态加载地址
	addrs := t.addresses
	if len(addrs) == 0 {
		var err error
		addrs, err = fetchUserAddressesFromCH(ctx)
		if err != nil {
			logger.Error("Failed to load user addresses from ClickHouse", zap.Error(err))
			return err
		}
		if len(addrs) == 0 {
			logger.Info("No user addresses found in ClickHouse; skip backfill run")
			return nil
		}
	}

	logger.Info("Starting backfill",
		zap.Time("start", startTime),
		zap.Time("end", endTime),
		zap.Int("addresses", len(addrs)))

	err := t.syncService.BackfillRange(ctx, addrs, startTime, endTime, t.batchSize)
	if err != nil {
		logger.Error("Backfill failed", zap.Error(err))
		return err
	}

	// 全量同步完成后，设置 watermark 为 endTime
	// 这样增量同步就会从 endTime 开始
	if err := t.setWatermark(ctx, endTime); err != nil {
		logger.Error("Failed to set watermark after backfill", zap.Error(err))
		return err
	}

	logger.Info("Backfill completed successfully",
		zap.Time("start", startTime),
		zap.Time("end", endTime))

	return nil
}

// 从 ClickHouse users 表获取地址列表（统一小写、过滤空/NULL）
func fetchUserAddressesFromCH(ctx context.Context) ([]string, error) {
	ch := global.GVA_DB_CLICKHOUSE
	if ch == nil {
		return nil, fmt.Errorf("clickhouse not initialized")
	}
	const q = `
        SELECT lower(user_address)
        FROM users
        WHERE user_address IS NOT NULL AND user_address != ''
    `
	rows, err := ch.QueryContext(ctx, q)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var out []string
	for rows.Next() {
		var addr string
		if err := rows.Scan(&addr); err != nil {
			return nil, err
		}
		out = append(out, addr)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return out, nil
}

// getWatermark 从 Redis 获取 watermark
// 如果不存在，返回默认值（很久以前的时间）
func (t *UserNonFundingSyncTask) getWatermark(ctx context.Context) (time.Time, error) {
	if global.GVA_REDIS == nil {
		// 如果没有 Redis，尝试从 ClickHouse 查询最大的 time
		return t.getWatermarkFromClickHouse(ctx)
	}

	key := WatermarkRedisKey + t.getAddressesKey()
	val, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err != nil {
		// Redis 中没有值，返回默认值
		defaultTime, _ := time.Parse(time.RFC3339, DefaultWatermark)
		return defaultTime, nil
	}

	watermark, err := time.Parse(time.RFC3339, val)
	if err != nil {
		// 解析失败，返回默认值
		defaultTime, _ := time.Parse(time.RFC3339, DefaultWatermark)
		return defaultTime, fmt.Errorf("failed to parse watermark: %w", err)
	}

	return watermark, nil
}

// setWatermark 将 watermark 保存到 Redis
func (t *UserNonFundingSyncTask) setWatermark(ctx context.Context, watermark time.Time) error {
	if global.GVA_REDIS == nil {
		// 如果没有 Redis，可以记录到日志或使用其他存储方式
		global.GVA_LOG.Warn("Redis not available, watermark not persisted",
			zap.Time("watermark", watermark))
		return nil
	}

	key := WatermarkRedisKey + t.getAddressesKey()
	val := watermark.Format(time.RFC3339)

	// 设置 key，不过期（或者设置很长的过期时间）
	err := global.GVA_REDIS.Set(ctx, key, val, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to set watermark in Redis: %w", err)
	}

	return nil
}

// getWatermarkFromClickHouse 从 ClickHouse 查询最大的 time 作为 watermark
// 作为 Redis 不可用时的备选方案
func (t *UserNonFundingSyncTask) getWatermarkFromClickHouse(ctx context.Context) (time.Time, error) {
	if global.GVA_DB_CLICKHOUSE == nil {
		defaultTime, _ := time.Parse(time.RFC3339, DefaultWatermark)
		return defaultTime, fmt.Errorf("ClickHouse not initialized")
	}

	// 构建地址过滤条件
	var query string
	var args []interface{}
	if len(t.addresses) > 0 {
		// 对于地址列表，查询这些地址的最大时间
		placeholders := ""
		for i, addr := range t.addresses {
			if i > 0 {
				placeholders += ","
			}
			placeholders += "?"
			args = append(args, strings.ToLower(addr))
		}
		query = fmt.Sprintf(`
			SELECT max(time) 
			FROM user_non_funding_ledger_updates 
			WHERE addr IN (%s)
		`, placeholders)
	} else {
		// 如果没有地址过滤，查询全局最大时间
		query = `SELECT max(time) FROM user_non_funding_ledger_updates`
	}

	var maxTime sql.NullTime
	err := global.GVA_DB_CLICKHOUSE.QueryRowContext(ctx, query, args...).Scan(&maxTime)
	if err != nil || !maxTime.Valid {
		// 查询失败或没有数据，返回默认值
		defaultTime, _ := time.Parse(time.RFC3339, DefaultWatermark)
		return defaultTime, nil
	}

	return maxTime.Time, nil
}

// getAddressesKey 生成地址列表的 key（用于 Redis key）
func (t *UserNonFundingSyncTask) getAddressesKey() string {
	if len(t.addresses) == 0 {
		return "all"
	}
	// 可以使用地址的哈希值，这里简化处理
	return fmt.Sprintf("count_%d", len(t.addresses))
}
