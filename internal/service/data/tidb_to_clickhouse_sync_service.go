package data

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// TiDBToClickHouseSyncService syncs user fills data from TiDB to ClickHouse
type TiDBToClickHouseSyncService struct {
	dbTidb       *gorm.DB
	dbClickHouse *sql.DB
}

// UserInfo represents user information
type UserInfo struct {
	ID          uuid.UUID
	UserAddress *string
}

const (
	// historyBackfillBatchSizePerUser is the batch size for history data backfill per user
	historyBackfillBatchSizePerUser = 10000
)

// NewTiDBToClickHouseSyncService creates a new sync service instance
func NewTiDBToClickHouseSyncService() *TiDBToClickHouseSyncService {
	return &TiDBToClickHouseSyncService{
		dbTidb:       global.GVA_DB_TIDB,
		dbClickHouse: global.GVA_DB_CLICKHOUSE,
	}
}

// ExecuteSync executes the sync task
// 1. Incremental sync: sync latest data to ClickHouse based on user table
// 2. History data backfill: if ClickHouse has no data, query 10k records per user from TiDB and write to ClickHouse
func (s *TiDBToClickHouseSyncService) ExecuteSync() {
	logger := global.GVA_LOG

	if s.dbTidb == nil {
		logger.Error("TiDB not initialized, skipping sync")
		return
	}

	if s.dbClickHouse == nil {
		logger.Error("ClickHouse not initialized, skipping sync")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
	defer cancel()

	logger.Info("Starting TiDB to ClickHouse sync task")

	// 1. Query all users from ClickHouse users table
	users, err := s.getUsersFromClickHouse(ctx)
	if err != nil {
		logger.Error("Failed to query user list", zap.Error(err))
		return
	}

	if len(users) == 0 {
		logger.Info("No users found, skipping sync")
		return
	}

	logger.Info("Found users", zap.Int("user_count", len(users)))

	// 2. Check if users have data in ClickHouse
	hasData := s.checkUsersDataInClickHouse(ctx, users)

	if !hasData {
		// ClickHouse has no data, perform history data backfill (by user)
		logger.Info("ClickHouse has no data, starting history data backfill (by user)")
		if err := s.backfillHistoryDataByUsers(ctx, users); err != nil {
			logger.Error("History data backfill failed", zap.Error(err))
			return
		}
		logger.Info("History data backfill completed")
	}

	// 3. Execute incremental sync (by user)
	logger.Info("Starting incremental sync (by user)")
	if err := s.incrementalSyncByUsers(ctx, users); err != nil {
		logger.Error("Incremental sync failed", zap.Error(err))
		return
	}

	logger.Info("TiDB to ClickHouse sync task completed")
}

// getUsersFromClickHouse queries all users from ClickHouse users table
func (s *TiDBToClickHouseSyncService) getUsersFromClickHouse(ctx context.Context) ([]UserInfo, error) {
	query := "SELECT id, user_address FROM users"

	rows, err := s.dbClickHouse.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query ClickHouse users table: %w", err)
	}
	defer rows.Close()

	var users []UserInfo
	for rows.Next() {
		var u UserInfo
		var userAddress sql.NullString

		if err := rows.Scan(&u.ID, &userAddress); err != nil {
			global.GVA_LOG.Warn("Failed to scan user data", zap.Error(err))
			continue
		}

		if userAddress.Valid && userAddress.String != "" {
			u.UserAddress = &userAddress.String
		}

		users = append(users, u)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate user list: %w", err)
	}

	return users, nil
}

// checkUsersDataInClickHouse checks if users have data in ClickHouse
func (s *TiDBToClickHouseSyncService) checkUsersDataInClickHouse(ctx context.Context, users []UserInfo) bool {
	query := `
		SELECT COUNT(*)
		FROM node_fills
		LIMIT 1
	`

	var count int64
	err := s.dbClickHouse.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		return false
	}

	return count > 0
}

// backfillHistoryDataByUsers performs history data backfill by user
// For each user, query up to 10k records from TiDB and write to ClickHouse
func (s *TiDBToClickHouseSyncService) backfillHistoryDataByUsers(ctx context.Context, users []UserInfo) error {
	logger := global.GVA_LOG

	totalSynced := 0
	for _, user := range users {
		// Skip zero-value user ID
		if user.ID == uuid.Nil {
			logger.Debug("Skipping zero-value user ID", zap.String("user_id", user.ID.String()))
			continue
		}

		userIDStr := user.ID.String()
		userAddrStr := ""
		if user.UserAddress != nil {
			userAddrStr = *user.UserAddress
		}

		// Query user's history data from TiDB (up to 10k records)
		var fills []model.NodeFillTiDB
		query := s.dbTidb.WithContext(ctx).Table("node_fills")

		if user.UserAddress != nil && *user.UserAddress != "" {
			// Query using both user_address and user_id (as user_address)
			query = query.Where("(user_address = ? OR user_address = ?) AND id >= ?", userIDStr, *user.UserAddress, 0)
		} else {
			// Only use user_id as user_address
			query = query.Where("user_address = ? AND id >= ?", userIDStr, 0)
		}

		err := query.Order("id DESC").Limit(historyBackfillBatchSizePerUser).Find(&fills).Error
		if err != nil {
			logger.Warn("Failed to query user history data",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Error(err))
			continue
		}

		if len(fills) == 0 {
			logger.Debug("User has no history data",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr))
			continue
		}

		// Batch insert to ClickHouse
		if err := s.batchInsertToClickHouse(ctx, fills); err != nil {
			logger.Error("Failed to batch insert user history data to ClickHouse",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Int("count", len(fills)),
				zap.Error(err))
			continue
		}

		totalSynced += len(fills)
		logger.Debug("User history data backfill completed",
			zap.String("user_id", userIDStr),
			zap.String("user_address", userAddrStr),
			zap.Int("synced_count", len(fills)))
	}

	logger.Info("All users history data backfill completed", zap.Int("total_synced", totalSynced))
	return nil
}

// incrementalSyncByUsers performs incremental sync by user
// For each user, sync the user's new data
func (s *TiDBToClickHouseSyncService) incrementalSyncByUsers(ctx context.Context, users []UserInfo) error {
	logger := global.GVA_LOG

	totalSynced := 0
	for _, user := range users {
		// Skip zero-value user ID
		if user.ID == uuid.Nil {
			logger.Debug("Skipping zero-value user ID", zap.String("user_id", user.ID.String()))
			continue
		}

		userIDStr := user.ID.String()
		userAddrStr := ""
		if user.UserAddress != nil {
			userAddrStr = *user.UserAddress
		}

		// Get the latest ID for this user in ClickHouse
		latestClickHouseID, err := s.getUserLatestClickHouseID(ctx, user)
		if err != nil {
			logger.Warn("Failed to get user's latest ClickHouse ID",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Error(err))
			continue
		}

		// Get the latest ID for this user in TiDB
		latestTiDBID, err := s.getUserLatestTiDBID(ctx, user)
		if err != nil {
			logger.Warn("Failed to get user's latest TiDB ID",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Error(err))
			continue
		}

		if latestTiDBID <= latestClickHouseID {
			// User data is already up to date
			continue
		}

		// Execute incremental sync for this user
		syncedCount, err := s.syncUserIncrementalData(ctx, user, latestClickHouseID, latestTiDBID)
		if err != nil {
			logger.Error("User incremental sync failed",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Error(err))
			continue
		}

		totalSynced += syncedCount
		if syncedCount > 0 {
			logger.Debug("User incremental sync completed",
				zap.String("user_id", userIDStr),
				zap.String("user_address", userAddrStr),
				zap.Int("synced_count", syncedCount))
		}
	}

	logger.Info("All users incremental sync completed", zap.Int("total_synced", totalSynced))
	return nil
}

// getUserLatestClickHouseID gets the latest ID for a user in ClickHouse
func (s *TiDBToClickHouseSyncService) getUserLatestClickHouseID(ctx context.Context, user UserInfo) (int64, error) {
	userIDStr := user.ID.String()
	var query string
	var args []interface{}

	if user.UserAddress != nil && *user.UserAddress != "" {
		// Query using both user_address and user_id (as user_address)
		query = `
			SELECT id
			FROM node_fills
			WHERE user_address = ? OR user_address = ?
			ORDER BY id DESC
			LIMIT 1
		`
		args = []interface{}{userIDStr, *user.UserAddress}
	} else {
		// Only use user_id as user_address
		query = `
			SELECT id
			FROM node_fills
			WHERE user_address = ?
			ORDER BY id DESC
			LIMIT 1
		`
		args = []interface{}{userIDStr}
	}

	var latestID int64
	err := s.dbClickHouse.QueryRowContext(ctx, query, args...).Scan(&latestID)
	if err == sql.ErrNoRows {
		// ClickHouse has no data for this user
		return 0, nil
	}
	if err != nil {
		return 0, fmt.Errorf("failed to query user's latest ClickHouse ID: %w", err)
	}

	return latestID, nil
}

// getUserLatestTiDBID gets the latest ID for a user in TiDB
func (s *TiDBToClickHouseSyncService) getUserLatestTiDBID(ctx context.Context, user UserInfo) (int64, error) {
	userIDStr := user.ID.String()
	query := s.dbTidb.WithContext(ctx).Table("node_fills")

	if user.UserAddress != nil && *user.UserAddress != "" {
		// Query using both user_address and user_id (as user_address)
		query = query.Where("user_address = ? OR user_address = ?", userIDStr, *user.UserAddress)
	} else {
		// Only use user_id as user_address
		query = query.Where("user_address = ?", userIDStr)
	}

	var latestID int64
	err := query.Select("id").Order("id DESC").Limit(1).Scan(&latestID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, fmt.Errorf("failed to query user's latest TiDB ID: %w", err)
	}

	return latestID, nil
}

// syncUserIncrementalData syncs user's incremental data
func (s *TiDBToClickHouseSyncService) syncUserIncrementalData(ctx context.Context, user UserInfo, latestClickHouseID, latestTiDBID int64) (int, error) {
	logger := global.GVA_LOG
	userIDStr := user.ID.String()

	// Process in batches to avoid querying too much data at once
	const batchSize = 5000
	startID := latestClickHouseID + 1 // Query from the next ID
	totalSynced := 0

	for startID <= latestTiDBID {
		endID := startID + int64(batchSize)
		if endID > latestTiDBID {
			endID = latestTiDBID + 1
		}

		var fills []model.NodeFillTiDB
		query := s.dbTidb.WithContext(ctx).Table("node_fills")

		if user.UserAddress != nil && *user.UserAddress != "" {
			// Query using both user_address and user_id (as user_address)
			query = query.Where("(user_address = ? OR user_address = ?) AND id >= ? AND id < ?",
				userIDStr, *user.UserAddress, startID, endID)
		} else {
			// Only use user_id as user_address
			query = query.Where("user_address = ? AND id >= ? AND id < ?", userIDStr, startID, endID)
		}

		err := query.Order("id ASC").Find(&fills).Error
		if err != nil {
			return totalSynced, fmt.Errorf("failed to query user incremental data: %w", err)
		}

		if len(fills) == 0 {
			// No more data, skip to next batch
			startID = endID
			continue
		}

		// Batch insert to ClickHouse
		if err := s.batchInsertToClickHouse(ctx, fills); err != nil {
			return totalSynced, fmt.Errorf("failed to batch insert user incremental data to ClickHouse: %w", err)
		}

		totalSynced += len(fills)
		logger.Debug("User incremental sync batch completed",
			zap.String("user_id", userIDStr),
			zap.Int64("from_id", startID),
			zap.Int64("to_id", endID-1),
			zap.Int("count", len(fills)))

		startID = endID

		// Avoid too frequent database operations
		time.Sleep(50 * time.Millisecond)
	}

	return totalSynced, nil
}

// batchInsertToClickHouse batch inserts data to ClickHouse
func (s *TiDBToClickHouseSyncService) batchInsertToClickHouse(ctx context.Context, fills []model.NodeFillTiDB) error {
	if len(fills) == 0 {
		return nil
	}

	// Build batch insert SQL
	insertQuery := `
		INSERT INTO node_fills (
			id, user_address, hash, tid, coin, px, sz, side, time,
			start_position, direction, closed_pnl, oid, crossed,
			fee, fee_token, trade_date, trade_type, processed_at
		) VALUES 
	`

	values := make([]interface{}, 0, len(fills)*19) // 19 fields
	valuePlaceholders := make([]string, 0, len(fills))

	for _, fill := range fills {
		valuePlaceholders = append(valuePlaceholders, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

		var hash *string = fill.Hash
		var tid *int64 = fill.Tid
		var userAddr *string = fill.UserAddress

		var crossedInt int8 = 0
		if fill.Crossed {
			crossedInt = 1
		}

		values = append(values,
			fill.ID,
			userAddr,
			hash,
			tid,
			fill.Coin,
			fill.Px.String(),
			fill.Sz.String(),
			fill.Side,
			fill.Time,
			fill.StartPosition.String(),
			fill.Direction,
			fill.ClosedPnl.String(),
			fill.Oid,
			crossedInt,
			fill.Fee.String(),
			fill.FeeToken,
			fill.TradeDate,
			fill.TradeType,
			fill.ProcessedAt,
		)
	}

	finalQuery := insertQuery + strings.Join(valuePlaceholders, ", ")

	_, err := s.dbClickHouse.ExecContext(ctx, finalQuery, values...)
	if err != nil {
		return fmt.Errorf("failed to execute batch insert: %w", err)
	}

	return nil
}

