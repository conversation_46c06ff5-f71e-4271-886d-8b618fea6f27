package data

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// UserNonFundingLedgerSyncService synchronizes non-funding ledger updates
// from TiDB table `evt_deposit_withdraws` to ClickHouse table
// `user_non_funding_ledger_updates`.
//
// - Full backfill: BackfillRange
// - Incremental:   SyncIncrementalOnce (watermark + overlap)
//
// This service is self-contained and does not alter other components.
type UserNonFundingLedgerSyncService struct {
	dbTidb       *gorm.DB
	dbClickHouse *sql.DB
}

func NewUserNonFundingLedgerSyncService() *UserNonFundingLedgerSyncService {
	return &UserNonFundingLedgerSyncService{
		dbTidb:       global.GVA_DB_TIDB,
		dbClickHouse: global.GVA_DB_CLICKHOUSE,
	}
}

// evtDepositWithdraws represents a row from TiDB table `evt_deposit_withdraws`.
type evtDepositWithdraws struct {
	ArbiHash  string    `gorm:"column:arbi_hash"`
	Time      time.Time `gorm:"column:time"`
	Addr      string    `gorm:"column:addr"`
	IsDeposit bool      `gorm:"column:is_deposit"`
	Usdc      string    `gorm:"column:usdc"` // Keep as string to preserve exact decimal textual form
	Fee       string    `gorm:"column:fee"`
}

// BackfillRange performs a one-time backfill for a set of addresses within [start,end).
// - addresses: addresses to include (case-insensitive). Empty slice means no-op.
// - batchSize: number of rows per batch (recommended 5k~50k). <=0 uses default 10000.
func (s *UserNonFundingLedgerSyncService) BackfillRange(ctx context.Context, addresses []string, start, end time.Time, batchSize int) error {
	logger := global.GVA_LOG
	if s.dbTidb == nil || s.dbClickHouse == nil {
		logger.Error("TiDB or ClickHouse not initialized")
		return fmt.Errorf("database not initialized")
	}

	addrs := normalizeAddresses(addresses)
	if len(addrs) == 0 {
		logger.Info("BackfillRange: empty address list -> syncing all addresses (no address filter)")
	}
	if batchSize <= 0 {
		batchSize = 10000
	}

	// Seek-pagination using (time, arbi_hash)
	var lastTime time.Time
	var lastHash string

	for {
		batch, err := s.readTiDBBatch(ctx, addrs, &start, &end, &lastTime, &lastHash, batchSize)
		if err != nil {
			return err
		}
		if len(batch) == 0 {
			break
		}

		if err := s.insertClickHouseBatch(ctx, batch); err != nil {
			return err
		}

		// advance cursor
		last := batch[len(batch)-1]
		lastTime = last.Time
		lastHash = last.ArbiHash
	}

	logger.Info("BackfillRange completed",
		zap.Int("addresses", len(addrs)),
		zap.Time("start", start),
		zap.Time("end", end))
	return nil
}

// SyncIncrementalOnce pulls rows where time in (watermark-overlap, now] and inserts into CH.
// Returns the new watermark (max(time) seen) when any rows were written; otherwise returns input watermark.
// - addresses: address subset; empty slice means no-op.
// - watermark: previous high-water mark (usually last successful max(time))
// - overlap: safety window to cover late arrivals (e.g., 5m)
// - nowTs: upper bound time (use time.Now().UTC())
// - batchSize: rows per batch
func (s *UserNonFundingLedgerSyncService) SyncIncrementalOnce(ctx context.Context, addresses []string, watermark time.Time, overlap time.Duration, nowTs time.Time, batchSize int) (time.Time, error) {
	logger := global.GVA_LOG
	if s.dbTidb == nil || s.dbClickHouse == nil {
		logger.Error("TiDB or ClickHouse not initialized")
		return watermark, fmt.Errorf("database not initialized")
	}

	addrs := normalizeAddresses(addresses)
	if len(addrs) == 0 {
		logger.Info("SyncIncrementalOnce: empty address list -> syncing all addresses (no address filter)")
	}
	if batchSize <= 0 {
		batchSize = 10000
	}

	// Determine scan window: (wm - overlap, now]
	scanStart := watermark.Add(-overlap)
	scanEnd := nowTs

	var lastTime time.Time
	var lastHash string
	maxSeen := watermark
	wroteAny := false

	for {
		batch, err := s.readTiDBBatch(ctx, addrs, &scanStart, &scanEnd, &lastTime, &lastHash, batchSize)
		if err != nil {
			return watermark, err
		}
		if len(batch) == 0 {
			break
		}

		if err := s.insertClickHouseBatch(ctx, batch); err != nil {
			return watermark, err
		}
		wroteAny = true

		// advance cursor and watermark
		last := batch[len(batch)-1]
		lastTime = last.Time
		lastHash = last.ArbiHash
		if lastTime.After(maxSeen) {
			maxSeen = lastTime
		}
	}

	if wroteAny {
		logger.Info("Incremental sync completed",
			zap.Time("window_start", scanStart),
			zap.Time("window_end", scanEnd),
			zap.Time("new_watermark", maxSeen))
		return maxSeen, nil
	}
	return watermark, nil
}

// readTiDBBatch reads a batch from TiDB with filters and seek pagination by (time, arbi_hash).
func (s *UserNonFundingLedgerSyncService) readTiDBBatch(
	ctx context.Context,
	addresses []string,
	start *time.Time,
	end *time.Time,
	lastTime *time.Time,
	lastHash *string,
	limit int,
) ([]evtDepositWithdraws, error) {
	logger := global.GVA_LOG
	q := s.dbTidb.WithContext(ctx).Table("evt_deposit_withdraws").
		Select("arbi_hash, time, addr, is_deposit, usdc, fee")

	// Enable SQL logging for visibility of generated SQL and parameters
	q = q.Debug()

	if start != nil {
		q = q.Where("time >= ?", *start)
	}
	if end != nil {
		q = q.Where("time < ?", *end)
	}

	// address filter (lowercase compare). If TiDB stores mixed case, compare case-insensitively.
	// Use OR chain in chunks to avoid too many params in one IN.
	if len(addresses) > 0 {
		const chunk = 500
		var conds []string
		var args []interface{}
		for i := 0; i < len(addresses); i += chunk {
			j := i + chunk
			if j > len(addresses) {
				j = len(addresses)
			}
			subset := addresses[i:j]
			conds = append(conds, fmt.Sprintf("LOWER(addr) IN (%s)", strings.Repeat("?,", len(subset))[0:len(subset)*2-1]))
			for _, a := range subset {
				args = append(args, a)
			}
		}
		q = q.Where("("+strings.Join(conds, " OR ")+")", args...)
	}

	// seek pagination
	if lastTime != nil && !lastTime.IsZero() {
		q = q.Where("(time > ?) OR (time = ? AND arbi_hash > ?)", *lastTime, *lastTime, *lastHash)
	}

	q = q.Order("time ASC, arbi_hash ASC").Limit(limit)

	var rows []evtDepositWithdraws
	if err := q.Find(&rows).Error; err != nil && err != gorm.ErrRecordNotFound {
		logger.Error("Failed to read TiDB batch", zap.Error(err))
		return nil, fmt.Errorf("readTiDBBatch: %w", err)
	}
	return rows, nil
}

// insertClickHouseBatch writes a batch into ClickHouse table `user_non_funding_ledger_updates`.
// It relies on ClickHouse-side deduplication (e.g., ReplacingMergeTree + insert_deduplicate)
// using (addr, time, arbi_hash) to achieve idempotency.
func (s *UserNonFundingLedgerSyncService) insertClickHouseBatch(ctx context.Context, batch []evtDepositWithdraws) error {
	if len(batch) == 0 {
		return nil
	}

	// Normalize and convert rows
	// Columns: arbi_hash, time, addr, type, usd, fee
	const insertSQL = `
        INSERT INTO user_non_funding_ledger_updates (
            arbi_hash, time, addr, type, usd, fee
        ) VALUES (?, ?, ?, ?, ?, ?)
    `

	tx, err := s.dbClickHouse.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("clickhouse begin tx: %w", err)
	}
	stmt, err := tx.PrepareContext(ctx, insertSQL)
	if err != nil {
		_ = tx.Rollback()
		return fmt.Errorf("clickhouse prepare: %w", err)
	}

	for _, r := range batch {
		addr := strings.ToLower(r.Addr)
		var typ string
		if r.IsDeposit {
			typ = "deposit"
		} else {
			typ = "withdrawal"
		}
		if _, err := stmt.ExecContext(ctx, r.ArbiHash, r.Time, addr, typ, r.Usdc, r.Fee); err != nil {
			_ = stmt.Close()
			_ = tx.Rollback()
			return fmt.Errorf("clickhouse exec: %w", err)
		}
	}

	if err := stmt.Close(); err != nil {
		_ = tx.Rollback()
		return fmt.Errorf("clickhouse stmt close: %w", err)
	}
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("clickhouse commit: %w", err)
	}
	return nil
}

// normalizeAddresses lowercases, trims and uniques addresses.
func normalizeAddresses(addresses []string) []string {
	if len(addresses) == 0 {
		return nil
	}
	m := make(map[string]struct{}, len(addresses))
	for _, a := range addresses {
		aa := strings.ToLower(strings.TrimSpace(a))
		if aa == "" {
			continue
		}
		m[aa] = struct{}{}
	}
	out := make([]string, 0, len(m))
	for k := range m {
		out = append(out, k)
	}
	sort.Strings(out)
	return out
}
