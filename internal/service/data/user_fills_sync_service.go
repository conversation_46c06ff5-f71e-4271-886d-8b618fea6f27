package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"go.uber.org/zap"
)

type UserFillsSyncService struct {
	dbClickHouse *sql.DB
	redis        *redis.Client
}

func NewUserFillsSyncService() *UserFillsSyncService {
	return &UserFillsSyncService{
		dbClickHouse: global.GVA_DB_CLICKHOUSE,
		redis:        global.GVA_REDIS,
	}
}

// GetUserFillsRedisKey returns the Redis key for user's latest 40 records
// Used for userfills cache, using user id as the key
func GetUserFillsRedisKey(userID string) string {
	return fmt.Sprintf("user_fills:list:%s", userID)
}

type UserLatestFill struct {
	ID            int64      `json:"id"`
	UserAddress   string     `json:"user_address"`
	Hash          string     `json:"hash"`
	Tid           int64      `json:"tid"`
	Coin          string     `json:"coin"`
	Px            string     `json:"px"`
	Sz            string     `json:"sz"`
	Side          string     `json:"side"`
	Time          int64      `json:"time"`
	StartPosition string     `json:"start_position"`
	Direction     string     `json:"direction"`
	ClosedPnl     string     `json:"closed_pnl"`
	Oid           int64      `json:"oid"`
	Crossed       int8       `json:"crossed"`
	Fee           string     `json:"fee"`
	FeeToken      string     `json:"fee_token"`
	TradeDate     string     `json:"trade_date"`
	TradeType     string     `json:"trade_type"`
	ProcessedAt   *time.Time `json:"processed_at"`
}

// ExecuteSync queries all users' latest 40 records every 5 seconds and stores them in Redis
func (s *UserFillsSyncService) ExecuteSync() {
	logger := global.GVA_LOG

	if s.dbClickHouse == nil {
		logger.Error("ClickHouse not initialized, skipping user fills sync")
		return
	}

	if s.redis == nil {
		logger.Error("Redis not initialized, skipping user fills sync")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Query all users' id and user_address from users table
	type userInfo struct {
		ID          string
		UserAddress string
	}
	query := "SELECT toString(id) as id, user_address FROM users WHERE user_address IS NOT NULL AND user_address != ''"
	rows, err := s.dbClickHouse.QueryContext(ctx, query)
	if err != nil {
		logger.Error("Failed to query ClickHouse users", zap.Error(err))
		return
	}
	defer rows.Close()

	var users []userInfo
	for rows.Next() {
		var u userInfo
		var userAddress sql.NullString
		var userID sql.NullString
		if err := rows.Scan(&userID, &userAddress); err != nil {
			logger.Warn("Failed to scan user id and user_address", zap.Error(err))
			continue
		}
		if userID.Valid && userID.String != "" && userAddress.Valid && userAddress.String != "" {
			u.ID = userID.String
			u.UserAddress = userAddress.String
			users = append(users, u)
		}
	}
	if err := rows.Err(); err != nil {
		logger.Error("Failed to iterate user list", zap.Error(err))
		return
	}

	logger.Debug("Found users from users table", zap.Int("user_count", len(users)))

	// Query the latest 40 records for each user and store them in Redis
	// If a user has no data in ClickHouse's node_fills table, skip caching
	cachedCount := 0
	updatedCount := 0
	skippedCount := 0
	for _, u := range users {
		// Check if there is data, only cache if data exists
		latestFill, err := s.getUserLatestFillFromClickHouse(ctx, u.UserAddress)
		if err != nil {
			logger.Warn("Failed to query user latest fill from ClickHouse",
				zap.String("user_id", u.ID),
				zap.String("user_address", u.UserAddress),
				zap.Error(err))
			continue
		}
		// Skip if no data
		if latestFill == nil {
			skippedCount++
			continue
		}

		// Check if data is up to date, decide whether to do incremental update or full query
		updated, err := s.updateUserFillsCache(ctx, u.ID, u.UserAddress, latestFill.ID)
		if err != nil {
			logger.Warn("Failed to update user fills cache",
				zap.String("user_id", u.ID),
				zap.String("user_address", u.UserAddress),
				zap.Error(err))
			continue
		}
		if updated {
			updatedCount++
		}
		cachedCount++
	}

	logger.Info("User fills cache sync completed",
		zap.Int("total_users", len(users)),
		zap.Int("cached_users", cachedCount),
		zap.Int("updated_users", updatedCount),
		zap.Int("skipped_users", skippedCount))
}

// getUserFillsFromClickHouse queries user's latest 40 records from ClickHouse
func (s *UserFillsSyncService) getUserFillsFromClickHouse(ctx context.Context, userAddress string) ([]UserLatestFill, error) {
	query := `
		SELECT 
			id, user_address, hash, tid, coin, 
			toString(px) as px, toString(sz) as sz, side, time,
			toString(start_position) as start_position, direction, 
			toString(closed_pnl) as closed_pnl, oid, crossed,
			toString(fee) as fee, fee_token, trade_date, trade_type, processed_at
		FROM node_fills
		WHERE user_address = ?
		ORDER BY id DESC
		LIMIT 40
	`

	rows, err := s.dbClickHouse.QueryContext(ctx, query, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to query user fills from ClickHouse: %w", err)
	}
	defer rows.Close()

	var fills []UserLatestFill
	for rows.Next() {
		var fill UserLatestFill
		var processedAtNullable sql.NullTime
		var userAddr, hash, coin, px, sz, side, startPos, direction, closedPnl, feeToken, tradeDate, tradeType sql.NullString
		var tid, oid sql.NullInt64
		var crossed sql.NullInt32
		var fee sql.NullString

		err := rows.Scan(
			&fill.ID,
			&userAddr,
			&hash,
			&tid,
			&coin,
			&px,
			&sz,
			&side,
			&fill.Time,
			&startPos,
			&direction,
			&closedPnl,
			&oid,
			&crossed,
			&fee,
			&feeToken,
			&tradeDate,
			&tradeType,
			&processedAtNullable,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fill row: %w", err)
		}

		if userAddr.Valid {
			fill.UserAddress = userAddr.String
		}
		if hash.Valid {
			fill.Hash = hash.String
		}
		if tid.Valid {
			fill.Tid = tid.Int64
		}
		if coin.Valid {
			fill.Coin = coin.String
		}
		if px.Valid {
			fill.Px = px.String
		}
		if sz.Valid {
			fill.Sz = sz.String
		}
		if side.Valid {
			fill.Side = side.String
		}
		if startPos.Valid {
			fill.StartPosition = startPos.String
		}
		if direction.Valid {
			fill.Direction = direction.String
		}
		if closedPnl.Valid {
			fill.ClosedPnl = closedPnl.String
		}
		if oid.Valid {
			fill.Oid = oid.Int64
		}
		if crossed.Valid {
			fill.Crossed = int8(crossed.Int32)
		}
		if fee.Valid {
			fill.Fee = fee.String
		}
		if feeToken.Valid {
			fill.FeeToken = feeToken.String
		}
		if tradeDate.Valid {
			fill.TradeDate = tradeDate.String
		}
		if tradeType.Valid {
			fill.TradeType = tradeType.String
		}
		if processedAtNullable.Valid {
			fill.ProcessedAt = &processedAtNullable.Time
		}

		fills = append(fills, fill)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate fills: %w", err)
	}

	return fills, nil
}

// getUserLatestFillFromClickHouse queries user's latest record from ClickHouse for ID comparison
func (s *UserFillsSyncService) getUserLatestFillFromClickHouse(ctx context.Context, userAddress string) (*UserLatestFill, error) {
	query := `
		SELECT 
			id, user_address, hash, tid, coin, 
			toString(px) as px, toString(sz) as sz, side, time,
			toString(start_position) as start_position, direction, 
			toString(closed_pnl) as closed_pnl, oid, crossed,
			toString(fee) as fee, fee_token, trade_date, trade_type, processed_at
		FROM node_fills
		WHERE user_address = ?
		ORDER BY id DESC
		LIMIT 1
	`

	var fill UserLatestFill
	var processedAtNullable sql.NullTime
	var userAddr, hash, coin, px, sz, side, startPos, direction, closedPnl, feeToken, tradeDate, tradeType sql.NullString
	var tid, oid sql.NullInt64
	var crossed sql.NullInt32
	var fee sql.NullString

	err := s.dbClickHouse.QueryRowContext(ctx, query, userAddress).Scan(
		&fill.ID,
		&userAddr,
		&hash,
		&tid,
		&coin,
		&px,
		&sz,
		&side,
		&fill.Time,
		&startPos,
		&direction,
		&closedPnl,
		&oid,
		&crossed,
		&fee,
		&feeToken,
		&tradeDate,
		&tradeType,
		&processedAtNullable,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to query user latest fill from ClickHouse: %w", err)
	}

	if userAddr.Valid {
		fill.UserAddress = userAddr.String
	}
	if hash.Valid {
		fill.Hash = hash.String
	}
	if tid.Valid {
		fill.Tid = tid.Int64
	}
	if coin.Valid {
		fill.Coin = coin.String
	}
	if px.Valid {
		fill.Px = px.String
	}
	if sz.Valid {
		fill.Sz = sz.String
	}
	if side.Valid {
		fill.Side = side.String
	}
	if startPos.Valid {
		fill.StartPosition = startPos.String
	}
	if direction.Valid {
		fill.Direction = direction.String
	}
	if closedPnl.Valid {
		fill.ClosedPnl = closedPnl.String
	}
	if oid.Valid {
		fill.Oid = oid.Int64
	}
	if crossed.Valid {
		fill.Crossed = int8(crossed.Int32)
	}
	if fee.Valid {
		fill.Fee = fee.String
	}
	if feeToken.Valid {
		fill.FeeToken = feeToken.String
	}
	if tradeDate.Valid {
		fill.TradeDate = tradeDate.String
	}
	if tradeType.Valid {
		fill.TradeType = tradeType.String
	}
	if processedAtNullable.Valid {
		fill.ProcessedAt = &processedAtNullable.Time
	}

	return &fill, nil
}

// updateUserFillsCache checks if data is up to date, performs incremental update if not, or full query and store in Redis if up to date
// Returns true if update was performed, false if data is already up to date
func (s *UserFillsSyncService) updateUserFillsCache(ctx context.Context, userID string, userAddress string, latestID int64) (bool, error) {
	logger := global.GVA_LOG
	redisKey := GetUserFillsRedisKey(userID)

	// Get existing data from Redis
	val, err := s.redis.Get(ctx, redisKey).Result()
	var cachedFills []UserLatestFill
	var cachedLatestID int64 = 0

	if err == nil {
		// Data exists in Redis, parse and get the latest ID
		if err := json.Unmarshal([]byte(val), &cachedFills); err == nil && len(cachedFills) > 0 {
			// Data is sorted by id DESC, the first one is the latest
			cachedLatestID = cachedFills[0].ID
		}
	} else if err != redis.Nil {
		// Redis error (not "key not found"), log it
		logger.Warn("Failed to get cached data from Redis",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.Error(err))
	}

	// Compare IDs to determine if data is up to date
	if cachedLatestID >= latestID {
		// Data is already up to date, no update needed
		logger.Debug("User fills cache is up to date",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.Int64("cached_latest_id", cachedLatestID),
			zap.Int64("clickhouse_latest_id", latestID))
		return false, nil
	}

	// Data is not up to date, update needed
	logger.Debug("User fills cache needs update",
		zap.String("user_id", userID),
		zap.String("user_address", userAddress),
		zap.Int64("cached_latest_id", cachedLatestID),
		zap.Int64("clickhouse_latest_id", latestID))

	// If cached data exists, perform incremental update
	if cachedLatestID > 0 && len(cachedFills) > 0 {
		// Query incremental data (ID greater than cached latest ID)
		incrementalFills, err := s.getUserIncrementalFillsFromClickHouse(ctx, userAddress, cachedLatestID)
		if err != nil {
			logger.Warn("Failed to query incremental fills from ClickHouse",
				zap.String("user_id", userID),
				zap.String("user_address", userAddress),
				zap.Error(err))
			// Incremental query failed, fall back to full query
			return s.saveUserFillsToRedis(ctx, userID, userAddress)
		}

		// Merge incremental data to the front of cached data (because incremental data is the latest)
		mergedFills := append(incrementalFills, cachedFills...)

		// Keep at most 40 records
		if len(mergedFills) > 40 {
			mergedFills = mergedFills[:40]
		}

		// Store in Redis
		data, err := json.Marshal(mergedFills)
		if err != nil {
			logger.Warn("Failed to serialize merged fills",
				zap.String("user_id", userID),
				zap.String("user_address", userAddress),
				zap.Error(err))
			return false, err
		}

		if err := s.redis.Set(ctx, redisKey, data, 0).Err(); err != nil {
			logger.Warn("Failed to save merged fills to Redis",
				zap.String("user_id", userID),
				zap.String("user_address", userAddress),
				zap.String("redis_key", redisKey),
				zap.Error(err))
			return false, err
		}

		logger.Debug("Incrementally updated user fills cache",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.Int("incremental_count", len(incrementalFills)),
			zap.Int("total_count", len(mergedFills)))
		return true, nil
	}

	// No cached data, perform full query
	return s.saveUserFillsToRedis(ctx, userID, userAddress)
}

// getUserIncrementalFillsFromClickHouse queries user's incremental data from ClickHouse (ID greater than specified ID)
func (s *UserFillsSyncService) getUserIncrementalFillsFromClickHouse(ctx context.Context, userAddress string, fromID int64) ([]UserLatestFill, error) {
	query := `
		SELECT 
			id, user_address, hash, tid, coin, 
			toString(px) as px, toString(sz) as sz, side, time,
			toString(start_position) as start_position, direction, 
			toString(closed_pnl) as closed_pnl, oid, crossed,
			toString(fee) as fee, fee_token, trade_date, trade_type, processed_at
		FROM node_fills
		WHERE user_address = ? AND id > ?
		ORDER BY id DESC
		LIMIT 40
	`

	rows, err := s.dbClickHouse.QueryContext(ctx, query, userAddress, fromID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user incremental fills from ClickHouse: %w", err)
	}
	defer rows.Close()

	var fills []UserLatestFill
	for rows.Next() {
		var fill UserLatestFill
		var processedAtNullable sql.NullTime
		var userAddr, hash, coin, px, sz, side, startPos, direction, closedPnl, feeToken, tradeDate, tradeType sql.NullString
		var tid, oid sql.NullInt64
		var crossed sql.NullInt32
		var fee sql.NullString

		err := rows.Scan(
			&fill.ID,
			&userAddr,
			&hash,
			&tid,
			&coin,
			&px,
			&sz,
			&side,
			&fill.Time,
			&startPos,
			&direction,
			&closedPnl,
			&oid,
			&crossed,
			&fee,
			&feeToken,
			&tradeDate,
			&tradeType,
			&processedAtNullable,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fill row: %w", err)
		}

		if userAddr.Valid {
			fill.UserAddress = userAddr.String
		}
		if hash.Valid {
			fill.Hash = hash.String
		}
		if tid.Valid {
			fill.Tid = tid.Int64
		}
		if coin.Valid {
			fill.Coin = coin.String
		}
		if px.Valid {
			fill.Px = px.String
		}
		if sz.Valid {
			fill.Sz = sz.String
		}
		if side.Valid {
			fill.Side = side.String
		}
		if startPos.Valid {
			fill.StartPosition = startPos.String
		}
		if direction.Valid {
			fill.Direction = direction.String
		}
		if closedPnl.Valid {
			fill.ClosedPnl = closedPnl.String
		}
		if oid.Valid {
			fill.Oid = oid.Int64
		}
		if crossed.Valid {
			fill.Crossed = int8(crossed.Int32)
		}
		if fee.Valid {
			fill.Fee = fee.String
		}
		if feeToken.Valid {
			fill.FeeToken = feeToken.String
		}
		if tradeDate.Valid {
			fill.TradeDate = tradeDate.String
		}
		if tradeType.Valid {
			fill.TradeType = tradeType.String
		}
		if processedAtNullable.Valid {
			fill.ProcessedAt = &processedAtNullable.Time
		}

		fills = append(fills, fill)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate fills: %w", err)
	}

	return fills, nil
}

// saveUserFillsToRedis saves user's latest 40 records to Redis (full query)
// Returns true if update was performed
func (s *UserFillsSyncService) saveUserFillsToRedis(ctx context.Context, userID string, userAddress string) (bool, error) {
	logger := global.GVA_LOG

	if userID == "" || userAddress == "" {
		logger.Debug("User id or address is empty, skipping Redis save",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress))
		return false, nil
	}

	fills, err := s.getUserFillsFromClickHouse(ctx, userAddress)
	if err != nil {
		logger.Warn("Failed to query user fills from ClickHouse",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.Error(err))
		return false, err
	}

	if len(fills) == 0 {
		logger.Debug("No fills found for user, skipping Redis save",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress))
		return false, nil
	}

	data, err := json.Marshal(fills)
	if err != nil {
		logger.Warn("Failed to serialize user fills",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.Error(err))
		return false, err
	}

	redisKey := GetUserFillsRedisKey(userID)
	if err := s.redis.Set(ctx, redisKey, data, 0).Err(); err != nil {
		logger.Warn("Failed to save user fills to Redis",
			zap.String("user_id", userID),
			zap.String("user_address", userAddress),
			zap.String("redis_key", redisKey),
			zap.Error(err))
		return false, err
	}

	logger.Debug("Saved user fills to Redis",
		zap.String("user_id", userID),
		zap.String("user_address", userAddress),
		zap.String("redis_key", redisKey),
		zap.Int("fill_count", len(fills)))
	return true, nil
}
