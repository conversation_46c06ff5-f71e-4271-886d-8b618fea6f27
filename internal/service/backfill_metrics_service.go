package service

import (
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// BackfillMetrics tracks backfill job performance and statistics
type BackfillMetrics struct {
	mutex sync.RWMutex

	// Job execution metrics
	JobExecutions map[string]*JobExecutionMetrics `json:"job_executions"`

	// Global metrics
	TotalExecutions    int64     `json:"total_executions"`
	TotalSuccesses     int64     `json:"total_successes"`
	TotalFailures      int64     `json:"total_failures"`
	TotalKlinesFetched int64     `json:"total_klines_fetched"`
	LastResetTime      time.Time `json:"last_reset_time"`
}

// JobExecutionMetrics tracks metrics for a specific job
type JobExecutionMetrics struct {
	JobID           string        `json:"job_id"`
	Timeframe       string        `json:"timeframe"`
	ExecutionCount  int64         `json:"execution_count"`
	SuccessCount    int64         `json:"success_count"`
	FailureCount    int64         `json:"failure_count"`
	TotalKlines     int64         `json:"total_klines"`
	TotalDuration   time.Duration `json:"total_duration"`
	AverageDuration time.Duration `json:"average_duration"`
	LastExecution   time.Time     `json:"last_execution"`
	LastSuccess     time.Time     `json:"last_success"`
	LastFailure     time.Time     `json:"last_failure"`
	LastError       string        `json:"last_error,omitempty"`

	// Performance metrics
	MinDuration time.Duration `json:"min_duration"`
	MaxDuration time.Duration `json:"max_duration"`

	// Error tracking
	ErrorTypes map[string]int64 `json:"error_types"`
}

// BackfillMetricsService manages backfill metrics collection and reporting
type BackfillMetricsService struct {
	metrics *BackfillMetrics

	// Configuration for memory management
	maxErrorTypesPerJob int // Maximum number of error types to track per job
	maxJobExecutions    int // Maximum number of job executions to keep in memory
}

// NewBackfillMetricsService creates a new metrics service
func NewBackfillMetricsService() *BackfillMetricsService {
	return &BackfillMetricsService{
		metrics: &BackfillMetrics{
			JobExecutions: make(map[string]*JobExecutionMetrics),
			LastResetTime: time.Now(),
		},
		maxErrorTypesPerJob: 50,  // Limit error types per job to prevent unbounded growth
		maxJobExecutions:    100, // Limit total job executions in memory
	}
}

// RecordJobStart records the start of a job execution
func (s *BackfillMetricsService) RecordJobStart(jobID, timeframe string) {
	s.metrics.mutex.Lock()
	defer s.metrics.mutex.Unlock()

	if _, exists := s.metrics.JobExecutions[jobID]; !exists {
		s.metrics.JobExecutions[jobID] = &JobExecutionMetrics{
			JobID:      jobID,
			Timeframe:  timeframe,
			ErrorTypes: make(map[string]int64),
		}
	}

	job := s.metrics.JobExecutions[jobID]
	job.ExecutionCount++
	job.LastExecution = time.Now()

	s.metrics.TotalExecutions++

	global.GVA_LOG.Debug("Recorded job start",
		zap.String("job_id", jobID),
		zap.String("timeframe", timeframe),
		zap.Int64("execution_count", job.ExecutionCount))
}

// RecordJobSuccess records a successful job execution
func (s *BackfillMetricsService) RecordJobSuccess(jobID string, duration time.Duration, klinesCount int) {
	s.metrics.mutex.Lock()
	defer s.metrics.mutex.Unlock()

	job, exists := s.metrics.JobExecutions[jobID]
	if !exists {
		global.GVA_LOG.Warn("Job not found for success recording", zap.String("job_id", jobID))
		return
	}

	job.SuccessCount++
	job.TotalKlines += int64(klinesCount)
	job.TotalDuration += duration
	job.LastSuccess = time.Now()
	job.LastError = "" // Clear last error on success

	// Update duration statistics
	if job.MinDuration == 0 || duration < job.MinDuration {
		job.MinDuration = duration
	}
	if duration > job.MaxDuration {
		job.MaxDuration = duration
	}

	// Calculate average duration
	if job.SuccessCount > 0 {
		job.AverageDuration = job.TotalDuration / time.Duration(job.SuccessCount)
	}

	s.metrics.TotalSuccesses++
	s.metrics.TotalKlinesFetched += int64(klinesCount)

	global.GVA_LOG.Info("Recorded job success",
		zap.String("job_id", jobID),
		zap.Duration("duration", duration),
		zap.Int("klines_count", klinesCount),
		zap.Int64("total_successes", job.SuccessCount))
}

// RecordJobFailure records a failed job execution
func (s *BackfillMetricsService) RecordJobFailure(jobID string, duration time.Duration, err error) {
	s.metrics.mutex.Lock()
	defer s.metrics.mutex.Unlock()

	job, exists := s.metrics.JobExecutions[jobID]
	if !exists {
		global.GVA_LOG.Warn("Job not found for failure recording", zap.String("job_id", jobID))
		return
	}

	job.FailureCount++
	job.LastFailure = time.Now()
	job.LastError = err.Error()

	// Track error types with size limit to prevent unbounded growth
	errorType := "UNKNOWN"
	if backfillErr, ok := err.(*BackfillError); ok {
		errorType = backfillErr.Type
	}

	// Limit the number of error types per job to prevent memory growth
	if len(job.ErrorTypes) >= s.maxErrorTypesPerJob {
		if _, exists := job.ErrorTypes[errorType]; !exists {
			// Remove the oldest error type (simple approach: remove first one found)
			for oldErrorType := range job.ErrorTypes {
				delete(job.ErrorTypes, oldErrorType)
				global.GVA_LOG.Debug("Removed old error type to make room for new one",
					zap.String("job_id", jobID),
					zap.String("removed_error_type", oldErrorType),
					zap.String("new_error_type", errorType))
				break
			}
		}
	}

	job.ErrorTypes[errorType]++

	s.metrics.TotalFailures++

	global.GVA_LOG.Error("Recorded job failure",
		zap.String("job_id", jobID),
		zap.Duration("duration", duration),
		zap.String("error_type", errorType),
		zap.Error(err),
		zap.Int64("total_failures", job.FailureCount))
}

// GetJobMetrics returns metrics for a specific job
func (s *BackfillMetricsService) GetJobMetrics(jobID string) (*JobExecutionMetrics, bool) {
	s.metrics.mutex.RLock()
	defer s.metrics.mutex.RUnlock()

	job, exists := s.metrics.JobExecutions[jobID]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	jobCopy := *job
	jobCopy.ErrorTypes = make(map[string]int64)
	for k, v := range job.ErrorTypes {
		jobCopy.ErrorTypes[k] = v
	}

	return &jobCopy, true
}

// GetAllMetrics returns all metrics
func (s *BackfillMetricsService) GetAllMetrics() *BackfillMetrics {
	s.metrics.mutex.RLock()
	defer s.metrics.mutex.RUnlock()

	// Create a deep copy
	metricsCopy := &BackfillMetrics{
		JobExecutions:      make(map[string]*JobExecutionMetrics),
		TotalExecutions:    s.metrics.TotalExecutions,
		TotalSuccesses:     s.metrics.TotalSuccesses,
		TotalFailures:      s.metrics.TotalFailures,
		TotalKlinesFetched: s.metrics.TotalKlinesFetched,
		LastResetTime:      s.metrics.LastResetTime,
	}

	for jobID, job := range s.metrics.JobExecutions {
		jobCopy := *job
		jobCopy.ErrorTypes = make(map[string]int64)
		for k, v := range job.ErrorTypes {
			jobCopy.ErrorTypes[k] = v
		}
		metricsCopy.JobExecutions[jobID] = &jobCopy
	}

	return metricsCopy
}

// GetSummaryStats returns summary statistics
func (s *BackfillMetricsService) GetSummaryStats() map[string]interface{} {
	s.metrics.mutex.RLock()
	defer s.metrics.mutex.RUnlock()

	successRate := float64(0)
	if s.metrics.TotalExecutions > 0 {
		successRate = float64(s.metrics.TotalSuccesses) / float64(s.metrics.TotalExecutions) * 100
	}

	return map[string]interface{}{
		"total_executions":     s.metrics.TotalExecutions,
		"total_successes":      s.metrics.TotalSuccesses,
		"total_failures":       s.metrics.TotalFailures,
		"success_rate_percent": successRate,
		"total_klines_fetched": s.metrics.TotalKlinesFetched,
		"active_jobs":          len(s.metrics.JobExecutions),
		"uptime":               time.Since(s.metrics.LastResetTime),
		"last_reset_time":      s.metrics.LastResetTime,
	}
}

// ResetMetrics resets all metrics
func (s *BackfillMetricsService) ResetMetrics() {
	s.metrics.mutex.Lock()
	defer s.metrics.mutex.Unlock()

	s.metrics.JobExecutions = make(map[string]*JobExecutionMetrics)
	s.metrics.TotalExecutions = 0
	s.metrics.TotalSuccesses = 0
	s.metrics.TotalFailures = 0
	s.metrics.TotalKlinesFetched = 0
	s.metrics.LastResetTime = time.Now()

	global.GVA_LOG.Info("Backfill metrics reset")
}

// LogPeriodicSummary logs a periodic summary of metrics
func (s *BackfillMetricsService) LogPeriodicSummary() {
	stats := s.GetSummaryStats()

	global.GVA_LOG.Info("Backfill metrics summary",
		zap.Any("stats", stats))

	// Log individual job performance
	s.metrics.mutex.RLock()
	for jobID, job := range s.metrics.JobExecutions {
		if job.ExecutionCount > 0 {
			global.GVA_LOG.Info("Job performance summary",
				zap.String("job_id", jobID),
				zap.String("timeframe", job.Timeframe),
				zap.Int64("executions", job.ExecutionCount),
				zap.Int64("successes", job.SuccessCount),
				zap.Int64("failures", job.FailureCount),
				zap.Duration("avg_duration", job.AverageDuration),
				zap.Int64("total_klines", job.TotalKlines))
		}
	}
	s.metrics.mutex.RUnlock()
}

// PerformPeriodicCleanup performs periodic cleanup to prevent memory growth
func (s *BackfillMetricsService) PerformPeriodicCleanup() {
	s.metrics.mutex.Lock()
	defer s.metrics.mutex.Unlock()

	// If we have too many job executions, remove the oldest ones
	if len(s.metrics.JobExecutions) > s.maxJobExecutions {
		// Find jobs with oldest last execution times
		type jobAge struct {
			jobID         string
			lastExecution time.Time
		}

		var jobs []jobAge
		for jobID, job := range s.metrics.JobExecutions {
			jobs = append(jobs, jobAge{
				jobID:         jobID,
				lastExecution: job.LastExecution,
			})
		}

		// Sort by last execution time (oldest first)
		for i := 0; i < len(jobs)-1; i++ {
			for j := i + 1; j < len(jobs); j++ {
				if jobs[i].lastExecution.After(jobs[j].lastExecution) {
					jobs[i], jobs[j] = jobs[j], jobs[i]
				}
			}
		}

		// Remove oldest jobs until we're under the limit
		toRemove := len(s.metrics.JobExecutions) - s.maxJobExecutions
		for i := 0; i < toRemove && i < len(jobs); i++ {
			delete(s.metrics.JobExecutions, jobs[i].jobID)
			global.GVA_LOG.Debug("Removed old job metrics to prevent memory growth",
				zap.String("job_id", jobs[i].jobID),
				zap.Time("last_execution", jobs[i].lastExecution))
		}

		global.GVA_LOG.Info("Performed metrics cleanup",
			zap.Int("removed_jobs", toRemove),
			zap.Int("remaining_jobs", len(s.metrics.JobExecutions)))
	}

	// Clean up error types that are too old or numerous
	for jobID, job := range s.metrics.JobExecutions {
		if len(job.ErrorTypes) > s.maxErrorTypesPerJob {
			// Keep only the most frequent error types
			type errorCount struct {
				errorType string
				count     int64
			}

			var errors []errorCount
			for errorType, count := range job.ErrorTypes {
				errors = append(errors, errorCount{
					errorType: errorType,
					count:     count,
				})
			}

			// Sort by count (highest first)
			for i := 0; i < len(errors)-1; i++ {
				for j := i + 1; j < len(errors); j++ {
					if errors[i].count < errors[j].count {
						errors[i], errors[j] = errors[j], errors[i]
					}
				}
			}

			// Keep only the top error types
			newErrorTypes := make(map[string]int64)
			for i := 0; i < s.maxErrorTypesPerJob && i < len(errors); i++ {
				newErrorTypes[errors[i].errorType] = errors[i].count
			}

			removedCount := len(job.ErrorTypes) - len(newErrorTypes)
			job.ErrorTypes = newErrorTypes

			if removedCount > 0 {
				global.GVA_LOG.Debug("Cleaned up error types for job",
					zap.String("job_id", jobID),
					zap.Int("removed_error_types", removedCount),
					zap.Int("remaining_error_types", len(newErrorTypes)))
			}
		}
	}
}
