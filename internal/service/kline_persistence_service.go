package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"go.uber.org/zap"

	"github.com/redis/go-redis/v9"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

// BatchKlineData represents kline data for a specific symbol/timeframe combination
type BatchKlineData struct {
	Symbol     string
	Timeframe  model.KlineTimeframe
	Klines     []RedisKline
	Timestamps []int64 // For cleanup tracking
}

// TimeframeGroup represents grouped klines by timeframe for batch processing
type TimeframeGroup struct {
	Timeframe model.KlineTimeframe
	Klines    []*model.Kline
	Metadata  []BatchKlineData // For cleanup tracking
}

// KlinePersistenceService handles persisting klines from Redis to ClickHouse
type KlinePersistenceService struct {
	clickhouse      *sql.DB
	realtimeService *RealtimeKlineService
	klineService    *KlineService
}

// NewKlinePersistenceService creates a new kline persistence service
func NewKlinePersistenceService() *KlinePersistenceService {
	return &KlinePersistenceService{
		clickhouse:      global.GVA_DB_CLICKHOUSE,
		realtimeService: NewRealtimeKlineService(),
		klineService:    NewKlineService(),
	}
}

// roundVolume rounds volume values to exactly 2 decimal places
func roundVolume(volume float64) float64 {
	return math.Round(volume*100) / 100
}

// PersistCompletedKlines reads completed klines from Redis and persists them to ClickHouse
// OPTIMIZED: Uses batch processing pattern for maximum performance
func (k *KlinePersistenceService) PersistCompletedKlines(ctx context.Context) error {
	startTime := time.Now()
	global.GVA_LOG.Info("Starting kline persistence job (excludes current active klines)")

	// Performance metrics tracking
	var (
		batchReadDuration    time.Duration
		processingDuration   time.Duration
		batchWriteDuration   time.Duration
		cleanupDuration      time.Duration
		redisOperations      int
		clickhouseOperations int
	)

	// Step 1: BATCH READ - Get all completed klines (excludes current active klines)
	batchReadStart := time.Now()
	allKlinesData, err := k.batchReadAllCompletedKlines(ctx)
	batchReadDuration = time.Since(batchReadStart)
	redisOperations = 1 // Single pipeline operation

	if err != nil {
		return fmt.Errorf("failed to batch read completed klines: %w", err)
	}

	if len(allKlinesData) == 0 {
		global.GVA_LOG.Info("No completed klines found for persistence",
			zap.Duration("batch_read_duration", batchReadDuration),
			zap.Duration("total_duration", time.Since(startTime)))
		return nil
	}

	// Step 2: IN-MEMORY PROCESSING - Group and prepare data
	processingStart := time.Now()
	timeframeGroups, totalKlines := k.groupKlinesByTimeframe(allKlinesData)
	processingDuration = time.Since(processingStart)

	global.GVA_LOG.Info("Batch read completed",
		zap.Int("total_klines_found", totalKlines),
		zap.Int("symbol_timeframe_combinations", len(allKlinesData)),
		zap.Duration("batch_read_duration", batchReadDuration))

	if totalKlines == 0 {
		global.GVA_LOG.Info("No klines to process after grouping",
			zap.Duration("processing_duration", processingDuration),
			zap.Duration("total_duration", time.Since(startTime)))
		return nil
	}

	global.GVA_LOG.Info("Starting in-memory processing",
		zap.Int("total_klines_to_process", totalKlines),
		zap.Int("timeframe_groups", len(timeframeGroups)),
		zap.Duration("processing_duration", processingDuration))

	// Step 3: BATCH WRITE - Bulk insert to ClickHouse
	batchWriteStart := time.Now()
	global.GVA_LOG.Info("Starting batch write to ClickHouse",
		zap.Int("timeframe_groups", len(timeframeGroups)),
		zap.Int("total_klines", totalKlines))

	totalPersisted, err := k.batchInsertAllKlines(ctx, timeframeGroups)
	batchWriteDuration = time.Since(batchWriteStart)
	clickhouseOperations = len(timeframeGroups) // One operation per timeframe

	if err != nil {
		return fmt.Errorf("failed to batch insert klines: %w", err)
	}

	// Step 4: BATCH CLEANUP - Remove persisted klines from Redis
	cleanupStart := time.Now()
	global.GVA_LOG.Info("Starting batch cleanup", zap.Int("total_persisted", totalPersisted))
	if err := k.batchCleanupPersistedKlines(ctx, allKlinesData); err != nil {
		global.GVA_LOG.Error("Failed to cleanup persisted klines from Redis", zap.Error(err))
		// Don't return error here as the data is already persisted
	}
	cleanupDuration = time.Since(cleanupStart)
	redisOperations++ // Add cleanup pipeline operation

	totalDuration := time.Since(startTime)

	// Calculate performance metrics
	klinesPerSecond := float64(totalKlines) / totalDuration.Seconds()
	avgKlinesPerTimeframe := float64(totalKlines) / float64(len(timeframeGroups))

	global.GVA_LOG.Info("Kline persistence job completed successfully",
		zap.Int("total_klines_found", totalKlines),
		zap.Int("total_persisted", totalPersisted),
		zap.Int("timeframe_groups_processed", len(timeframeGroups)),
		zap.Int("symbol_timeframe_combinations", len(allKlinesData)),
		// Performance metrics
		zap.Duration("total_duration", totalDuration),
		zap.Duration("batch_read_duration", batchReadDuration),
		zap.Duration("processing_duration", processingDuration),
		zap.Duration("batch_write_duration", batchWriteDuration),
		zap.Duration("cleanup_duration", cleanupDuration),
		// Database operation efficiency
		zap.Int("redis_operations", redisOperations),
		zap.Int("clickhouse_operations", clickhouseOperations),
		zap.Int("total_database_operations", redisOperations+clickhouseOperations),
		// Throughput metrics
		zap.Float64("klines_per_second", klinesPerSecond),
		zap.Float64("avg_klines_per_timeframe", avgKlinesPerTimeframe),
		// Efficiency ratios
		zap.Float64("batch_read_percentage", (batchReadDuration.Seconds()/totalDuration.Seconds())*100),
		zap.Float64("processing_percentage", (processingDuration.Seconds()/totalDuration.Seconds())*100),
		zap.Float64("batch_write_percentage", (batchWriteDuration.Seconds()/totalDuration.Seconds())*100),
		zap.Float64("cleanup_percentage", (cleanupDuration.Seconds()/totalDuration.Seconds())*100))

	return nil
}

// batchReadAllCompletedKlines reads all completed klines from Redis using batch operations
func (k *KlinePersistenceService) batchReadAllCompletedKlines(ctx context.Context) ([]BatchKlineData, error) {
	// Get all symbols with kline data
	symbols, err := k.realtimeService.GetAllSymbolsWithKlines(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get symbols with klines: %w", err)
	}

	if len(symbols) == 0 {
		return nil, nil
	}

	timeframes := model.GetAllTimeframes()
	global.GVA_LOG.Debug("Batch reading completed klines (excluding current active klines)",
		zap.Int("symbols", len(symbols)),
		zap.Int("timeframes", len(timeframes)))

	// Calculate current bucket time for each timeframe to exclude active klines
	now := time.Now()
	timeframeBuckets := make(map[model.KlineTimeframe]int64)
	for _, timeframe := range timeframes {
		currentBucket := timeframe.TruncateTime(now)
		timeframeBuckets[timeframe] = currentBucket.UnixMilli()
	}

	// Use Redis pipeline to read all completed klines in batch
	pipe := global.GVA_REDIS.Pipeline()

	// Track commands and their metadata
	type commandMetadata struct {
		symbol    string
		timeframe model.KlineTimeframe
		cmdIndex  int
	}

	commandMap := make(map[int]commandMetadata)
	cmdIndex := 0

	// Add all ZRangeByScore commands to pipeline
	for _, symbol := range symbols {
		for _, timeframe := range timeframes {
			key := fmt.Sprintf("kline:%s:%s", symbol, string(timeframe))

			// Get all klines except the current active one
			maxScore := float64(timeframeBuckets[timeframe]) - 1

			pipe.ZRangeByScore(ctx, key, &redis.ZRangeBy{
				Min: "-inf",
				Max: fmt.Sprintf("%f", maxScore),
			})

			commandMap[cmdIndex] = commandMetadata{
				symbol:    symbol,
				timeframe: timeframe,
				cmdIndex:  cmdIndex,
			}
			cmdIndex++
		}
	}

	// Execute pipeline
	results, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to execute Redis pipeline: %w", err)
	}

	// Process results
	var allKlinesData []BatchKlineData

	for i, result := range results {
		metadata := commandMap[i]

		if result.Err() != nil {
			global.GVA_LOG.Warn("Failed to get klines for symbol/timeframe",
				zap.String("symbol", metadata.symbol),
				zap.String("timeframe", string(metadata.timeframe)),
				zap.Error(result.Err()))
			continue
		}

		// Parse Redis result
		stringSliceCmd, ok := result.(*redis.StringSliceCmd)
		if !ok {
			global.GVA_LOG.Warn("Unexpected result type from Redis pipeline",
				zap.String("symbol", metadata.symbol),
				zap.String("timeframe", string(metadata.timeframe)))
			continue
		}

		rawKlines := stringSliceCmd.Val()
		if len(rawKlines) == 0 {
			continue // No completed klines for this symbol/timeframe
		}

		// Parse klines and timestamps
		var klines []RedisKline
		var timestamps []int64

		for _, rawKline := range rawKlines {
			var redisKline RedisKline
			if err := json.Unmarshal([]byte(rawKline), &redisKline); err != nil {
				global.GVA_LOG.Warn("Failed to unmarshal kline data",
					zap.String("symbol", metadata.symbol),
					zap.String("timeframe", string(metadata.timeframe)),
					zap.Error(err))
				continue
			}

			klines = append(klines, redisKline)
			timestamps = append(timestamps, redisKline.Timestamp)
		}

		if len(klines) > 0 {
			allKlinesData = append(allKlinesData, BatchKlineData{
				Symbol:     metadata.symbol,
				Timeframe:  metadata.timeframe,
				Klines:     klines,
				Timestamps: timestamps,
			})
		}
	}

	return allKlinesData, nil
}

// groupKlinesByTimeframe groups klines by timeframe for efficient batch processing
func (k *KlinePersistenceService) groupKlinesByTimeframe(allKlinesData []BatchKlineData) (map[model.KlineTimeframe]*TimeframeGroup, int) {
	timeframeGroups := make(map[model.KlineTimeframe]*TimeframeGroup)
	totalKlines := 0

	for _, batchData := range allKlinesData {
		if len(batchData.Klines) == 0 {
			continue
		}

		// Get or create timeframe group
		group := timeframeGroups[batchData.Timeframe]
		if group == nil {
			group = &TimeframeGroup{
				Timeframe: batchData.Timeframe,
				Klines:    make([]*model.Kline, 0),
				Metadata:  make([]BatchKlineData, 0),
			}
			timeframeGroups[batchData.Timeframe] = group
		}

		// Convert Redis klines to model klines
		for _, redisKline := range batchData.Klines {
			kline := k.realtimeService.ConvertRedisKlineToModel(redisKline)
			group.Klines = append(group.Klines, kline)
		}

		// Track metadata for cleanup
		group.Metadata = append(group.Metadata, batchData)
		totalKlines += len(batchData.Klines)
	}

	// Sort klines within each timeframe for optimal ClickHouse insertion
	// ClickHouse performs best when data is sorted by primary key (symbol, timestamp)
	for _, group := range timeframeGroups {
		sort.Slice(group.Klines, func(i, j int) bool {
			// First sort by symbol for better compression and query performance
			if group.Klines[i].Symbol != group.Klines[j].Symbol {
				return group.Klines[i].Symbol < group.Klines[j].Symbol
			}
			// Then sort by timestamp for optimal time-series insertion
			return group.Klines[i].Timestamp.Before(group.Klines[j].Timestamp)
		})
	}

	return timeframeGroups, totalKlines
}

// batchInsertAllKlines performs optimized batch insertion of all klines to ClickHouse
func (k *KlinePersistenceService) batchInsertAllKlines(ctx context.Context, timeframeGroups map[model.KlineTimeframe]*TimeframeGroup) (int, error) {
	totalPersisted := 0

	for timeframe, group := range timeframeGroups {
		if len(group.Klines) == 0 {
			continue
		}

		global.GVA_LOG.Debug("Batch inserting klines for timeframe",
			zap.String("timeframe", string(timeframe)),
			zap.Int("kline_count", len(group.Klines)))

		// Use optimized native batch insert
		if err := k.nativeBatchInsertKlines(ctx, group.Klines, timeframe); err != nil {
			return totalPersisted, fmt.Errorf("failed to batch insert klines for timeframe %s: %w", timeframe, err)
		}

		totalPersisted += len(group.Klines)
	}

	return totalPersisted, nil
}

// nativeBatchInsertKlines performs native ClickHouse batch insert using the v2 driver
func (k *KlinePersistenceService) nativeBatchInsertKlines(ctx context.Context, klines []*model.Kline, timeframe model.KlineTimeframe) error {
	if len(klines) == 0 {
		return nil
	}

	tableName := timeframe.GetTableName()

	// Use ClickHouse native batch insert with the v2 driver
	// This is much more efficient than individual row inserts
	query := fmt.Sprintf(`
		INSERT INTO %s (timestamp, symbol, open, high, low, close, volume, trades_count)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, tableName)

	// Begin transaction for atomic batch insert
	tx, err := k.clickhouse.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Prepare statement for batch insert
	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare batch insert statement: %w", err)
	}
	defer stmt.Close()

	// Execute batch insert - all rows in single transaction
	for _, kline := range klines {
		_, err := stmt.ExecContext(ctx,
			kline.Timestamp,
			kline.Symbol,
			kline.Open,
			kline.High,
			kline.Low,
			kline.Close,
			roundVolume(kline.Volume), // Round volume to 2 decimal places
			kline.TradesCount,
		)
		if err != nil {
			return fmt.Errorf("failed to execute batch insert for kline: %w", err)
		}
	}

	// Commit the entire batch
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit batch insert transaction: %w", err)
	}

	global.GVA_LOG.Debug("Successfully batch inserted klines",
		zap.String("timeframe", string(timeframe)),
		zap.Int("count", len(klines)))

	return nil
}

// batchCleanupPersistedKlines removes persisted klines from Redis using pipeline operations
func (k *KlinePersistenceService) batchCleanupPersistedKlines(ctx context.Context, allKlinesData []BatchKlineData) error {
	if len(allKlinesData) == 0 {
		return nil
	}

	// Use Redis pipeline for all cleanup operations
	pipe := global.GVA_REDIS.Pipeline()
	totalOperations := 0

	for _, batchData := range allKlinesData {
		if len(batchData.Timestamps) == 0 {
			continue
		}

		key := fmt.Sprintf("kline:%s:%s", batchData.Symbol, string(batchData.Timeframe))

		// Add cleanup commands to pipeline for each timestamp
		for _, timestamp := range batchData.Timestamps {
			score := float64(timestamp)
			pipe.ZRemRangeByScore(ctx, key, fmt.Sprintf("%f", score), fmt.Sprintf("%f", score))
			totalOperations++
		}
	}

	if totalOperations == 0 {
		global.GVA_LOG.Debug("No cleanup operations needed")
		return nil
	}

	global.GVA_LOG.Debug("Executing batch cleanup operations",
		zap.Int("total_operations", totalOperations))

	// Execute all cleanup operations in single network call
	results, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute Redis cleanup pipeline: %w", err)
	}

	// Check for any failed operations
	failedOperations := 0
	for i, result := range results {
		if result.Err() != nil {
			global.GVA_LOG.Warn("Redis cleanup operation failed",
				zap.Int("operation_index", i),
				zap.Error(result.Err()))
			failedOperations++
		}
	}

	if failedOperations > 0 {
		global.GVA_LOG.Warn("Some Redis cleanup operations failed",
			zap.Int("failed_operations", failedOperations),
			zap.Int("total_operations", totalOperations))
		// Don't return error as the main persistence succeeded
	} else {
		global.GVA_LOG.Debug("All Redis cleanup operations completed successfully",
			zap.Int("total_operations", totalOperations))
	}

	return nil
}

// persistSymbolTimeframe persists completed klines for a specific symbol and timeframe
func (k *KlinePersistenceService) persistSymbolTimeframe(ctx context.Context, symbol string, timeframe model.KlineTimeframe) (int, error) {
	// Get completed klines from Redis
	redisKlines, err := k.realtimeService.GetCompletedKlinesForPersistence(ctx, symbol, timeframe)
	if err != nil {
		return 0, fmt.Errorf("failed to get completed klines from Redis: %w", err)
	}

	if len(redisKlines) == 0 {
		return 0, nil // No completed klines to persist
	}

	// Convert Redis klines to model klines
	klines := make([]*model.Kline, 0, len(redisKlines))
	timestamps := make([]int64, 0, len(redisKlines))

	for _, redisKline := range redisKlines {
		kline := k.realtimeService.ConvertRedisKlineToModel(redisKline)
		klines = append(klines, kline)
		timestamps = append(timestamps, redisKline.Timestamp)
	}

	// Sort klines by timestamp for optimal ClickHouse insertion
	sort.Slice(klines, func(i, j int) bool {
		return klines[i].Timestamp.Before(klines[j].Timestamp)
	})

	// Batch insert into ClickHouse
	if err := k.batchInsertKlines(ctx, klines, timeframe); err != nil {
		return 0, fmt.Errorf("failed to batch insert klines: %w", err)
	}

	// Remove persisted klines from Redis
	if err := k.realtimeService.RemovePersistedKlines(ctx, symbol, timeframe, timestamps); err != nil {
		global.GVA_LOG.Error("Failed to remove persisted klines from Redis",
			zap.String("symbol", symbol),
			zap.String("timeframe", string(timeframe)),
			zap.Error(err))
		// Don't return error here as the data is already persisted
	}

	global.GVA_LOG.Debug("Persisted klines for symbol/timeframe",
		zap.String("symbol", symbol),
		zap.String("timeframe", string(timeframe)),
		zap.Int("count", len(klines)))

	return len(klines), nil
}

// batchInsertKlines performs batch insertion of klines into ClickHouse
func (k *KlinePersistenceService) batchInsertKlines(ctx context.Context, klines []*model.Kline, timeframe model.KlineTimeframe) error {
	if len(klines) == 0 {
		return nil
	}

	tableName := timeframe.GetTableName()

	// Prepare batch insert query
	query := fmt.Sprintf(`
		INSERT INTO %s (timestamp, symbol, open, high, low, close, volume, trades_count)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, tableName)

	// Prepare statement
	stmt, err := k.clickhouse.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare batch insert statement: %w", err)
	}
	defer stmt.Close()

	// Begin transaction for batch insert
	tx, err := k.clickhouse.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Execute batch insert
	for _, kline := range klines {
		_, err := tx.StmtContext(ctx, stmt).ExecContext(ctx,
			kline.Timestamp,
			kline.Symbol,
			kline.Open,
			kline.High,
			kline.Low,
			kline.Close,
			roundVolume(kline.Volume), // Round volume to 2 decimal places
			kline.TradesCount,
		)
		if err != nil {
			return fmt.Errorf("failed to execute batch insert: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit batch insert transaction: %w", err)
	}

	return nil
}

// ExecutePersistenceTask executes the persistence job as a scheduled task
// This function is designed to be called by the cron scheduler
func (k *KlinePersistenceService) ExecutePersistenceTask() {
	ctx := context.Background()
	if err := k.PersistCompletedKlines(ctx); err != nil {
		global.GVA_LOG.Error("Failed to persist klines in scheduled task", zap.Error(err))
	}
}

// GetPersistenceStatistics returns statistics about the persistence process
func (k *KlinePersistenceService) GetPersistenceStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get Redis statistics
	redisStats, err := k.realtimeService.GetKlineStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis statistics: %w", err)
	}
	stats["redis"] = redisStats

	// Get ClickHouse statistics for each timeframe
	clickhouseStats := make(map[string]interface{})
	timeframes := model.GetAllTimeframes()

	for _, timeframe := range timeframes {
		tableName := timeframe.GetTableName()

		// Count total records in ClickHouse table
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)
		var count int64
		if err := k.clickhouse.QueryRowContext(ctx, query).Scan(&count); err != nil {
			global.GVA_LOG.Error("Failed to get ClickHouse count",
				zap.String("table", tableName),
				zap.Error(err))
			count = -1 // Indicate error
		}
		clickhouseStats[string(timeframe)] = count
	}
	stats["clickhouse"] = clickhouseStats

	return stats, nil
}
