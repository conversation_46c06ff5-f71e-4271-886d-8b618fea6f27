package global

import (
	"database/sql"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
)

var (
	GVA_DB_TIDB       *gorm.DB
	GVA_DB_CLICKHOUSE *sql.DB       // ClickHouse connection for OLAP workloads
	GVA_REDIS         *redis.Client // Redis connection for caching and real-time data
	GVA_NATS          interface{}   // NATS client for message queue (will be *nats.NATSClient)
	GVA_CONFIG        config.Server // Global configuration
	GVA_VP            *viper.Viper  // Viper instance
	GVA_LOG           *zap.Logger   // Logger instance
)
