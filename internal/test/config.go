package test

import (
	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

type TestConfig struct {
	Server *config.Server
}

func SetupTestConfig() *TestConfig {

	testConfig := &TestConfig{
		Server: &config.Server{
			JWT: config.JWT{
				SigningKey:  "test-secret-key",
				ExpiresTime: "24h",
				BufferTime:  "1h",
				Issuer:      "dex-hypertrader-test",
			},
			System: config.System{
				Env:                "test",
				Addr:               8080,
				RouterPrefix:       "/api/dex-hypertrader",
				GraphqlPrefix:      "/api/dex-hypertrader/graphql",
				AdminGraphqlPrefix: "/api/dex-hypertrader/admin/graphql",
			},

			ClickHouse: config.ClickHouse{
				Host:         "localhost",
				Port:         "8123",
				Database:     "test_db",
				Username:     "default",
				Password:     "",
				MaxIdleConns: 2,
				MaxOpenConns: 5,
				MaxLifetime:  "1h",
				Secure:       false,
				SkipVerify:   true,
			},
		},
	}

	global.GVA_CONFIG = *testConfig.Server
	return testConfig
}

func CleanupTestDB() {
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_DB_CLICKHOUSE.Close()
		global.GVA_DB_CLICKHOUSE = nil
	}
}
