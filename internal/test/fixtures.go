package test

import (
	"time"

	"github.com/google/uuid"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

type TestFixtures struct{}

func NewTestFixtures() *TestFixtures {
	return &TestFixtures{}
}

func (f *TestFixtures) CreateTestUser() *model.User {
	userID := uuid.New()
	email := "<EMAIL>"

	return &model.User{
		ID:        userID,
		Email:     &email,
		Name:      "Test User",
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

func (f *TestFixtures) CreateTestUserWithEmail(email string) *model.User {
	user := f.CreateTestUser()
	user.Email = &email
	return user
}

// Test fixtures for TradeAnalytics, UserAnalytics, and MarketData removed as tables are not needed
