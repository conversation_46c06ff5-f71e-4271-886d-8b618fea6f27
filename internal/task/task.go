package task

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

type TaskScheduler struct {
	cron       *cron.Cron
	tasks      map[string]cron.EntryID
	cancelFunc context.CancelFunc
}

func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		cron:  cron.New(cron.WithSeconds()),
		tasks: make(map[string]cron.EntryID),
	}
}

func (s *TaskScheduler) Register(id string, expr string, job func()) error {
	if _, exists := s.tasks[id]; exists {
		return fmt.Errorf("task ID %s already exists", id)
	}

	entryID, err := s.cron.AddFunc(expr, job)
	if err != nil {
		return fmt.Errorf("register task failed: %w", err)
	}

	s.tasks[id] = entryID
	global.GVA_LOG.Info("Registered task", 
		zap.String("id", id), 
		zap.String("cron", expr))
	return nil
}

func (s *TaskScheduler) Start() {
	s.cron.Start()
	global.GVA_LOG.Info("Task scheduler started")
}

func (s *TaskScheduler) Stop() {
	ctx := s.cron.Stop()
	<-ctx.Done()
	global.GVA_LOG.Info("Task scheduler stopped")
}

func (s *TaskScheduler) SetCancelFunc(cancel context.CancelFunc) {
	s.cancelFunc = cancel
}

func (s *TaskScheduler) RunWithSignal(ctx context.Context) {
	go s.Start()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		global.GVA_LOG.Info("Received exit signal, stopping scheduler...")
		s.Stop()
		if s.cancelFunc != nil {
			s.cancelFunc()
		}
	case <-ctx.Done():
		global.GVA_LOG.Info("Context cancelled, stopping scheduler...")
		s.Stop()
	}
}
