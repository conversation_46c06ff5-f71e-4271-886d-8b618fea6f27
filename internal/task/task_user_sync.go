package task

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"go.uber.org/zap"
)

type UserEvent string

const (
	UserNewWalletSubject UserEvent = "dex.user.wallet.new"

	DexUserStream               = "dex_user"
	DexUserSubject              = "dex.user.wallet.new"
	AggregationSyncUserConsumer = "AggregationSyncUserConsumer"
)

type UserWalletInfo struct {
	ID              string    `json:"id"`
	WalletAddress   string    `json:"walletAddress"`
	WalletAccountID uuid.UUID `json:"walletAccountId"`
	WalletID        uuid.UUID `json:"walletId"`
	CreatedAt       string    `json:"createdAt"`
}

type UserNewWalletEvent struct {
	UserID  uuid.UUID        `json:"userId"`
	Wallets []UserWalletInfo `json:"wallets"`
}

func ConsumeUserSyncInfoEvent(msg *nats.Msg) error {
	logger := global.GVA_LOG

	var event UserNewWalletEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		logger.Error("failed to unmarshal user event", zap.Error(err))
		return fmt.Errorf("failed to unmarshal user event: %w", err)
	}

	ctx := context.Background()

	// Create or update the main user record
	_, err := CreateOrUpdateUser(ctx, event.UserID)
	if err != nil {
		logger.Error("failed to create or update user",
			zap.String("user_id", event.UserID.String()),
			zap.Error(err),
		)
		return err
	}

	logger.Info("Successfully processed user sync event",
		zap.String("user_id", event.UserID.String()),
		zap.Int("wallet_count", len(event.Wallets)),
	)

	return nil
}

// CreateOrUpdateUser creates a user if it doesn't exist, or updates it if it does
func CreateOrUpdateUser(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	db := global.GVA_DB_CLICKHOUSE
	if db == nil {
		return nil, fmt.Errorf("ClickHouse connection is not initialized")
	}

	// First, check if the user already exists
	var count int
	checkQuery := "SELECT count() FROM users WHERE toString(id) = ?"
	err := db.QueryRowContext(ctx, checkQuery, userID.String()).Scan(&count)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to check user existence: %w", err)
	}

	now := time.Now()

	if count > 0 {
		// User exists, update the updated_at field using DELETE + INSERT
		// First get the existing user data
		var email sql.NullString
		var name, status string
		var createdAt time.Time
		getQuery := "SELECT email, name, status, created_at FROM users WHERE toString(id) = ?"
		err := db.QueryRowContext(ctx, getQuery, userID.String()).Scan(&email, &name, &status, &createdAt)
		if err != nil {
			return nil, fmt.Errorf("failed to get existing user: %w", err)
		}

		// Delete the old record
		deleteQuery := "ALTER TABLE users DELETE WHERE toString(id) = ?"
		_, err = db.ExecContext(ctx, deleteQuery, userID.String())
		if err != nil {
			return nil, fmt.Errorf("failed to delete old user record: %w", err)
		}

		// Insert the updated record
		insertQuery := `
			INSERT INTO users (id, email, name, status, created_at, updated_at, deleted_at)
			VALUES (toUUID(?), ?, ?, ?, ?, ?, ?)
		`
		_, err = db.ExecContext(ctx, insertQuery,
			userID.String(),
			email,
			name,
			status,
			createdAt,
			now,                        // new updated_at
			sql.NullTime{Valid: false}, // deleted_at (nullable)
		)
		if err != nil {
			return nil, fmt.Errorf("failed to insert updated user: %w", err)
		}

		global.GVA_LOG.Debug("Updated existing user",
			zap.String("user_id", userID.String()),
		)
	} else {
		// User doesn't exist, create new user
		insertQuery := `
			INSERT INTO users (id, email, name, status, created_at, updated_at, deleted_at)
			VALUES (toUUID(?), ?, ?, ?, ?, ?, ?)
		`
		defaultName := ""
		defaultStatus := "active"
		_, err := db.ExecContext(ctx, insertQuery,
			userID.String(),
			sql.NullString{Valid: false}, // email (nullable)
			defaultName,
			defaultStatus,
			now,
			now,
			sql.NullTime{Valid: false}, // deleted_at (nullable)
		)
		if err != nil {
			return nil, fmt.Errorf("failed to insert user: %w", err)
		}

		global.GVA_LOG.Debug("Created new user",
			zap.String("user_id", userID.String()),
		)
	}

	// Return the user model
	user := &model.User{
		ID:        userID,
		Email:     nil,
		Name:      "",
		Status:    "active",
		CreatedAt: now,
		UpdatedAt: now,
		DeletedAt: nil,
	}

	return user, nil
}
