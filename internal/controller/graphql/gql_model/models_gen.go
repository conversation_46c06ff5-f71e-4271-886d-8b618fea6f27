// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
)

type Ohlc struct {
	Timestamp int     `json:"timestamp"`
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
}

type OHLCRequest struct {
	Symbol    string           `json:"symbol"`
	Interval  OHLCIntervalEnum `json:"interval"`
	Timestamp int              `json:"timestamp"`
	IsForward bool             `json:"isForward"`
	Limit     int              `json:"limit"`
}

type OHLCResponse struct {
	Symbol string  `json:"symbol"`
	Data   []*Ohlc `json:"data"`
}

type Query struct {
}

type OHLCIntervalEnum string

const (
	OHLCIntervalEnumOneMinute      OHLCIntervalEnum = "ONE_MINUTE"
	OHLCIntervalEnumThreeMinutes   OHLCIntervalEnum = "THREE_MINUTES"
	OHLCIntervalEnumFiveMinutes    OHLCIntervalEnum = "FIVE_MINUTES"
	OHLCIntervalEnumFifteenMinutes OHLCIntervalEnum = "FIFTEEN_MINUTES"
	OHLCIntervalEnumThirtyMinutes  OHLCIntervalEnum = "THIRTY_MINUTES"
	OHLCIntervalEnumOneHour        OHLCIntervalEnum = "ONE_HOUR"
	OHLCIntervalEnumTwoHours       OHLCIntervalEnum = "TWO_HOURS"
	OHLCIntervalEnumFourHours      OHLCIntervalEnum = "FOUR_HOURS"
	OHLCIntervalEnumEightHours     OHLCIntervalEnum = "EIGHT_HOURS"
	OHLCIntervalEnumTwelveHours    OHLCIntervalEnum = "TWELVE_HOURS"
	OHLCIntervalEnumOneDay         OHLCIntervalEnum = "ONE_DAY"
	OHLCIntervalEnumThreeDays      OHLCIntervalEnum = "THREE_DAYS"
	OHLCIntervalEnumOneWeek        OHLCIntervalEnum = "ONE_WEEK"
	OHLCIntervalEnumOneMonth       OHLCIntervalEnum = "ONE_MONTH"
)

var AllOHLCIntervalEnum = []OHLCIntervalEnum{
	OHLCIntervalEnumOneMinute,
	OHLCIntervalEnumThreeMinutes,
	OHLCIntervalEnumFiveMinutes,
	OHLCIntervalEnumFifteenMinutes,
	OHLCIntervalEnumThirtyMinutes,
	OHLCIntervalEnumOneHour,
	OHLCIntervalEnumTwoHours,
	OHLCIntervalEnumFourHours,
	OHLCIntervalEnumEightHours,
	OHLCIntervalEnumTwelveHours,
	OHLCIntervalEnumOneDay,
	OHLCIntervalEnumThreeDays,
	OHLCIntervalEnumOneWeek,
	OHLCIntervalEnumOneMonth,
}

func (e OHLCIntervalEnum) IsValid() bool {
	switch e {
	case OHLCIntervalEnumOneMinute, OHLCIntervalEnumThreeMinutes, OHLCIntervalEnumFiveMinutes, OHLCIntervalEnumFifteenMinutes, OHLCIntervalEnumThirtyMinutes, OHLCIntervalEnumOneHour, OHLCIntervalEnumTwoHours, OHLCIntervalEnumFourHours, OHLCIntervalEnumEightHours, OHLCIntervalEnumTwelveHours, OHLCIntervalEnumOneDay, OHLCIntervalEnumThreeDays, OHLCIntervalEnumOneWeek, OHLCIntervalEnumOneMonth:
		return true
	}
	return false
}

func (e OHLCIntervalEnum) String() string {
	return string(e)
}

func (e *OHLCIntervalEnum) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = OHLCIntervalEnum(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid OHLCIntervalEnum", str)
	}
	return nil
}

func (e OHLCIntervalEnum) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *OHLCIntervalEnum) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e OHLCIntervalEnum) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
