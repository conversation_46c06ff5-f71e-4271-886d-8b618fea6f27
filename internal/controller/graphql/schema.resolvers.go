package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"
	"fmt"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/controller/graphql/gql_model"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

// Health is the resolver for the health field.
func (r *queryResolver) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

// GetOhlc is the resolver for the getOHLC field.
func (r *queryResolver) GetOhlc(ctx context.Context, input gql_model.OHLCRequest) (*gql_model.OHLCResponse, error) {
	// Convert GraphQL input to service request
	serviceReq := &service.OHLCRequest{
		Symbol:    input.Symbol,
		Interval:  convertGQLIntervalToServiceInterval(input.Interval),
		Timestamp: int64(input.Timestamp),
		IsForward: input.IsForward,
		Limit:     input.Limit,
	}

	// Call the service
	serviceResp, err := r.ohlcService.GetOHLC(ctx, serviceReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get OHLC data: %w", err)
	}

	// Convert service response to GraphQL response
	gqlResp := &gql_model.OHLCResponse{
		Symbol: serviceResp.Symbol,
		Data:   convertServiceOHLCToGQLOHLC(serviceResp.Data),
	}

	return gqlResp, nil
}

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type queryResolver struct{ *Resolver }
