# GraphQL Schema for xbit-hypertrader-go

scalar Time

type Query {
  # Health check query
  health: String!

  # OHLC data query
  getOHLC(input: OHLCRequest!): OHLCResponse!
}

# OHLC Request Input
input OHLCRequest {
  symbol: String!              # Symbol code (e.g., "BTC", "ETH", "SOL")
  interval: OHLCIntervalEnum!  # Timeframe interval
  timestamp: Int!              # Unix timestamp in milliseconds
  isForward: Boolean!          # true: fetch data from timestamp onwards, false: fetch data before timestamp
  limit: Int!                  # Number of candles to return (max: 10000)
}

# OHLC Interval Enum
enum OHLCIntervalEnum {
  ONE_MINUTE      # 1m
  THREE_MINUTES   # 3m
  FIVE_MINUTES    # 5m
  FIFTEEN_MINUTES # 15m
  THIRTY_MINUTES  # 30m
  ONE_HOUR        # 1h
  TWO_HOURS       # 2h
  FOUR_HOURS      # 4h
  EIGHT_HOURS     # 8h
  TWELVE_HOURS    # 12h
  ONE_DAY         # 1d
  THREE_DAYS      # 3d
  ONE_WEEK        # 1w
  ONE_MONTH       # 1M
}

# OHLC Response
type OHLCResponse {
  symbol: String!    # Symbol code
  data: [OHLC!]!    # Array of OHLC candles
}

# OHLC Candle Data
type OHLC {
  timestamp: Int!    # Unix timestamp in milliseconds (candle open time)
  open: Float!       # Opening price
  high: Float!       # Highest price
  low: Float!        # Lowest price
  close: Float!      # Closing price
  volume: Float!     # Trading volume
}
