package graphql

import (
	"github.com/xbit-dex/xbit-hypertrader-go/internal/repo"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

// Resolver is the root GraphQL resolver
type Resolver struct {
	ohlcService service.OHLCServiceInterface
}

// NewRootResolver creates a new root resolver with all service dependencies
func NewRootResolver() *Resolver {
	// Create the service and inject the repository
	ohlcService := service.NewOHLCService()

	// Create repository and inject it into the service
	ohlcRepo := repo.NewOHLCRepository()
	if svc, ok := ohlcService.(*service.OHLCService); ok {
		svc.SetRepository(ohlcRepo)
	}

	return &Resolver{
		ohlcService: ohlcService,
	}
}
