package graphql

import (
	"github.com/xbit-dex/xbit-hypertrader-go/internal/controller/graphql/gql_model"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

// convertGQLIntervalToServiceInterval converts GraphQL interval enum to service interval enum
func convertGQLIntervalToServiceInterval(gqlInterval gql_model.OHLCIntervalEnum) service.OHLCIntervalEnum {
	switch gqlInterval {
	case gql_model.OHLCIntervalEnumOneMinute:
		return service.OHLCIntervalOneMinute
	case gql_model.OHLCIntervalEnumThreeMinutes:
		return service.OHLCIntervalThreeMinutes
	case gql_model.OHLCIntervalEnumFiveMinutes:
		return service.OHLCIntervalFiveMinutes
	case gql_model.OHLCIntervalEnumFifteenMinutes:
		return service.OHLCIntervalFifteenMinutes
	case gql_model.OHLCIntervalEnumThirtyMinutes:
		return service.OHLCIntervalThirtyMinutes
	case gql_model.OHLCIntervalEnumOneHour:
		return service.OHLCIntervalOneHour
	case gql_model.OHLCIntervalEnumTwoHours:
		return service.OHLCIntervalTwoHours
	case gql_model.OHLCIntervalEnumFourHours:
		return service.OHLCIntervalFourHours
	case gql_model.OHLCIntervalEnumEightHours:
		return service.OHLCIntervalEightHours
	case gql_model.OHLCIntervalEnumTwelveHours:
		return service.OHLCIntervalTwelveHours
	case gql_model.OHLCIntervalEnumOneDay:
		return service.OHLCIntervalOneDay
	case gql_model.OHLCIntervalEnumThreeDays:
		return service.OHLCIntervalThreeDays
	case gql_model.OHLCIntervalEnumOneWeek:
		return service.OHLCIntervalOneWeek
	case gql_model.OHLCIntervalEnumOneMonth:
		return service.OHLCIntervalOneMonth
	default:
		return service.OHLCIntervalOneMinute // Default fallback
	}
}

// convertServiceOHLCToGQLOHLC converts service OHLC data to GraphQL OHLC data
func convertServiceOHLCToGQLOHLC(serviceData []*service.OHLCData) []*gql_model.Ohlc {
	var gqlData []*gql_model.Ohlc

	for _, data := range serviceData {
		gqlOHLC := &gql_model.Ohlc{
			Timestamp: int(data.Timestamp),
			Open:      data.Open,
			High:      data.High,
			Low:       data.Low,
			Close:     data.Close,
			Volume:    data.Volume,
		}
		gqlData = append(gqlData, gqlOHLC)
	}

	return gqlData
}
