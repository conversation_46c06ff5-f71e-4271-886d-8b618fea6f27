# Real-time Trading Data Processing System

This document describes the real-time trading data processing system implemented for dex-hypertrader-go.

## Overview

The system implements a three-tier architecture for processing real-time trading data with NATS message queue for resilience:

1. **Tier 1: NATS JetStream (Message Queue)** - Buffers and reliably delivers trading messages
2. **Tier 2: Redis (Real-time Cache)** - Stores live candlestick data with TTL management
3. **Tier 3: <PERSON><PERSON><PERSON>ouse (Persistent Storage)** - <PERSON><PERSON> inserts completed candles every minute

### Data Flow

```
WebSocket Stream → NATS JetStream → Parallel Processing → Redis (Cache) → ClickHouse (Persistence)
```

## Architecture Components

### 1. WebSocket Client Service (`internal/service/websocket_service.go`)
- Connects to trading data stream at `wss://unstable-smart-money.the-x.link/ws`
- Handles automatic reconnection with configurable intervals
- Publishes `user_fill` messages to NATS JetStream for resilient processing
- Implements ping/pong heartbeat mechanism

### 2. NATS Publisher Service (`internal/service/nats_publisher_service.go`)
- Publishes user fill messages to NATS JetStream
- Ensures stream creation and configuration
- Provides message reliability and buffering

### 3. NATS Consumer Service (`internal/service/nats_consumer_service.go`)
- Consumes messages from NATS JetStream in batches
- Processes messages in parallel with limited concurrency
- Implements ACK/NAK for message acknowledgment
- Provides retry logic with exponential backoff

### 2. Redis Caching Layer (`internal/service/redis_kline_service.go`)
- Uses Redis Sorted Sets for efficient time-series data storage
- Implements TTL-based data expiration per timeframe
- Stores candlestick data in JSON format
- Key naming: `kline:{symbol}:{timeframe}`

### 3. Real-time OHLCV Service (`internal/service/realtime_kline_service.go`)
- Processes incoming trade messages
- Calculates OHLCV data for all 14 timeframes simultaneously
- Updates Redis cache in real-time
- Validates trade data before processing

### 4. Persistence Service (`internal/service/kline_persistence_service.go`)
- Runs every minute to persist completed candles
- Batch inserts data into ClickHouse for optimal performance
- Removes persisted data from Redis to manage memory
- Sorts data by partition key before insertion

## Supported Timeframes

| Timeframe | Duration | TTL | Max Candles | Coverage |
|-----------|----------|-----|-------------|----------|
| 1m        | 1 minute | 5 minutes | 5 | 5 minutes |
| 3m        | 3 minutes | 9 minutes | 3 | 9 minutes |
| 5m        | 5 minutes | 15 minutes | 3 | 15 minutes |
| 15m       | 15 minutes | 30 minutes | 2 | 30 minutes |
| 30m       | 30 minutes | 1 hour | 2 | 1 hour |
| 1h        | 1 hour | 2 hours | 2 | 2 hours |
| 2h        | 2 hours | 4 hours | 2 | 4 hours |
| 4h        | 4 hours | 8 hours | 2 | 8 hours |
| 8h        | 8 hours | 16 hours | 2 | 16 hours |
| 12h       | 12 hours | 24 hours | 2 | 1 day |
| 1d        | 1 day | 2 days | 2 | 2 days |
| 3d        | 3 days | 6 days | 2 | 6 days |
| 1W        | 7 days | 14 days | 2 | 2 weeks |
| 1M        | 30 days | 60 days | 2 | 2 months |

## Configuration

### Environment Variables

#### Redis Configuration
```bash
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=fwdA08030FMPAcQcR3Mk7hQRXZ036y4zJ5qODub0dbU0
REDIS_DB=0
REDIS_MAX_IDLE_CONNS=10
REDIS_MAX_OPEN_CONNS=100
REDIS_MAX_LIFETIME=1h
```

#### WebSocket Configuration
```bash
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=5s
WEBSOCKET_PING_INTERVAL=30s
WEBSOCKET_PONG_TIMEOUT=10s
```

## Worker Services

### 1. WebSocket Worker (`cmd/worker_websocket/main.go`)
Processes real-time trading data from WebSocket stream.

**Build:**
```bash
make build-websocket-worker
```

**Run:**
```bash
make run-websocket-worker
# or
./scripts/run.sh local websocket-worker
```

### 2. Scheduler Service (`cmd/scheduler/main.go`)
Runs scheduled tasks including kline persistence every minute using cron expressions.

**Build:**
```bash
make build-scheduler
```

**Run:**
```bash
make run-scheduler
# or
./scripts/run.sh local scheduler
```

## Data Flow

1. **WebSocket Connection**: Worker connects to trading data stream
2. **Message Processing**: `user_fill` messages are parsed and validated
3. **OHLCV Calculation**: Trade data updates candlesticks for all timeframes
4. **Redis Storage**: Updated candles stored in Redis with TTL
5. **Scheduled Persistence**: Cron job runs every minute to move completed candles to ClickHouse
6. **Memory Management**: Persisted candles removed from Redis

## Trade Message Format

```json
{
  "type": "user_fill",
  "data": {
    "index": 498,
    "address": "0x1c1083932ea9a1289ad332573c1af99bb6679ff4",
    "coin": "DOOD",
    "px": "0.007545",
    "sz": "15029.0",
    "side": "B",
    "time": 1760157207621,
    "start_position": "1148495.0",
    "dir": "Open Long",
    "closed_pnl": "0.0",
    "hash": "0xdd7e5e937c8d6e3cdef8042d3f5a640201ec007917808d0e814709e63b814827",
    "oid": 194562408876,
    "crossed": true,
    "fee": "0.028915",
    "tid": 214821782811225,
    "fee_token": "USDC",
    "trade_type": "perpetual"
  }
}
```

## Performance Considerations

- **Redis Pipelining**: Atomic updates across multiple timeframes
- **Connection Pooling**: Optimized for both Redis and ClickHouse
- **Batch Operations**: Efficient ClickHouse insertions
- **Memory Management**: TTL-based Redis cleanup
- **Error Recovery**: Automatic WebSocket reconnection

## Monitoring

The system provides statistics endpoints for monitoring:
- Redis cache statistics
- ClickHouse table counts
- Processing metrics
- Connection status

## Deployment

1. Ensure Redis and ClickHouse are accessible
2. Update environment configuration
3. Build services: `make build-all`
4. Run WebSocket worker for real-time processing: `make run-websocket-worker`
5. Run scheduler for data archival: `make run-scheduler`

Both services can run independently and will automatically handle connection failures and recovery.

## Scheduled Tasks Configuration

The persistence job is configured as a cron task in `config.yaml`:

```yaml
cron-tasks:
  - id: "persist_klines"
    cron: "0 * * * * *"    # Every minute
```

This follows the project's standard pattern for scheduled tasks using the `robfig/cron/v3` library with seconds support.
