# User Sync Feature Testing

## Overview

This document describes how to test the newly implemented user sync feature.

## Implemented Features

### 1. Database Migration

Created ClickHouse user table migration file: `migrations/clickhouse/003_create_users_table.sql`

```sql
CREATE TABLE IF NOT EXISTS users (
    id UUID,
    email Nullable(String),
    name String,
    status String,
    created_at DateTime,
    updated_at DateTime,
    deleted_at Nullable(DateTime)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (id, created_at);
```

### 2. Worker Program

Implemented `cmd/worker_sync_user/main.go` with the following features:

- Subscribe to `dex.user.>` topics on NATS JetStream
- Use persistent consumer `AgentSyncUserConsumer`
- Handle user new wallet events
- Support message retry (up to 10 times)
- Automatic ACK/NACK processing

### 3. Business Logic

Implemented `internal/task/task_user_sync.go` with the following features:

- `ConsumeUserSyncInfoEvent`: Process NATS messages
- `CreateOrUpdateUser`: Create or update users
  - Automatically detect if user exists
  - Create new user if not exists
  - Update `updated_at` field if exists

## Testing Steps

### Prerequisites

1. Ensure ClickHouse is running
2. Ensure NATS is running
3. Ensure Redis is running

### Run Migration

```bash
make clickhouse-migrate-up
```

### Run Test Program

```bash
go run cmd/test_sync_user/main.go
```

The test program will execute the following tests:

1. **Test 1: Create New User**

   - Generate new UUID
   - Call `CreateOrUpdateUser`
   - Verify user is successfully created

2. **Test 2: Update Existing User**

   - Use the same UUID
   - Verify user update operation
   - Note: `updated_at` timestamp may not change immediately due to ClickHouse's asynchronous ALTER operations

3. **Test 3: Process NATS Event**

   - Create test event
   - Publish to NATS
   - Wait for worker to process (if running)

4. **Test 4: Query All Users**

   - Display list of users in database

5. **Test 5: Test Table Structure**
   - Verify users table exists
   - Display table column information

### Run Worker

```bash
go run cmd/worker_sync_user/main.go
```

Worker will:

- Connect to NATS JetStream
- Subscribe to `dex.user.>` topics
- Wait and process user events

### Manual Test Event

Create a test script or use the following JSON to publish to NATS:

```json
{
  "userId": "550e8400-e29b-41d4-a716-************",
  "wallets": [
    {
      "id": "wallet-1",
      "walletAddress": "0x1234567890abcdef",
      "walletAccountId": "550e8400-e29b-41d4-a716-************",
      "walletId": "550e8400-e29b-41d4-a716-************",
      "createdAt": "2025-10-28T10:00:00Z"
    }
  ]
}
```

## File Modification Summary

### New Files

1. `migrations/clickhouse/003_create_users_table.sql` - User table migration
2. `cmd/test_sync_user/main.go` - Test program
3. `docs/USER_SYNC_TEST.md` - Test documentation

### Modified Files

1. `cmd/worker_sync_user/main.go` - Fixed imports and NATS client usage
2. `internal/task/task_user_sync.go` - Implemented user create/update logic

## Key Features

### Error Handling

- JSON parsing errors
- Database connection errors
- UUID conversion errors

### Performance Optimization

- Use native SQL queries
- Support batch operations
- Asynchronous message processing

### Data Consistency

- Use ClickHouse's ALTER TABLE UPDATE to update records
- Use `toUUID()` and `toString()` to handle UUID types

## Known Issues

1. If migration is already marked as complete but table not created, need to manually create table using:

   ```bash
   cat scripts/create_users_table.sql | curl -X POST "http://localhost:8123/?user=default&password=local-clickhouse-password&database=default" --data-binary @-
   ```

2. ClickHouse's UUID type requires using `toUUID()` function for conversion

3. ClickHouse's `ALTER TABLE UPDATE/DELETE` operations are asynchronous, so timestamp changes may not be immediately visible. This is normal behavior for ClickHouse.

## Quick Start

If the users table doesn't exist after running migrations, manually create it:

```bash
# Create users table
cat scripts/create_users_table.sql | curl -X POST "http://localhost:8123/?user=default&password=local-clickhouse-password&database=default" --data-binary @-

# Run tests
go run cmd/test_sync_user/main.go
```

## Test Results

Expected test results:

```
✅ ClickHouse connection successful
✅ NATS connection successful
✅ User created successfully
✅ User exists in database
✅ User updated successfully
⚠️ Updated timestamp did not change (ClickHouse async operations)
✅ Event published to NATS
✅ Found X users
✅ Users table exists
✅ User sync logic test passed!
```

## Next Steps

1. Add user wallet information storage (if needed)
2. Implement more complete user information queries
3. Add other user information fields (such as nickname, avatar, etc.)
