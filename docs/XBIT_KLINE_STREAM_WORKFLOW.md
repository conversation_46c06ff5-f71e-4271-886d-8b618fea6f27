# Luồng Hoạt Động Tạo Stream xbit-kline

## 📋 Tổng Quan

Tài liệu này mô tả chi tiết luồng hoạt động để tạo ra NATS JetStream stream `xbit-kline` với subject `xbit.kline.raw_user_fills` để xử lý dữ liệu trading real-time cho việc tính toán OHLCV/Kline.

## 🎯 Mục Đích

Stream `xbit-kline` được sử dụng để:
- Nhận và lưu trữ các message `user_fills` từ WebSocket
- Cung cấp dữ liệu cho consumer xử lý OHLCV/Kline
- Đảm bảo persistence và reliability cho dữ liệu trading

## 📊 Thông Tin Stream

### Stream Configuration

```go
Stream Name: "xbit-kline"
Subject: "xbit.kline.raw_user_fills"
Description: "Stream for kline/OHLCV processing"
```

### Stream Settings

| Thu<PERSON><PERSON> tính | Gi<PERSON> trị | <PERSON>ô tả |
|------------|---------|-------|
| **Name** | `xbit-kline` | Tên của stream |
| **Subjects** | `["xbit.kline.raw_user_fills"]` | Subject pattern để match messages |
| **MaxAge** | `24 hours` | Thời gian lưu trữ tối đa |
| **MaxBytes** | `1GB` | Dung lượng lưu trữ tối đa |
| **Storage** | `FileStorage` | Lưu trữ trên disk để đảm bảo persistence |
| **Retention Policy** | `LimitsPolicy` | Chính sách retention dựa trên giới hạn |

## 🔧 Cấu Hình NATS

### File: `config.yaml`

```yaml
nats:
  url: "nats://127.0.0.1:4222"  # NATS server URL
  user: ""                       # Username (optional)
  pass: ""                       # Password (optional)
  use-tls: false                 # Enable TLS
  token: ""                      # Authentication token (optional)
```

### Environment Variables

```bash
NATS_URL=nats://127.0.0.1:4222
NATS_USER=your_username
NATS_PASS=your_password
NATS_USE_TLS=false
NATS_TOKEN=your_token
```

## 🏗️ Kiến Trúc Luồng Dữ Liệu

```
┌─────────────────────────────────────────────────────────────┐
│                    WebSocket Service                         │
│  - Nhận message từ WebSocket                                 │
│  - Detect message type "user_fills"                         │
│  - Queue message vào channel                                 │
└──────────────────────┬──────────────────────────────────────┘
                       │
                       │ Raw WebSocket Message ([]byte)
                       ↓
┌─────────────────────────────────────────────────────────────┐
│              NATS Publisher Service                         │
│  - PublishOptimizedUserFill()                               │
│  - Validate message format                                  │
│  - PublishAsync() to NATS JetStream                        │
└──────────────────────┬──────────────────────────────────────┘
                       │
                       │ Subject: xbit.kline.raw_user_fills
                       ↓
┌─────────────────────────────────────────────────────────────┐
│              NATS JetStream                                  │
│  Stream: xbit-kline                                         │
│  Subject: xbit.kline.raw_user_fills                         │
│  Storage: FileStorage                                       │
│  Retention: 24h / 1GB                                       │
└──────────────────────┬──────────────────────────────────────┘
                       │
                       │ Consumer subscribes
                       ↓
┌─────────────────────────────────────────────────────────────┐
│              NATS Consumer Service                          │
│  - Pull messages from stream                                │
│  - Process for OHLCV calculation                            │
│  - Update Redis/ClickHouse                                  │
└─────────────────────────────────────────────────────────────┘
```

## 📝 Các Bước Triển Khai

### Bước 1: Khởi Tạo NATS Client

**File:** `internal/nats/nats.go`

```go
func InitNatsJetStream(natConfig config.Nats) *NATSClient {
    // 1. Setup TLS config (nếu cần)
    var tlsConfig *tls.Config
    if natConfig.UseTLS {
        tlsConfig = &tls.Config{
            MinVersion: tls.VersionTLS12,
        }
    }

    // 2. Tạo NATS connection options
    natsOptions := []nats.Option{
        nats.Timeout(10 * time.Second),
        nats.MaxReconnects(-1),
        nats.ReconnectWait(2 * time.Second),
        nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
            // Handle disconnect
        }),
        nats.ReconnectHandler(func(nc *nats.Conn) {
            // Handle reconnect
        }),
    }

    // 3. Add authentication (Token hoặc User/Pass)
    if natConfig.Token != "" {
        natsOptions = append(natsOptions, nats.Token(natConfig.Token))
    } else if natConfig.User != "" || natConfig.Pass != "" {
        natsOptions = append(natsOptions, nats.UserInfo(natConfig.User, natConfig.Pass))
    }

    // 4. Connect to NATS
    nc, err := nats.Connect(natConfig.URL, natsOptions...)
    if err != nil {
        // Handle error
    }

    // 5. Tạo JetStream context
    js, err := nc.JetStream()
    if err != nil {
        // Handle error
    }

    return &NATSClient{conn: nc, js: js}
}
```

**File:** `internal/initializer/nats.go`

```go
func InitNATS() {
    natsClient := nats.InitNatsJetStream(global.GVA_CONFIG.Nats)
    global.GVA_NATS = natsClient
    global.GVA_LOG.Info("NATS client initialized successfully")
}
```

### Bước 2: Định Nghĩa Constants

**File:** `internal/model/nats_message.go`

```go
const (
    SubjectRawUserFills = "xbit.kline.raw_user_fills" // Subject cho raw user fills
    StreamKline         = "xbit-kline"                // Tên stream
)
```

### Bước 3: Tạo Stream (Ensure Stream Exists)

**File:** `internal/service/nats_publisher_service.go`

```go
// EnsureStream creates the NATS stream if it doesn't exist
func (n *NATSPublisherService) EnsureStream() error {
    // 1. Kiểm tra stream đã tồn tại chưa
    _, err := n.js.StreamInfo(model.StreamKline)
    if err != nil {
        // 2. Stream chưa tồn tại, tạo mới
        streamConfig := &nats.StreamConfig{
            Name:        model.StreamKline,                    // "xbit-kline"
            Description: "Stream for kline/OHLCV processing",
            Subjects:    []string{model.SubjectRawUserFills},  // ["xbit.kline.raw_user_fills"]
            MaxAge:      24 * time.Hour,                       // 24 giờ
            MaxBytes:    1024 * 1024 * 1024,                   // 1GB
            Storage:     nats.FileStorage,                     // Lưu trên disk
        }

        // 3. Tạo stream
        _, err = n.js.AddStream(streamConfig)
        if err != nil {
            return fmt.Errorf("failed to create stream: %w", err)
        }

        global.GVA_LOG.Info("Created NATS stream successfully",
            zap.String("stream", model.StreamKline),
            zap.String("retention", "LimitsPolicy"),
            zap.String("storage", "FileStorage"))
    }

    return nil
}
```

### Bước 4: Publish Messages

**File:** `internal/service/nats_publisher_service.go`

Có 2 phương thức publish:

#### 4.1. PublishOptimizedUserFill (Recommended - Ultra Low Latency)

```go
// PublishOptimizedUserFill publishes raw user_fills messages directly to NATS
// Ưu tiên tốc độ, publish raw data trực tiếp không parse JSON
func (n *NATSPublisherService) PublishOptimizedUserFill(ctx context.Context, rawData []byte) error {
    // 1. Quick validation: Kiểm tra message có phải user_fills không
    if !bytes.Contains(rawData, []byte(`"type":"user_fills"`)) &&
       !bytes.Contains(rawData, []byte(`"type": "user_fills"`)) {
        return fmt.Errorf("invalid message: does not appear to be user_fills format")
    }

    // 2. Publish raw data trực tiếp (zero-copy)
    pubAck, err := n.js.PublishAsync(model.SubjectRawUserFills, rawData)
    if err != nil {
        return fmt.Errorf("failed to publish raw message to NATS: %w", err)
    }

    // 3. Handle acknowledgment async (non-blocking)
    go func() {
        select {
        case <-pubAck.Ok():
            // Publish thành công
        case err := <-pubAck.Err():
            // Publish thất bại
        case <-time.After(5 * time.Second):
            // Timeout
        }
    }()

    return nil
}
```

#### 4.2. PublishRawUserFill (Alternative - Wrapped Format)

```go
// PublishRawUserFill wraps raw data trong RawWebSocketMessage structure
func (n *NATSPublisherService) PublishRawUserFill(ctx context.Context, rawData []byte) error {
    // 1. Tạo RawWebSocketMessage wrapper
    rawMessage := model.CreateRawWebSocketMessage(rawData)

    // 2. Convert to JSON
    jsonData, err := rawMessage.ToJSON()
    if err != nil {
        return fmt.Errorf("failed to marshal raw message: %w", err)
    }

    // 3. Publish to NATS
    pubAck, err := n.js.PublishAsync(model.SubjectRawUserFills, jsonData)
    if err != nil {
        return fmt.Errorf("failed to publish raw message to NATS: %w", err)
    }

    // 4. Handle acknowledgment
    // ... (tương tự như trên)

    return nil
}
```

### Bước 5: WebSocket Service Integration

**File:** `internal/service/websocket_service.go`

```go
// NewWebSocketService khởi tạo WebSocket service
func NewWebSocketService() *WebSocketService {
    // 1. Khởi tạo NATS Publisher
    natsPublisher := NewNATSPublisherService()

    // 2. Đảm bảo stream tồn tại
    if err := natsPublisher.EnsureStream(); err != nil {
        global.GVA_LOG.Error("Failed to ensure NATS stream", zap.Error(err))
    }

    return &WebSocketService{
        natsPublisher: natsPublisher,
        rawPublishChan: make(chan []byte, 10000), // Buffer channel
        // ... other fields
    }
}

// handleMessage xử lý message từ WebSocket
func (ws *WebSocketService) handleMessage(message []byte) error {
    // 1. Kiểm tra nếu là user_fills message
    if ws.isUserFillMessage(message) {
        // 2. Queue vào channel (non-blocking)
        select {
        case ws.rawPublishChan <- message:
            // Successfully queued
            return nil
        default:
            // Channel full, publish directly
            return ws.natsPublisher.PublishOptimizedUserFill(context.Background(), message)
        }
    }

    // ... handle other message types
    return nil
}

// Worker goroutine xử lý messages từ channel
func (ws *WebSocketService) startSingleWorker(ctx context.Context, workerID int) {
    go func() {
        for {
            select {
            case <-ctx.Done():
                return
            case rawData := <-ws.rawPublishChan:
                // Publish to NATS
                if err := ws.natsPublisher.PublishOptimizedUserFill(ctx, rawData); err != nil {
                    // Handle error
                }
            }
        }
    }()
}
```

## 📦 Message Format

### Raw WebSocket Message Format

Message từ WebSocket có format JSON:

```json
{
  "type": "user_fills",
  "data": [
    [index, coin, px, sz, time, hash],
    [index, coin, px, sz, time, hash],
    ...
  ]
}
```

Ví dụ:

```json
{
  "type": "user_fills",
  "data": [
    [0, "BTC-USD", "50000.5", "0.1", 1699123456789, "0xabc123..."],
    [1, "ETH-USD", "3000.25", "1.5", 1699123456790, "0xdef456..."]
  ]
}
```

### Published Message Format

Khi sử dụng `PublishOptimizedUserFill`, message được publish **trực tiếp** dưới dạng raw bytes (không wrap).

Khi sử dụng `PublishRawUserFill`, message được wrap trong structure:

```json
{
  "message_id": "20240101120000-abc12345",
  "timestamp": "2024-01-01T12:00:00Z",
  "source": "websocket",
  "raw_data": "<base64_encoded_raw_websocket_message>"
}
```

## 🔍 Kiểm Tra Stream

### Get Stream Info

```go
streamInfo, err := n.js.StreamInfo(model.StreamKline)
if err != nil {
    // Stream không tồn tại
}

// Stream info bao gồm:
// - Config: Stream configuration
// - State: Current state (messages, bytes, sequences)
// - Cluster: Cluster information (nếu có)
```

### Get Publish Stats

```go
stats := natsPublisher.GetPublishStats()
// Returns:
// {
//   "stream_name": "xbit-kline",
//   "total_messages": 123456,
//   "total_bytes": 1024000000,
//   "first_seq": 1,
//   "last_seq": 123456
// }
```

## 🚀 Khởi Động Application

### Initialization Order

1. **Load Configuration** (`config.yaml`)
2. **Initialize Logger**
3. **Initialize NATS Client** (`InitNATS()`)
4. **Initialize WebSocket Service** (tự động gọi `EnsureStream()`)
5. **Start WebSocket Connection**
6. **Start Worker Goroutines** để publish messages

### Example Main Function

```go
func main() {
    // 1. Initialize configuration
    global.GVA_VP = initializer.Viper()

    // 2. Initialize logger
    initializer.InitZap()

    // 3. Initialize databases (including NATS)
    initializer.InitDatabases()
    defer initializer.CloseDatabases()

    // 4. Initialize WebSocket Service
    // (EnsureStream được gọi tự động trong NewWebSocketService)
    wsService := service.NewWebSocketService()

    // 5. Connect và start
    ctx := context.Background()
    if err := wsService.Connect(ctx); err != nil {
        log.Fatal(err)
    }

    // 6. Start listening
    wsService.Start(ctx)
}
```

## ⚙️ Tối Ưu Hóa Performance

### 1. Async Publishing

- Sử dụng `PublishAsync()` thay vì `Publish()` để non-blocking
- Handle acknowledgment trong background goroutine

### 2. Batch Processing

- Sử dụng buffered channel để queue messages
- Multiple worker goroutines để xử lý parallel

### 3. Zero-Copy Publishing

- `PublishOptimizedUserFill` publish raw bytes trực tiếp
- Không parse JSON, không wrap message
- Minimal latency

### 4. Channel Management

```go
rawPublishChan: make(chan []byte, 10000)  // Buffer lớn
workerCount: 1                             // Start với 1 worker
maxWorkerCount: 5                          // Scale lên tối đa 5 workers
```

## 🔐 Security & Authentication

### Authentication Options

1. **Token Authentication:**
   ```yaml
   nats:
     token: "your-secret-token"
   ```

2. **Username/Password:**
   ```yaml
   nats:
     user: "username"
     pass: "password"
   ```

3. **TLS:**
   ```yaml
   nats:
     use-tls: true
   ```

## 📈 Monitoring & Logging

### Key Metrics to Monitor

- **Stream State:**
  - Total messages
  - Total bytes
  - First/Last sequence numbers

- **Publishing:**
  - Publish latency
  - Publish success/failure rate
  - Channel utilization

- **Connection:**
  - NATS connection status
  - Reconnection events
  - Disconnection errors

### Logging Examples

```go
// Stream created
global.GVA_LOG.Info("Created NATS stream successfully",
    zap.String("stream", model.StreamKline))

// Message published
global.GVA_LOG.Debug("Published raw user_fills to NATS",
    zap.Int("message_size", len(rawData)),
    zap.String("optimization", "zero-copy-raw-publish"))

// Error handling
global.GVA_LOG.Error("Failed to publish raw user_fills to NATS",
    zap.String("subject", model.SubjectRawUserFills),
    zap.Error(err))
```

## 🐛 Troubleshooting

### Stream Không Tồn Tại

**Error:** `stream not found`

**Solution:**
- Đảm bảo `EnsureStream()` được gọi trước khi publish
- Kiểm tra NATS connection
- Kiểm tra permissions

### Publish Failed

**Error:** `failed to publish raw message to NATS`

**Possible Causes:**
- NATS connection lost
- Subject không match với stream subjects
- Stream đã đạt giới hạn (MaxBytes hoặc MaxAge)

**Solution:**
- Kiểm tra connection status
- Verify subject name: `xbit.kline.raw_user_fills`
- Check stream limits

### High Latency

**Symptoms:** Messages publish chậm

**Solutions:**
- Sử dụng `PublishOptimizedUserFill` thay vì `PublishRawUserFill`
- Tăng số lượng worker goroutines
- Tăng buffer size của channel
- Kiểm tra NATS server performance

## 📚 Tài Liệu Tham Khảo

- [NATS JetStream Documentation](https://docs.nats.io/nats-concepts/jetstream)
- [NATS Go Client](https://github.com/nats-io/nats.go)
- [Stream Configuration](https://docs.nats.io/nats-concepts/jetstream/streams)

## ✅ Checklist Triển Khai

- [ ] Cấu hình NATS trong `config.yaml`
- [ ] Khởi tạo NATS client (`InitNATS()`)
- [ ] Định nghĩa constants (`SubjectRawUserFills`, `StreamKline`)
- [ ] Implement `EnsureStream()` method
- [ ] Implement `PublishOptimizedUserFill()` hoặc `PublishRawUserFill()`
- [ ] Tích hợp với WebSocket service
- [ ] Setup worker goroutines cho async publishing
- [ ] Add logging và monitoring
- [ ] Test stream creation
- [ ] Test message publishing
- [ ] Verify message consumption

## 🎯 Tóm Tắt

1. **Stream Name:** `xbit-kline`
2. **Subject:** `xbit.kline.raw_user_fills`
3. **Storage:** FileStorage (persistent)
4. **Retention:** 24 hours hoặc 1GB
5. **Publishing:** Async, zero-copy, optimized
6. **Initialization:** Tự động tạo stream nếu chưa tồn tại
7. **Message Format:** Raw WebSocket JSON bytes

---

**Lưu ý:** Tài liệu này mô tả implementation hiện tại. Khi triển khai ở repo khác, có thể cần điều chỉnh theo ngôn ngữ và framework sử dụng, nhưng các khái niệm và luồng hoạt động cơ bản vẫn giữ nguyên.

