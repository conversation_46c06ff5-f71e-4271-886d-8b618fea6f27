# Data Flow Diagram: WebSocket → NATS → Redis → <PERSON><PERSON><PERSON>ouse → candleSnapshot

## Overview

The system processes real-time trading data with a data flow from websocket source through the following components:

## Data Flow Details

### 1️⃣ **WebSocket Service**
**File:** `internal/service/websocket_service.go`

```
┌─────────────────────────────────────────────────────┐
│        WebSocket Service (worker_websocket)        │
├─────────────────────────────────────────────────────┤
│  🔌 Connection: wss://unstable-smart-money.the-x.link/ws │
│  📡 Source: Hyperliquid Exchange                   │
│  ⚡ Features: Auto-reconnect, Ping/Pong, Fast-path │
│                                                     │
│  📥 MESSAGE FORMAT:                                │
│  {                                                  │
│    "type": "user_fills",                          │
│    "data": [[index, coin, px, sz, time, hash],   │
│             [index, coin, px, sz, time, hash]],   │
│    "timestamp": 1234567890                         │
│  }                                                  │
│                                                     │
│  ⚡ Processing:                                     │
│     • Fast-path: Zero JSON parsing                │
│     • Channel buffer: 10,000 msgs                 │
│     • Dynamic workers: 1-5 (auto-scale)           │
│     • Async publish to NATS                       │
└─────────────────────────────────────────────────────┘
                     │
                     ▼
```

**Key Features:**
- Auto-reconnect: 3s interval (production)
- Ping/Pong keepalive: 15s interval
- Fast-path: Direct publish without JSON parsing
- Dynamic worker pool: 1-5 workers with auto-scaling
- Channel buffer: 10,000 messages

---

### 2️⃣ **NATS Stream**
**Files:** `internal/service/nats_publisher_service.go`, `internal/model/nats_message.go`

```
┌─────────────────────────────────────────────────────┐
│              NATS Publisher Service                 │
├─────────────────────────────────────────────────────┤
│  📤 Subject: "raw.user.fills"                      │
│                                                     │
│  🔄 Data Transformation:                          │
│     Raw WebSocket Message → NATS Message           │
│                                                     │
│  📦 Message Format:                                │
│  {                                                  │
│    "message_id": "uuid",                          │
│    "timestamp": timestamp,                        │
│    "raw_data": [...raw websocket data...]         │
│  }                                                  │
│                                                     │
│  ✨ Optimization:                                  │
│  - Zero-copy raw publish                           │
│  - Async acknowledgment                           │
└─────────────────────────────────────────────────────┘
                     │
                     ▼
          ┌──────────────────────┐
          │   NATS JetStream     │
          │   raw.user.fills     │
          └──────────────────────┘
                     │
                     ▼
```

---

### 3️⃣ **NATS Consumer Service**
**File:** `internal/service/nats_consumer_service.go`

```
┌─────────────────────────────────────────────────────┐
│      NATS Consumer Service (worker_nats_consumer)   │
├─────────────────────────────────────────────────────┤
│  📥 Subscribe: "raw.user.fills"                    │
│  🔄 Queue Group: "kline-processors"                │
│                                                     │
│  📊 BATCH PROCESSING:                              │
│  1️⃣ Parse messages: Group by symbol               │
│  2️⃣ Batch read OHLCV from Redis                   │
│  3️⃣ Process trades in memory                      │
│  4️⃣ Batch write updates to Redis                  │
│  5️⃣ Acknowledge messages                          │
│                                                     │
│  ⚙️ Config:                                        │
│  - Batch size: 1000 messages                       │
│  - Batch interval: 500ms                          │
│  - Max workers: Dynamic scaling                    │
└─────────────────────────────────────────────────────┘
                     │
                     ▼ (Step 4: Batch Write)
```

**Processing details:**
- Durable consumer with retry logic
- Batch processing for optimal performance
- Parse raw data into TradeData
- Group trades by symbol and timeframe
- Update OHLCV data

---

### 4️⃣ **Redis - Real-time Storage**
**File:** `internal/service/redis_kline_service.go`

```
┌─────────────────────────────────────────────────────┐
│              Redis Sorted Set Storage               │
├─────────────────────────────────────────────────────┤
│  🔑 Key Format: "kline:{symbol}:{timeframe}"       │
│  Example: "kline:BTC-USDT:1m"                       │
│                                                     │
│  📊 Data Structure:                                │
│  - Score: timestamp (milliseconds)                 │
│  - Member: JSON kline data                         │
│                                                     │
│  📝 RedisKline Structure:                         │
│  {                                                  │
│    "timestamp": 1234567890000,                    │
│    "symbol": "BTC-USDT",                           │
│    "open": 50000.0,                                │
│    "high": 50100.0,                                │
│    "low": 49900.0,                                 │
│    "close": 50050.0,                               │
│    "volume": 1.5,                                  │
│    "trades_count": 150,                            │
│    "timeframe": "1m",                              │
│    "open_timestamp": 1234567890100,               │
│    "close_timestamp": 1234567895900,              │
│    ...                                             │
│  }                                                  │
│                                                     │
│  ⏰ TTL Management (Auto-cleanup):                │
│  1m: 5 min, Max 5 candles                         │
│  5m: 15 min, Max 3 candles                        │
│  15m: 30 min, Max 2 candles                       │
│  1h: 2 hours, Max 2 candles                       │
│  ...                                               │
└─────────────────────────────────────────────────────┘
                     │
                     ▼ (Scheduled task)
```

**Storage details:**
- Uses Redis Sorted Sets (ZADD)
- Score = timestamp as index
- Automatically update OHLCV when new trades arrive
- Maintain TTL for auto cleanup

---

### 5️⃣ **ClickHouse - Historical Storage**
**File:** `internal/service/kline_persistence_service.go`

```
┌─────────────────────────────────────────────────────┐
│      Kline Persistence Service (Scheduler)          │
├─────────────────────────────────────────────────────┤
│  ⏰ Scheduled: Every minute (cron job)             │
│                                                     │
│  📊 PROCESS:                                        │
│  1️⃣ Batch read completed klines from Redis        │
│     (Excludes current active candles)              │
│  2️⃣ Group by timeframe                            │
│  3️⃣ Convert RedisKline → Model.Kline              │
│  4️⃣ Batch insert into ClickHouse                  │
│  5️⃣ Batch cleanup from Redis                       │
│                                                     │
│  💾 Tables:                                        │
│  - kline_1m                                        │
│  - kline_3m                                        │
│  - kline_5m                                        │
│  - kline_15m                                       │
│  - kline_30m                                       │
│  - kline_1h, 2h, 4h, 8h, 12h, 1d, 3d, 1w, 1mo    │
│                                                     │
│  🔄 Batch Operations:                              │
│  - Single transaction per timeframe                │
│  - Atomic commits                                   │
│  - Optimized pipeline                               │
└─────────────────────────────────────────────────────┘
                     │
                     ▼ (Historical Data)
```

**Persistence details:**
- Only persist completed candles (not active ones)
- Batch processing for optimization
- Native ClickHouse batch insert
- Atomic transactions
- Cleanup Redis after persistence

---

### 6️⃣ **candleSnapshot API**
**Files:**
- `internal/service/candle_snapshot_service.go`
- `internal/repo/candle_snapshot_repository.go`
- `internal/controller/graphql/schema.resolvers.go`

```
┌─────────────────────────────────────────────────────┐
│            candleSnapshot GraphQL API               │
├─────────────────────────────────────────────────────┤
│  📥 REQUEST:                                        │
│  query {                                            │
│    candleSnapshot(                                 │
│      symbol: "BTC-USDT",                          │
│      interval: 1m,                                │
│      timestamp: 1234567890000,                     │
│      isForward: true,                             │
│      limit: 100                                    │
│    ) {                                             │
│      symbol                                        │
│      data {                                        │
│        timestamp                                   │
│        open                                        │
│        high                                        │
│        low                                         │
│        close                                       │
│        volume                                      │
│      }                                             │
│    }                                               │
│  }                                                  │
│                                                     │
│  🔄 PROCESSING FLOW:                                │
│  1️⃣ Validate request                               │
│  2️⃣ Convert interval → timeframe                   │
│  3️⃣ Query historical data from ClickHouse          │
│     - isForward=true: timestamp >= query_ts        │
│     - isForward=false: timestamp < query_ts       │
│  4️⃣ Get active kline from Redis                    │
│     - Include candle patching logic               │
│     - Fallback mechanisms                          │
│  5️⃣ Combine historical + active data              │
│  6️⃣ Sort and apply limit                           │
│  7️⃣ Handle Redis delay fallback                   │
│                                                     │
│  📤 RESPONSE:                                       │
│  {                                                  │
│    "symbol": "BTC-USDT",                          │
│    "data": [                                       │
│      { "timestamp": ..., "open": ..., ... },      │
│      ...                                           │
│    ]                                               │
│  }                                                  │
└─────────────────────────────────────────────────────┘
```

**Query details:**
- Historical data: ClickHouse (completed candles)
- Active data: Redis (current active candle)
- Combine logic: Avoid duplicates, sort correctly
- Fallback: ClickHouse fallback if Redis unavailable

---

## Overall Diagram

```
┌──────────────┐
│  External    │
│  Hyperliquid │
│  WebSocket   │
└──────┬───────┘
       │
       │ Raw trade data
       ▼
┌─────────────────────────────────────┐
│  🔵 WebSocket Service               │
│  - Receive messages                 │
│  - Fast-path validation             │
│  - Zero-copy publish                │
└──────┬──────────────────────────────┘
       │
       │ Publish raw data
       ▼
┌─────────────────────────────────────┐
│  🔵 NATS JetStream                  │
│  Subject: "raw.user.fills"         │
│  - Message queuing                  │
│  - Durability                        │
└──────┬──────────────────────────────┘
       │
       │ Subscribe & Consume
       ▼
┌─────────────────────────────────────┐
│  🔵 NATS Consumer                   │
│  - Batch processing (1000 msgs)     │
│  - Parse & group trades            │
│  - Update OHLCV                    │
└──────┬──────────────────────────────┘
       │
       │ Batch Write
       ▼
┌─────────────────────────────────────┐
│  🔴 Redis (Real-time)               │
│  Sorted Sets                        │
│  Key: "kline:{symbol}:{timeframe}" │
│  - Current active candles           │
│  - TTL auto-cleanup                 │
└──────┬──────────────────────────────┘
       │                    │
       │                    ▼
       │         ┌────────────────────────┐
       │         │  💚 ClickHouse (Batch) │
       │         │  Tables: kline_*      │
       │         │  - Historical data     │
       │         │  - Completed candles   │
       │         └────────────┬───────────┘
       │                      │
       │                      ▼
       │              ┌─────────────────────────┐
│              │  📊 candleSnapshot API │
│              │  - Query ClickHouse    │
│              │  - Query Redis         │
│              │  - Combine results     │
       │              └─────────────────────────┘
       │
       │
       ▼
┌─────────────────────────────────────┐
│  🟡 Persistence Scheduler           │
│  Cron: Every 1 minute               │
│  - Read completed klines from Redis │
│  - Batch insert to ClickHouse       │
│  - Cleanup Redis                    │
└─────────────────────────────────────┘
```

---

## Main Components

### **Workers:**

1. **Websocket Worker** (`cmd/worker_websocket/main.go`)
   - Connect and receive data from Hyperliquid
   - Publish to NATS

2. **NATS Consumer Worker** (`cmd/worker_nats_consumer/main.go`)
   - Consume messages from NATS
   - Process and store to Redis

3. **Scheduler** (`cmd/scheduler/main.go`)
   - Persist completed klines from Redis → ClickHouse
   - Run on cron schedule

4. **GraphQL API** (`cmd/graphql/main.go`)
   - Expose candleSnapshot endpoint
   - Query from ClickHouse + Redis

---

## Performance Optimizations

### ⚡ **Websocket Layer:**
- Zero-copy fast-path to publish raw data
- Worker pool with async publishing
- Channel buffering to avoid blocking

### ⚡ **NATS Layer:**
- Batch processing (1000 messages/batch)
- Pipeline operations
- Async acknowledgment

### ⚡ **Redis Layer:**
- Batch read/write operations
- Pipeline Redis commands
- Sorted sets for quick access
- Auto TTL cleanup

### ⚡ **ClickHouse Layer:**
- Batch insert with transactions
- Native driver optimization
- Single transaction per timeframe

### ⚡ **API Layer:**
- Combine cache (Redis) + database (ClickHouse)
- Candle patching logic
- Fallback mechanisms

---

## Data Models

### **TradeData** (Parsed from NATS)
```go
type TradeData struct {
    BatchIndex int       // Batch index from WebSocket
    Index      int       // Transaction index
    Price      float64
    Volume     float64
    Timestamp  time.Time
}
```

### **RedisKline** (Stored in Redis)
```go
type RedisKline struct {
    Timestamp   int64   // Candle start time (ms)
    Symbol      string
    Open        float64
    High        float64
    Low         float64
    Close       float64
    Volume      float64
    TradesCount int64
    Timeframe   string
    // Index tracking for ordering
    OpenIndex, CloseIndex int
    OpenBatchIndex, CloseBatchIndex int
}
```

### **Model.Kline** (Database Model)
```go
type Kline struct {
    Timestamp   time.Time
    Symbol      string
    Open        float64
    High        float64
    Low         float64
    Close       float64
    Volume      float64
    TradesCount int64
}
```

---

## Summary

✅ **Real-time Processing**: WebSocket → NATS → Redis (active candles)
✅ **Historical Storage**: Redis → ClickHouse (completed candles)
✅ **candleSnapshot**: ClickHouse (historical) + Redis (active) → Combine → Response
✅ **Performance**: Batch operations, pipeline, async processing
✅ **Reliability**: Durable consumers, retry logic, fallback mechanisms

---

## WebSocket Configuration

**Connection URL:** `wss://unstable-smart-money.the-x.link/ws`

**Configuration Parameters:**

| Parameter | Production | Local | Description |
|-----------|-----------|-------|-------------|
| Host | `wss://unstable-smart-money.the-x.link` | `127.0.0.1` | WebSocket server |
| Port | N/A (included in URL) | `8000` | WebSocket port |
| Reconnect Interval | `3s` | `5s` | Retry delay |
| Ping Interval | `15s` | `30s` | Keepalive frequency |

**Environment Variables:**
```bash
WEBSOCKET_HOST=wss://unstable-smart-money.the-x.link
WEBSOCKET_PORT=
WEBSOCKET_PATH=/ws
WEBSOCKET_RECONNECT_INTERVAL=3s
WEBSOCKET_PING_INTERVAL=15s
WEBSOCKET_PONG_TIMEOUT=5s
```

**Key Features:**
- ✅ Auto-reconnect with configurable intervals
- ✅ Ping/Pong keepalive mechanism
- ✅ Fast-path message processing (zero JSON parsing)
- ✅ Dynamic worker pool (1-5 workers, auto-scaling)
- ✅ Channel buffering (10,000 message capacity)
