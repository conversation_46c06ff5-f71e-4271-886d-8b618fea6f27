# GraphQL Code Generation Configuration for dex-hypertrader-go

schema:
  - internal/controller/graphql/*.graphqls
  - internal/controller/graphql/schemas/*.gql

exec:
  filename: internal/controller/graphql/generated.go
  package: graphql

model:
  filename: internal/controller/graphql/gql_model/models_gen.go
  package: gql_model

resolver:
  layout: follow-schema
  dir: internal/controller/graphql/
  package: graphql
  filename_template: "{name}.resolvers.go"

# directives:
#   auth:
#     implementation: AuthDirective

models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
  Time:
    model:
      - github.com/99designs/gqlgen/graphql.Time

# Skip generation for these models as we'll use custom implementations
skip_mod_tidy: true
