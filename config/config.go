package config

type Server struct {
	JWT        JWT        `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	System     System     `mapstructure:"system" json:"system" yaml:"system"`
	TiDB       TiDB       `mapstructure:"tidb" json:"tidb" yaml:"tidb"`
	ClickHouse ClickHouse `mapstructure:"clickhouse" json:"clickhouse" yaml:"clickhouse"`
	Nats       Nats       `mapstructure:"nats" json:"nats" yaml:"nats"`
	Redis      Redis      `mapstructure:"redis" json:"redis" yaml:"redis"`
	WebSocket  WebSocket  `mapstructure:"websocket" json:"websocket" yaml:"websocket"`
	Protection Protection `mapstructure:"protection" json:"protection" yaml:"protection"`
	CronTasks  []Task     `mapstructure:"cron-tasks" json:"cron-tasks" yaml:"cron-tasks"`
	Backfill   Backfill   `mapstructure:"backfill" json:"backfill" yaml:"backfill"`
	InfoAPI    InfoAPI    `mapstructure:"info-api" json:"info-api" yaml:"info-api"`
	NodeAPI    NodeAPI    `mapstructure:"node-api" json:"node-api" yaml:"node-api"`
}

type JWT struct {
	SigningKey  string `mapstructure:"signing-key" json:"signing-key" yaml:"signing-key"`
	ExpiresTime string `mapstructure:"expires-time" json:"expires-time" yaml:"expires-time"`
	BufferTime  string `mapstructure:"buffer-time" json:"buffer-time" yaml:"buffer-time"`
	Issuer      string `mapstructure:"issuer" json:"issuer" yaml:"issuer"`
}

type System struct {
	Env                string `mapstructure:"env" json:"env" yaml:"env"`
	Addr               int    `mapstructure:"addr" json:"addr" yaml:"addr"`
	RouterPrefix       string `mapstructure:"router-prefix" json:"router-prefix" yaml:"router-prefix"`
	GraphqlPrefix      string `mapstructure:"graphql-prefix" json:"graphql-prefix" yaml:"graphql-prefix"`
	AdminGraphqlPrefix string `mapstructure:"admin-graphql-prefix" json:"admin-graphql-prefix" yaml:"admin-graphql-prefix"`
}

// TiDB configuration
type TiDB struct {
	Host         string `mapstructure:"host" json:"host" yaml:"host"`
	Port         string `mapstructure:"port" json:"port" yaml:"port"`
	Config       string `mapstructure:"config" json:"config" yaml:"config"`
	Dbname       string `mapstructure:"db-name" json:"db-name" yaml:"db-name"`
	Username     string `mapstructure:"username" json:"username" yaml:"username"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"max-idle-conns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"max-open-conns" yaml:"max-open-conns"`
	MaxLifetime  string `mapstructure:"max-lifetime" json:"max-lifetime" yaml:"max-lifetime"`
}

// ClickHouse configuration for analytics/OLAP workloads
type ClickHouse struct {
	Host         string `mapstructure:"host" json:"host" yaml:"host"`
	Port         string `mapstructure:"port" json:"port" yaml:"port"`
	Database     string `mapstructure:"database" json:"database" yaml:"database"`
	Username     string `mapstructure:"username" json:"username" yaml:"username"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"max-idle-conns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"max-open-conns" yaml:"max-open-conns"`
	MaxLifetime  string `mapstructure:"max-lifetime" json:"max-lifetime" yaml:"max-lifetime"`
	Secure       bool   `mapstructure:"secure" json:"secure" yaml:"secure"`
	SkipVerify   bool   `mapstructure:"skip-verify" json:"skip-verify" yaml:"skip-verify"`
}

type Nats struct {
	URL    string `mapstructure:"url" json:"url" yaml:"url"`
	User   string `mapstructure:"user" json:"user" yaml:"user"`
	Pass   string `mapstructure:"pass" json:"pass" yaml:"pass"`
	UseTLS bool   `mapstructure:"use-tls" json:"use-tls" yaml:"use-tls"`
	Token  string `mapstructure:"token" json:"token" yaml:"token"`
}

type Task struct {
	ID   string `mapstructure:"id" json:"id" yaml:"id"`
	Cron string `mapstructure:"cron" json:"cron" yaml:"cron"`
}

// Backfill configuration for OHLCV data backfill jobs
type Backfill struct {
	Enabled bool            `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	Timeout string          `mapstructure:"timeout" json:"timeout" yaml:"timeout"`
	Jobs    []BackfillJob   `mapstructure:"jobs" json:"jobs" yaml:"jobs"`
	Timing  BackfillTiming  `mapstructure:"timing" json:"timing" yaml:"timing"`
	Proxies BackfillProxies `mapstructure:"proxies" json:"proxies" yaml:"proxies"`
}

// BackfillTiming contains all timing-related configuration for backfill operations
type BackfillTiming struct {
	// API request timing
	RetryDelay       string `mapstructure:"retry-delay" json:"retry-delay" yaml:"retry-delay"`                      // Delay between retry attempts
	RateLimitDelay   string `mapstructure:"rate-limit-delay" json:"rate-limit-delay" yaml:"rate-limit-delay"`       // Delay between API requests for rate limiting
	InterSymbolDelay string `mapstructure:"inter-symbol-delay" json:"inter-symbol-delay" yaml:"inter-symbol-delay"` // Delay between processing different symbols

	// HTTP client timeouts
	BaseTimeout                 string `mapstructure:"base-timeout" json:"base-timeout" yaml:"base-timeout"`                                                    // Base timeout for HTTP requests
	ProgressiveTimeoutIncrement string `mapstructure:"progressive-timeout-increment" json:"progressive-timeout-increment" yaml:"progressive-timeout-increment"` // Timeout increment per retry
	MaxTimeout                  string `mapstructure:"max-timeout" json:"max-timeout" yaml:"max-timeout"`                                                       // Maximum timeout for HTTP requests
	TLSHandshakeTimeout         string `mapstructure:"tls-handshake-timeout" json:"tls-handshake-timeout" yaml:"tls-handshake-timeout"`                         // TLS handshake timeout
	ResponseHeaderTimeout       string `mapstructure:"response-header-timeout" json:"response-header-timeout" yaml:"response-header-timeout"`                   // Response header timeout
	IdleConnTimeout             string `mapstructure:"idle-conn-timeout" json:"idle-conn-timeout" yaml:"idle-conn-timeout"`                                     // Idle connection timeout

	// Database operation timeouts
	DatabaseTimeout string `mapstructure:"database-timeout" json:"database-timeout" yaml:"database-timeout"` // Timeout for database operations

	// Caching
	SymbolsCacheTTL string `mapstructure:"symbols-cache-ttl" json:"symbols-cache-ttl" yaml:"symbols-cache-ttl"` // TTL for symbols cache
}

// BackfillProxies contains proxy configuration for backfill operations
type BackfillProxies struct {
	Enabled     bool                     `mapstructure:"enabled" json:"enabled" yaml:"enabled"`                // Enable/disable proxy usage
	URLs        []string                 `mapstructure:"urls" json:"urls" yaml:"urls"`                         // List of proxy URLs
	Concurrency int                      `mapstructure:"concurrency" json:"concurrency" yaml:"concurrency"`    // Max concurrent requests (should match proxy count)
	Rotation    string                   `mapstructure:"rotation" json:"rotation" yaml:"rotation"`             // Rotation strategy: "round-robin", "random"
	HealthCheck BackfillProxyHealthCheck `mapstructure:"health-check" json:"health-check" yaml:"health-check"` // Health check configuration
}

// BackfillProxyHealthCheck contains health check configuration for proxies
type BackfillProxyHealthCheck struct {
	Enabled  bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`    // Enable/disable health checks
	Interval string `mapstructure:"interval" json:"interval" yaml:"interval"` // Health check interval
	Timeout  string `mapstructure:"timeout" json:"timeout" yaml:"timeout"`    // Health check timeout
	URL      string `mapstructure:"url" json:"url" yaml:"url"`                // URL to use for health checks
}

// BackfillJob represents a single backfill job configuration
type BackfillJob struct {
	ID          string `mapstructure:"id" json:"id" yaml:"id"`
	Timeframe   string `mapstructure:"timeframe" json:"timeframe" yaml:"timeframe"`
	CandleCount int    `mapstructure:"candle-count" json:"candle-count" yaml:"candle-count"`
	Cron        string `mapstructure:"cron" json:"cron" yaml:"cron"`
	Enabled     bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
}

// Redis configuration for caching and real-time data
type Redis struct {
	Host         string `mapstructure:"host" json:"host" yaml:"host"`
	Port         string `mapstructure:"port" json:"port" yaml:"port"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	DB           int    `mapstructure:"db" json:"db" yaml:"db"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"max-idle-conns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"max-open-conns" yaml:"max-open-conns"`
	MaxLifetime  string `mapstructure:"max-lifetime" json:"max-lifetime" yaml:"max-lifetime"`
}

// WebSocket configuration for real-time trading data
type WebSocket struct {
	Host              string `mapstructure:"host" json:"host" yaml:"host"`
	Port              string `mapstructure:"port" json:"port" yaml:"port"`
	Path              string `mapstructure:"path" json:"path" yaml:"path"`
	ReconnectInterval string `mapstructure:"reconnect-interval" json:"reconnect-interval" yaml:"reconnect-interval"`
	PingInterval      string `mapstructure:"ping-interval" json:"ping-interval" yaml:"ping-interval"`
	PongTimeout       string `mapstructure:"pong-timeout" json:"pong-timeout" yaml:"pong-timeout"`
}

// Protection middleware configuration
type Protection struct {
	RateLimit      RateLimit      `mapstructure:"rateLimit" json:"rateLimit" yaml:"rateLimit"`
	Cache          Cache          `mapstructure:"cache" json:"cache" yaml:"cache"`
	CircuitBreaker CircuitBreaker `mapstructure:"circuitBreaker" json:"circuitBreaker" yaml:"circuitBreaker"`
}

type RateLimit struct {
	Enabled   bool     `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	Requests  int      `mapstructure:"requests" json:"requests" yaml:"requests"`
	Window    string   `mapstructure:"window" json:"window" yaml:"window"`
	SkipPaths []string `mapstructure:"skipPaths" json:"skipPaths" yaml:"skipPaths"`
}

type Cache struct {
	Enabled     bool     `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	TTL         string   `mapstructure:"ttl" json:"ttl" yaml:"ttl"`
	MaxSize     int      `mapstructure:"maxSize" json:"maxSize" yaml:"maxSize"`
	SkipPaths   []string `mapstructure:"skipPaths" json:"skipPaths" yaml:"skipPaths"`
	SkipMethods []string `mapstructure:"skipMethods" json:"skipMethods" yaml:"skipMethods"`
}

type CircuitBreaker struct {
	Enabled           bool    `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	MaxMemoryUsage    float64 `mapstructure:"maxMemoryUsage" json:"maxMemoryUsage" yaml:"maxMemoryUsage"`
	MaxCPUUsage       float64 `mapstructure:"maxCPUUsage" json:"maxCPUUsage" yaml:"maxCPUUsage"`
	MaxResponseTime   string  `mapstructure:"maxResponseTime" json:"maxResponseTime" yaml:"maxResponseTime"`
	CheckInterval     string  `mapstructure:"checkInterval" json:"checkInterval" yaml:"checkInterval"`
	RecoveryThreshold int     `mapstructure:"recoveryThreshold" json:"recoveryThreshold" yaml:"recoveryThreshold"`
}

// InfoAPI configuration for external API calls
type InfoAPI struct {
	URL     string `mapstructure:"url" json:"url" yaml:"url"`
	Timeout string `mapstructure:"timeout" json:"timeout" yaml:"timeout"`
}

// NodeAPI configuration for node API calls
type NodeAPI struct {
	URL     string `mapstructure:"url" json:"url" yaml:"url"`
	Timeout string `mapstructure:"timeout" json:"timeout" yaml:"timeout"`
}
