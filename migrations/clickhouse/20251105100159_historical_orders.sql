-- +goose Up
-- Create historical_orders table for storing historical order data
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS historical_orders (
    id UInt64,
    user_address Nullable(String) COMMENT '用户地址',
    coin String COMMENT '币种',
    limit_px Decimal64(8) COMMENT '限价价格',
    oid Int64 COMMENT '订单ID',
    side String COMMENT '交易方向 B/A',
    sz Decimal64(8) COMMENT '订单大小',
    timestamp Int64 COMMENT '时间戳(毫秒)',
    status String COMMENT '订单状态',
    avg_px Nullable(Decimal64(8)) COMMENT '平均成交价格',
    filled_sz Nullable(Decimal64(8)) COMMENT '已成交数量',
    trade_date String COMMENT '交易日期 YYYY-MM-DD',
    processed_at Nullable(DateTime) DEFAULT now() COMMENT '处理时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDateTime(timestamp / 1000))
ORDER BY (user_address, oid, timestamp)
SETTINGS allow_nullable_key = 1;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS historical_orders;
-- +goose StatementEnd
