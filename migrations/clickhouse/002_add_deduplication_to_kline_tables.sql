-- +goose Up
-- Add deduplication to all kline tables by converting them to ReplacingMergeTree engine
-- This prevents duplicate records based on timestamp + symbol combination

-- +goose StatementBegin
-- Create new kline_1m table with ReplacingMergeTree engine
CREATE TABLE kline_1m_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_1m_new SELECT * FROM kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1m_new TO kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_3m table with ReplacingMergeTree engine
CREATE TABLE kline_3m_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_3m_new SELECT * FROM kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_3m_new TO kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_5m table with ReplacingMergeTree engine
CREATE TABLE kline_5m_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_5m_new SELECT * FROM kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_5m_new TO kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_15m table with ReplacingMergeTree engine
CREATE TABLE kline_15m_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_15m_new SELECT * FROM kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_15m_new TO kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_30m table with ReplacingMergeTree engine
CREATE TABLE kline_30m_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_30m_new SELECT * FROM kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_30m_new TO kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_1h table with ReplacingMergeTree engine
CREATE TABLE kline_1h_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
-- Migrate data from old table to new table
INSERT INTO kline_1h_new SELECT * FROM kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Drop old table and rename new table
DROP TABLE kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1h_new TO kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_2h table with ReplacingMergeTree engine
CREATE TABLE kline_2h_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_2h_new SELECT * FROM kline_2h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_2h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_2h_new TO kline_2h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_4h table with ReplacingMergeTree engine
CREATE TABLE kline_4h_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_4h_new SELECT * FROM kline_4h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_4h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_4h_new TO kline_4h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_8h table with ReplacingMergeTree engine
CREATE TABLE kline_8h_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_8h_new SELECT * FROM kline_8h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_8h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_8h_new TO kline_8h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_12h table with ReplacingMergeTree engine
CREATE TABLE kline_12h_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_12h_new SELECT * FROM kline_12h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_12h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_12h_new TO kline_12h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_1d table with ReplacingMergeTree engine
CREATE TABLE kline_1d_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1d_new SELECT * FROM kline_1d;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_1d;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1d_new TO kline_1d;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_3d table with ReplacingMergeTree engine
CREATE TABLE kline_3d_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_3d_new SELECT * FROM kline_3d;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_3d;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_3d_new TO kline_3d;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_1w table with ReplacingMergeTree engine
CREATE TABLE kline_1w_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1w_new SELECT * FROM kline_1w;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_1w;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1w_new TO kline_1w;
-- +goose StatementEnd

-- +goose StatementBegin
-- Create new kline_1mo table with ReplacingMergeTree engine
CREATE TABLE kline_1mo_new (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = ReplacingMergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1mo_new SELECT * FROM kline_1mo;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_1mo;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1mo_new TO kline_1mo;
-- +goose StatementEnd

-- +goose Down
-- Rollback deduplication by converting all kline tables back to MergeTree engine
-- WARNING: This will lose deduplication functionality

-- +goose StatementBegin
-- Rollback kline_1m table to MergeTree engine
CREATE TABLE kline_1m_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1m_rollback SELECT * FROM kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1m_rollback TO kline_1m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback kline_3m table to MergeTree engine
CREATE TABLE kline_3m_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_3m_rollback SELECT * FROM kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_3m_rollback TO kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback kline_5m table to MergeTree engine
CREATE TABLE kline_5m_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_5m_rollback SELECT * FROM kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_5m_rollback TO kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback kline_15m table to MergeTree engine
CREATE TABLE kline_15m_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_15m_rollback SELECT * FROM kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_15m_rollback TO kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback kline_30m table to MergeTree engine
CREATE TABLE kline_30m_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_30m_rollback SELECT * FROM kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_30m_rollback TO kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback kline_1h table to MergeTree engine
CREATE TABLE kline_1h_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1h_rollback SELECT * FROM kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
RENAME TABLE kline_1h_rollback TO kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
-- Rollback remaining tables (2h, 4h, 8h, 12h, 1d, 3d, 1w, 1mo)
CREATE TABLE kline_2h_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_2h_rollback SELECT * FROM kline_2h;
DROP TABLE kline_2h;
RENAME TABLE kline_2h_rollback TO kline_2h;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_4h_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_4h_rollback SELECT * FROM kline_4h;
DROP TABLE kline_4h;
RENAME TABLE kline_4h_rollback TO kline_4h;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_8h_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_8h_rollback SELECT * FROM kline_8h;
DROP TABLE kline_8h;
RENAME TABLE kline_8h_rollback TO kline_8h;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_12h_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_12h_rollback SELECT * FROM kline_12h;
DROP TABLE kline_12h;
RENAME TABLE kline_12h_rollback TO kline_12h;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_1d_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1d_rollback SELECT * FROM kline_1d;
DROP TABLE kline_1d;
RENAME TABLE kline_1d_rollback TO kline_1d;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_3d_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_3d_rollback SELECT * FROM kline_3d;
DROP TABLE kline_3d;
RENAME TABLE kline_3d_rollback TO kline_3d;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_1w_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1w_rollback SELECT * FROM kline_1w;
DROP TABLE kline_1w;
RENAME TABLE kline_1w_rollback TO kline_1w;
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE kline_1mo_rollback (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO kline_1mo_rollback SELECT * FROM kline_1mo;
DROP TABLE kline_1mo;
RENAME TABLE kline_1mo_rollback TO kline_1mo;
-- +goose StatementEnd
