-- +goose Up
-- Create users table for storing user information
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS users (
    id String,
    email Nullable(String),
    name String,
    status String,
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now(),
    deleted_at Nullable(DateTime)
) ENGINE = MergeTree()
ORDER BY (id, created_at);
-- +goose StatementEnd

-- +goose Down
-- Drop users table
-- +goose StatementBegin
DROP TABLE IF EXISTS users;
-- +goose StatementEnd
