-- +goose Up
-- Create node_fills table for storing fill/trade data
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS node_fills (
    id UInt64,
    user_address String,
    hash String,
    tid Int64,
    coin String,
    px Decimal64(8),
    sz Decimal64(8),
    side String COMMENT '交易方向 B/S',
    time Int64 COMMENT '时间戳(毫秒)',
    start_position Decimal128(18) COMMENT '起始仓位',
    direction String COMMENT '操作方向',
    closed_pnl Decimal128(18) COMMENT '已实现盈亏',
    oid Int64 COMMENT '订单ID',
    crossed UInt8 COMMENT '是否跨价格',
    fee Decimal128(18) COMMENT '手续费',
    fee_token String COMMENT '手续费币种',
    trade_date String COMMENT '交易日期 YYYY-MM-DD',
    trade_type String COMMENT '交易类型: perpetual/spot',
    processed_at DateTime DEFAULT now() COMMENT '处理时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDateTime(time / 1000))
ORDER BY (user_address, hash, tid, time);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS node_fills;
-- +goose StatementEnd
