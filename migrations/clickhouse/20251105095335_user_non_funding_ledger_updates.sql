-- +goose Up
-- +goose StatementBegin
CREATE TABLE default.user_non_funding_ledger_updates
(
    `arbi_hash` String,
    `time` DateTime64(3, 'UTC'),
    `addr` String,
    `type` Enum8('deposit' = 1, 'withdrawal' = 2),
    `usd` Decimal(20, 6),
    `fee` Decimal(20, 6)
)
    ENGINE = ReplacingMergeTree
ORDER BY (addr, time, arbi_hash);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS user_non_funding_ledger_updates;
-- +goose StatementEnd
