# ClickHouse Migrations

This directory contains SQL migration files for ClickHouse database schema management using <PERSON>.

## Important Notes

Currently, <PERSON> does not support creating migration files for ClickHouse from models. Therefore, all migration files in this directory have been MANUALLY created.

## Directory Structure

```
migrations/clickhouse/
├── README.md                    # This file
├── 001_initial_kline_tables.sql # Initial kline tables creation
├── 002_add_indexes.sql          # Additional indexes (example)
└── ...                          # Future migrations
```

## Migration File Naming Convention

Migration files follow the pattern: `{version}_{description}.sql`

- **Version**: 3-digit zero-padded number (001, 002, 003, etc.)
- **Description**: Snake_case description of the migration
- **Extension**: Always `.sql`

## Migration File Format

Each migration file must contain both `-- +goose Up` and `-- +goose Down` sections:

```sql
-- +goose Up
-- +goose StatementBegin
CREATE TABLE example_table (
    id UInt64,
    name String
) ENGINE = MergeTree()
ORDER BY id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS example_table;
-- +goose StatementEnd
```

## Usage

See the main project Makefile for migration commands:

- `make clickhouse-migrate-up` - Apply all pending migrations
- `make clickhouse-migrate-down` - Rollback the last migration
- `make clickhouse-migrate-status` - Check migration status
- `make clickhouse-migrate-create NAME=migration_name` - Create a new migration file

## Best Practices

1. **Always test migrations** in a development environment first
2. **Keep migrations small** and focused on a single change
3. **Write reversible migrations** with proper Down sections
4. **Use IF NOT EXISTS** for CREATE statements when appropriate
5. **Use IF EXISTS** for DROP statements in Down sections
6. **Test rollback scenarios** to ensure Down migrations work correctly

## ClickHouse Specific Considerations

- Use appropriate engines (MergeTree, AggregatingMergeTree, etc.)
- Consider partitioning strategy for large tables
- Use proper data types (DateTime64(3) for timestamps, Decimal(36,18) for currency)
- Apply compression codecs (ZSTD, Delta) for better storage efficiency
- Set appropriate ORDER BY clauses for query optimization
