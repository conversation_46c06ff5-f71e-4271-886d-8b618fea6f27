-- +goose Up
-- Create all kline tables for different timeframes
-- These tables store OHLCV (Open, High, Low, Close, Volume) candlestick data

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_1m (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_3m (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_5m (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_15m (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_30m (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_1h (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_2h (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_4h (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_8h (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_12h (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_1d (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_3d (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_1w (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS kline_1mo (
    timestamp DateTime,
    symbol String,
    open Float64,
    high Float64,
    low Float64,
    close Float64,
    volume Float64,
    trades_count Int64
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (symbol, timestamp);
-- +goose StatementEnd

-- +goose Down
-- Drop all kline tables in reverse order

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_1mo;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_1w;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_3d;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_1d;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_12h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_8h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_4h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_2h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_1h;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_30m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_15m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_5m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_3m;
-- +goose StatementEnd

-- +goose StatementBegin
DROP TABLE IF EXISTS kline_1m;
-- +goose StatementEnd
