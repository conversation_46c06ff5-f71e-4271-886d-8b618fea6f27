package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader WebSocket worker...")

	// Initialize databases (including Redis)
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Verify Redis connection
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Fatal("Redis connection is required for WebSocket worker")
	}

	// Create WebSocket service (now publishes to NATS instead of direct processing)
	wsService := service.NewWebSocketService()

	// Start worker
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start WebSocket service in a goroutine
	go func() {
		if err := wsService.Start(ctx); err != nil {
			global.GVA_LOG.Error("WebSocket service stopped with error", zap.Error(err))
		}
	}()

	global.GVA_LOG.Info("WebSocket worker started successfully")

	// Wait for shutdown signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down WebSocket worker...")

	// Stop WebSocket service
	wsService.Stop()

	// Cancel context to stop all goroutines
	cancel()

	global.GVA_LOG.Info("WebSocket worker exited")
}
