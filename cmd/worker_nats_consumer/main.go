package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader NATS consumer worker...")

	// Initialize databases (including Redis and NATS)
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Verify required connections
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Fatal("Redis connection is required for NATS consumer worker")
	}

	if global.GVA_NATS == nil {
		global.GVA_LOG.Fatal("NATS connection is required for NATS consumer worker")
	}

	// Create NATS consumer service
	consumerService := service.NewNATSConsumerService()

	// Configure batch processing for optimal performance
	consumerService.SetBatchSize(1000) // Process 1000 messages at a time
	consumerService.SetMaxWait(500)    // Wait max 500ms for batch

	// Start worker
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start NATS message processing
	if err := consumerService.StartProcessing(ctx); err != nil {
		global.GVA_LOG.Fatal("Failed to start NATS consumer", zap.Error(err))
	}

	global.GVA_LOG.Info("NATS consumer worker started successfully - processing user fills from NATS")

	// Wait for shutdown signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down NATS consumer worker...")

	// Cancel context to stop all goroutines
	cancel()

	// Stop the consumer
	consumerService.Stop()

	// Give additional time for durable consumer state to persist
	time.Sleep(200 * time.Millisecond)

	global.GVA_LOG.Info("NATS consumer worker exited")
}
