package main

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
	"go.uber.org/zap"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting real-time trading system test...")

	// Initialize databases (including Redis)
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Verify Redis connection
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Fatal("Redis connection is required for this test")
	}

	// Test Redis connection
	ctx := context.Background()
	if err := global.GVA_REDIS.Ping(ctx).Err(); err != nil {
		global.GVA_LOG.Fatal("Failed to ping Redis: " + err.Error())
	}

	fmt.Println("✅ Redis connection successful")

	// Test NATS connection
	if global.GVA_NATS == nil {
		global.GVA_LOG.Fatal("NATS connection is required for testing")
	}
	fmt.Println("✅ NATS connection successful")

	// Initialize services for NATS-based architecture
	natsPublisher := service.NewNATSPublisherService()
	consumerService := service.NewNATSConsumerService()

	// Configure for testing
	consumerService.SetBatchSize(10) // Small batch for testing
	consumerService.SetMaxWait(100)  // Fast processing for testing

	// Start NATS consumer in background
	consumerCtx, consumerCancel := context.WithCancel(ctx)
	defer consumerCancel()

	if err := consumerService.StartProcessing(consumerCtx); err != nil {
		global.GVA_LOG.Fatal("Failed to start NATS consumer", zap.Error(err))
	}

	fmt.Println("✅ NATS consumer started")

	// Create test raw WebSocket message data (for optimized publishing)
	testMessage := map[string]interface{}{
		"type":          "user_fill",
		"data":          []interface{}{1, "TEST", "100.50", "10.0", float64(time.Now().UnixMilli()), "0xtest"},
		"timestamp":     time.Now().Format(time.RFC3339),
		"batch_index":   0,
		"message_index": 0,
	}

	// Convert to JSON bytes (simulating raw WebSocket data)
	rawData, err := json.Marshal(testMessage)
	if err != nil {
		global.GVA_LOG.Fatal("Failed to marshal test message: " + err.Error())
	}

	// Publish optimized data to NATS (extracts only the data array)
	if err := natsPublisher.PublishOptimizedUserFill(ctx, rawData); err != nil {
		global.GVA_LOG.Fatal("Failed to publish test optimized user fill to NATS: " + err.Error())
	}

	fmt.Println("✅ Test raw user fill published to NATS successfully")

	// Wait a moment for NATS consumer to process the message
	time.Sleep(2 * time.Second)

	// Initialize realtime service to check results
	realtimeService := service.NewRealtimeKlineService()

	// Check if klines were created in Redis
	klines, err := realtimeService.GetRealtimeKlines(ctx, "TEST", model.Timeframe1m, 5)
	if err != nil {
		global.GVA_LOG.Fatal("Failed to get klines from Redis: " + err.Error())
	}

	if len(klines) == 0 {
		fmt.Println("❌ No klines found in Redis")
	} else {
		fmt.Printf("✅ Found %d klines in Redis for TEST 1m timeframe\n", len(klines))
		for i, kline := range klines {
			fmt.Printf("  Kline %d: Open=%.2f, High=%.2f, Low=%.2f, Close=%.2f, Volume=%.2f\n",
				i+1, kline.Open, kline.High, kline.Low, kline.Close, kline.Volume)
		}
	}

	// Test statistics
	stats, err := realtimeService.GetKlineStatistics(ctx)
	if err != nil {
		global.GVA_LOG.Fatal("Failed to get statistics: " + err.Error())
	}

	fmt.Printf("✅ Statistics: %+v\n", stats)

	// Test persistence service
	if global.GVA_DB_CLICKHOUSE != nil {
		fmt.Println("✅ ClickHouse connection available")

		persistenceService := service.NewKlinePersistenceService()
		persistStats, err := persistenceService.GetPersistenceStatistics(ctx)
		if err != nil {
			fmt.Printf("⚠️  Failed to get persistence statistics: %v\n", err)
		} else {
			fmt.Printf("✅ Persistence statistics: %+v\n", persistStats)
		}

		// Test the persistence task execution
		fmt.Println("🔄 Testing persistence task execution...")
		persistenceService.ExecutePersistenceTask()
		fmt.Println("✅ Persistence task executed successfully")
	} else {
		fmt.Println("⚠️  ClickHouse connection not available")
	}

	fmt.Println("\n🎉 Real-time trading system test completed successfully!")
	fmt.Println("\nTo run the full system:")
	fmt.Println("1. Start WebSocket worker: make run-websocket-worker")
	fmt.Println("2. Start scheduler (includes persistence): make run-scheduler")
}
