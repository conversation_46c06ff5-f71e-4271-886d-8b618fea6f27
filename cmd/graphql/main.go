package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/controller/graphql"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/middleware"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/migration"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/repo"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting dex-hypertrader GraphQL server...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Initialize services
	infoService := service.NewInfoService()

	// Inject OHLC repository into InfoService
	ohlcRepo := repo.NewOHLCRepository()
	infoService.SetOHLCRepository(ohlcRepo)

	// Initialize protection middleware
	protectionConfig := middleware.ParseProtectionConfig(global.GVA_CONFIG.Protection)
	protectionMiddleware := middleware.NewProtectionMiddleware(protectionConfig)

	// Run ClickHouse migrations if ClickHouse is available
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_LOG.Info("Running ClickHouse migrations on GraphQL server startup...")
		if err := migration.RunClickHouseMigrations(global.GVA_DB_CLICKHOUSE); err != nil {
			global.GVA_LOG.Fatal("Failed to run ClickHouse migrations on startup", zap.Error(err))
		}
		global.GVA_LOG.Info("ClickHouse migrations completed successfully")
	}

	// Set Gin mode based on environment
	switch global.GVA_CONFIG.System.Env {
	case "local", "dev", "development", "unstable":
		gin.SetMode(gin.DebugMode)
	case "test":
		gin.SetMode(gin.TestMode)
	case "docker", "staging", "production":
		gin.SetMode(gin.ReleaseMode)
	default:
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Temporarily disable protection middleware for debugging
	router.Use(protectionMiddleware.Middleware())

	// Configure CORS with GraphQL-specific headers
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{
		"Origin",
		"Content-Length",
		"Content-Type",
		"Authorization",
		"Accept",
		"X-Requested-With",
		"Apollo-Require-Preflight", // Apollo GraphQL client
		"X-Apollo-Operation-Name",  // Apollo GraphQL client
		"X-Apollo-Operation-Type",  // Apollo GraphQL client
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	config.ExposeHeaders = []string{"Content-Length", "Content-Type"}
	config.AllowCredentials = true
	router.Use(cors.New(config))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "dex-hypertrader",
			"timestamp": time.Now().UTC(),
			"databases": gin.H{
				"clickhouse": global.GVA_DB_CLICKHOUSE != nil,
				"tidb":       global.GVA_DB_TIDB != nil,
			},
		})
	})

	// API routes group
	api := router.Group(global.GVA_CONFIG.System.RouterPrefix)
	{
		api.GET("/ping", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
				"time":    time.Now().UTC(),
			})
		})

		// Generic information endpoint, supporting multiple request types
		api.POST("/info", infoService.HandleInfoRequest)

		// GraphQL endpoints
		gqlGroup := api.Group("/graphql")
		{
			// Create GraphQL resolver
			resolver := graphql.NewRootResolver()

			// Create GraphQL server with introspection configuration
			schema := graphql.NewExecutableSchema(graphql.Config{
				Resolvers: resolver,
			})

			// Create server with proper transport configuration
			srv := handler.New(schema)

			// Add transports
			srv.AddTransport(transport.Options{})
			srv.AddTransport(transport.GET{})
			srv.AddTransport(transport.POST{})

			// Configure introspection based on environment
			// Enable introspection for development and staging, disable for production
			if global.GVA_CONFIG.System.Env != "production" && global.GVA_CONFIG.System.Env != "prod" {
				srv.Use(extension.Introspection{})
				global.GVA_LOG.Info("GraphQL introspection enabled", zap.String("environment", global.GVA_CONFIG.System.Env))
			} else {
				global.GVA_LOG.Info("GraphQL introspection disabled for production environment")
			}

			// GraphQL endpoint
			gqlGroup.POST("/", gin.WrapH(srv))

			// GraphQL Playground (enabled for development and staging environments)
			if global.GVA_CONFIG.System.Env == "local" ||
				global.GVA_CONFIG.System.Env == "dev" ||
				global.GVA_CONFIG.System.Env == "development" ||
				global.GVA_CONFIG.System.Env == "unstable" ||
				global.GVA_CONFIG.System.Env == "staging" {
				gqlGroup.GET("/", gin.WrapH(playground.Handler("GraphQL Playground", "/api/dex-hypertrader/graphql/")))
				global.GVA_LOG.Info("GraphQL Playground enabled", zap.String("environment", global.GVA_CONFIG.System.Env))
			}
		}
	}

	// Create HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		global.GVA_LOG.Info(fmt.Sprintf("Server starting on port %d", global.GVA_CONFIG.System.Addr))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			global.GVA_LOG.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		global.GVA_LOG.Error("Server forced to shutdown", zap.Error(err))
	}

	global.GVA_LOG.Info("Server exited")
}
