package main

import (
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	natsClient "github.com/xbit-dex/xbit-hypertrader-go/internal/nats"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/task"
	"go.uber.org/zap"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader NATS consumer worker...")

	// Initialize databases (including Redis and NATS)
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Verify required connections
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Fatal("Redis connection is required for NATS consumer worker")
	}

	if global.GVA_NATS == nil {
		global.GVA_LOG.Fatal("NATS connection is required for NATS consumer worker")
	}

	natsClientInstance := global.GVA_NATS.(*natsClient.NATSClient)
	if err := natsClientInstance.EnsureReadonlyStreamExists(task.DexUserStream); err != nil {
		panic(fmt.Sprintf("Failed to ensure readonly stream %s exists: %v", task.DexUserStream, err))
	}

	// if err := runSyncUserNatsWorker(); err != nil {
	// 	global.GVA_LOG.Fatal("Failed to start sync user worker", zap.Error(err))
	// }

	// Block forever
	// select {}
}

func runSyncUserNatsWorker() error {
	logger := global.GVA_LOG

	natsClientInstance := global.GVA_NATS.(*natsClient.NATSClient)

	subjects := map[string]func(msg *nats.Msg) error{
		string(task.UserNewWalletSubject): task.ConsumeUserSyncInfoEvent,
	}

	subject := "dex.user.>"
	opts := []nats.SubOpt{
		nats.BindStream(task.DexUserStream),
		nats.Durable(task.AggregationSyncUserConsumer),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
		nats.DeliverAll(),
	}
	_, err := natsClientInstance.SubscribeJS(subject, func(msg *nats.Msg) {
		go func() {
			start := time.Now()
			defer func() {
				elapsed := time.Since(start)
				logger.Info("Worker process completed: ", zap.String("Subject", msg.Subject), zap.Duration("duration", elapsed))
			}()

			handler, ok := subjects[msg.Subject]
			if !ok || handler == nil {
				logger.Error("failed: Worker handler not found:", zap.String("Subject", msg.Subject))
				msg.Ack()
				return
			}

			err := handler(msg)
			if err != nil {
				logger.Error("Worker Consume subject failed:", zap.String("Subject", msg.Subject), zap.Error(err), zap.Any("Data", msg.Data))
				msg.Nak()
			} else {
				msg.Ack()
			}
		}()
	}, opts...)
	if err != nil {
		logger.Error("Could not subscribe to subject:", zap.String("Subject", subject), zap.Error(err))
		return err
	}
	logger.Info("Worker started, listening on JetStream subjects.")
	return nil
}
