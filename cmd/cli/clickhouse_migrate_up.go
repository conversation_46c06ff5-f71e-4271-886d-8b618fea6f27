package main

import (
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/migration"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()
	if global.GVA_VP == nil {
		panic("Failed to initialize Viper configuration")
	}

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Running ClickHouse migrations up...")

	// Initialize only ClickHouse database
	global.GVA_DB_CLICKHOUSE = initializer.InitClickHouse()
	defer func() {
		if global.GVA_DB_CLICKHOUSE != nil {
			if err := global.GVA_DB_CLICKHOUSE.Close(); err != nil {
				global.GVA_LOG.Error("Failed to close ClickHouse connection: " + err.Error())
			} else {
				global.GVA_LOG.Info("ClickHouse connection closed successfully")
			}
		}
	}()

	// Run ClickHouse migrations
	if global.GVA_DB_CLICKHOUSE != nil {
		if err := migration.RunClickHouseMigrations(global.GVA_DB_CLICKHOUSE); err != nil {
			global.GVA_LOG.Fatal("Failed to run ClickHouse migrations: " + err.Error())
		}
		global.GVA_LOG.Info("ClickHouse migrations up completed successfully")
	} else {
		global.GVA_LOG.Fatal("ClickHouse connection is nil")
	}
}
