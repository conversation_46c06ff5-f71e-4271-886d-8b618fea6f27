package main

import (
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/migration"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Creating ClickHouse tables...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Create ClickHouse tables using migration system
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_LOG.Info("Creating ClickHouse tables using migration system...")

		if err := migration.RunClickHouseMigrations(global.GVA_DB_CLICKHOUSE); err != nil {
			global.GVA_LOG.Fatal("Failed to run ClickHouse migrations", zap.Error(err))
		}

		global.GVA_LOG.Info("ClickHouse tables created successfully")
	} else {
		global.GVA_LOG.Fatal("ClickHouse connection is required for table creation")
	}

	global.GVA_LOG.Info("All database tables created successfully")
}
