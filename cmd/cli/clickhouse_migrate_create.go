package main

import (
	"fmt"
	"os"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/migration"
)

func main() {
	// Check if migration name is provided
	if len(os.Args) < 2 {
		fmt.Println("Usage: clickhouse_migrate_create <migration_name>")
		fmt.Println("Example: clickhouse_migrate_create add_indexes")
		os.Exit(1)
	}

	migrationName := os.Args[1]

	// Initialize configuration
	global.GVA_VP = initializer.Viper()
	if global.GVA_VP == nil {
		panic("Failed to initialize Viper configuration")
	}

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info(fmt.Sprintf("Creating new ClickHouse migration: %s", migrationName))

	// Create new migration file
	if err := migration.CreateClickHouseMigration(migrationName); err != nil {
		global.GVA_LOG.Fatal("Failed to create ClickHouse migration: " + err.Error())
	}

	global.GVA_LOG.Info("ClickHouse migration created successfully")
}
