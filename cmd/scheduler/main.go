package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/migration"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service/data"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/task"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader scheduler...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Run ClickHouse migrations if ClickHouse is available
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_LOG.Info("Running ClickHouse migrations on scheduler startup...")
		if err := migration.RunClickHouseMigrations(global.GVA_DB_CLICKHOUSE); err != nil {
			global.GVA_LOG.Fatal("Failed to run ClickHouse migrations on startup", zap.Error(err))
		}
		global.GVA_LOG.Info("ClickHouse migrations completed successfully")
	}

	// Create task scheduler
	scheduler := task.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Register scheduled tasks
	registerTasks(scheduler)

	// Start scheduler in a goroutine
	go scheduler.RunWithSignal(ctx)

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down scheduler...")
	cancel()

	// Stop shared proxy manager to prevent goroutine leaks
	service.StopSharedProxyManager()

	global.GVA_LOG.Info("Scheduler exited")
}

func registerTasks(scheduler *task.TaskScheduler) {
	// Initialize persistence service for kline persistence task
	var persistenceService *service.KlinePersistenceService

	// Initialize backfill job service
	var backfillJobService *service.BackfillJobService

	// Register your scheduled tasks here
	for _, taskConfig := range global.GVA_CONFIG.CronTasks {
		switch taskConfig.ID {
		// market_data_sync and analytics_aggregation tasks removed as tables are not needed

		case "persist_klines":
			// Initialize persistence service if not already done
			if persistenceService == nil {
				if global.GVA_DB_CLICKHOUSE == nil || global.GVA_REDIS == nil {
					global.GVA_LOG.Error("ClickHouse and Redis connections required for kline persistence task")
					continue
				}
				persistenceService = service.NewKlinePersistenceService()
			}

			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Debug("Executing kline persistence task")
				persistenceService.ExecutePersistenceTask()
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register persist_klines task", zap.Error(err))
			}

		case "user_fills_sync":
			if global.GVA_DB_TIDB == nil || global.GVA_REDIS == nil {
				global.GVA_LOG.Error("TiDB and Redis connectivity are required; the user_fills_sync task cannot be registered.")
				continue
			}
			userFillsService := data.NewUserFillsSyncService()
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Debug("Executing user fills sync task")
				userFillsService.ExecuteSync()
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register user_fills_sync task", zap.Error(err))
			} else {
				global.GVA_LOG.Info("Running user_fills_sync once for testing...")
				go func() {
					time.Sleep(2 * time.Second)
					userFillsService.ExecuteSync()
				}()
			}

		case "historical_orders_sync":
			if global.GVA_DB_TIDB == nil || global.GVA_REDIS == nil {
				global.GVA_LOG.Error("TiDB and Redis connectivity are required; the historical_orders_sync task cannot be registered.")
				continue
			}
			historicalOrdersService := data.NewHistoricalOrdersSyncService()
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Debug("Executing historical orders sync task")
				historicalOrdersService.ExecuteSync()
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register historical_orders_sync task", zap.Error(err))
			} else {
				global.GVA_LOG.Info("Running historical_orders_sync once for testing...")
				go func() {
					time.Sleep(2 * time.Second)
					historicalOrdersService.ExecuteSync()
				}()
			}

		case "tidb_to_clickhouse_sync":
			if global.GVA_DB_TIDB == nil || global.GVA_DB_CLICKHOUSE == nil {
				global.GVA_LOG.Error("TiDB and ClickHouse connections are required; the tidb_to_clickhouse_sync task cannot be registered.")
				continue
			}
			syncService := data.NewTiDBToClickHouseSyncService()
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Debug("Executing TiDB to ClickHouse sync task")
				syncService.ExecuteSync()
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register tidb_to_clickhouse_sync task", zap.Error(err))
			} else {
				global.GVA_LOG.Info("Running tidb_to_clickhouse_sync once for testing...")
				go func() {
					time.Sleep(2 * time.Second)
					syncService.ExecuteSync()
				}()
			}
		case "user_non_funding_sync":
			if global.GVA_DB_TIDB == nil || global.GVA_DB_CLICKHOUSE == nil {
				global.GVA_LOG.Error("TiDB and ClickHouse connectivity are required; the user_non_funding_sync task cannot be registered.")
				continue
			}
			// 注意：这里使用空地址列表表示同步所有地址
			// 如果需要同步特定地址，可以从配置中读取
			syncTask := data.NewUserNonFundingSyncTask(
				nil,           // 空地址列表 = 同步所有地址
				5*time.Minute, // 5分钟重叠窗口
				10000,         // 批量大小
			)
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Debug("Executing user non-funding ledger sync task")
				syncTask.ExecuteSync()
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register user_non_funding_sync task", zap.Error(err))
			}

		default:
			global.GVA_LOG.Warn("Unknown task ID", zap.String("task_id", taskConfig.ID))
		}
	}

	// Register backfill jobs
	if global.GVA_CONFIG.Backfill.Enabled {
		// Initialize backfill job service if not already done
		if backfillJobService == nil {
			if global.GVA_DB_CLICKHOUSE == nil {
				global.GVA_LOG.Error("ClickHouse connection required for backfill jobs")
				return
			}
			backfillJobService = service.NewBackfillJobService()

			// Validate configuration
			if err := backfillJobService.ValidateConfiguration(); err != nil {
				global.GVA_LOG.Error("Backfill configuration validation failed", zap.Error(err))
				return
			}
		}

		// Register each enabled backfill job
		enabledJobs := backfillJobService.GetEnabledJobs()
		global.GVA_LOG.Info("Registering backfill jobs",
			zap.Int("enabled_jobs", len(enabledJobs)),
			zap.Int("total_jobs", len(global.GVA_CONFIG.Backfill.Jobs)))

		for _, job := range enabledJobs {
			jobExecutor := backfillJobService.CreateJobExecutor(job.ID)

			err := scheduler.Register(job.ID, job.Cron, jobExecutor)
			if err != nil {
				global.GVA_LOG.Error("Failed to register backfill job",
					zap.String("job_id", job.ID),
					zap.String("timeframe", job.Timeframe),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("Registered backfill job",
					zap.String("job_id", job.ID),
					zap.String("timeframe", job.Timeframe),
					zap.String("cron", job.Cron),
					zap.Int("candle_count", job.CandleCount))
			}
		}

		// Log backfill statistics
		stats := backfillJobService.GetJobStatistics()
		global.GVA_LOG.Info("Backfill job registration completed", zap.Any("statistics", stats))

		// Register periodic cleanup job to prevent memory leaks
		err := scheduler.Register("backfill_cleanup", "0 */30 * * * *", func() { // Every 30 minutes
			global.GVA_LOG.Debug("Executing backfill metrics cleanup")
			backfillJobService.PerformPeriodicCleanup()
		})
		if err != nil {
			global.GVA_LOG.Error("Failed to register backfill cleanup job", zap.Error(err))
		} else {
			global.GVA_LOG.Info("Registered backfill cleanup job")
		}
	} else {
		global.GVA_LOG.Info("Backfill jobs are disabled")
	}
}
