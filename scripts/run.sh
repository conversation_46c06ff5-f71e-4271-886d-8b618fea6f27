#!/bin/bash
set -e

# Load environment variables
source scripts/load-env.sh $1

case "$2" in
    "create-tables")
        echo "Creating all database tables..."
        go run cmd/cli/create_tables.go
        ;;
    "create-clickhouse-tables")
        echo "Creating ClickHouse tables only..."
        go run cmd/cli/create_clickhouse_tables.go
        ;;
    "run")
        echo "Starting GraphQL server..."
        go run cmd/graphql/main.go
        ;;
    "scheduler")
        echo "Starting scheduler..."
        go run cmd/scheduler/main.go
        ;;
    "websocket-worker")
        echo "Starting WebSocket worker..."
        go run cmd/worker_websocket/main.go
        ;;
    "nats-consumer-worker")
        echo "Starting NATS consumer worker..."
        go run cmd/worker_nats_consumer/main.go
        ;;
    "test-realtime")
        echo "Running real-time system test..."
        go run cmd/test_realtime/main.go
        ;;
    "dev")
        echo "Starting development server with hot reload..."
        # Install air if not present
        if ! command -v air &> /dev/null; then
            echo "Installing air for hot reload..."
            go install github.com/cosmtrek/air@latest
        fi
        air -c .air.toml
        ;;
    *)
        echo "Usage: $0 <env> <command>"
        echo "Commands: create-tables, create-clickhouse-tables, run, scheduler, websocket-worker, nats-consumer-worker, dev"
        exit 1
        ;;
esac
